import * as vscode from 'vscode';
import * as fs from 'fs';
import { batchReplacementFile, getDirectoryTree, getFileContent, modifyFileTreeNode } from '../utils/file';
import FileTreeDataProvider from '../tree/fileTreeDataProvider';
import { Logger } from '@joycoder/shared';
import _ from 'lodash';
import { CodeTranslateChatType } from '@joycoder/plugin-base-ai/src/dialog/constant';
import CodeTranslateLanguage from './codeTranslateLanguage';
const filesTreeView: vscode.Disposable | null = null;
let fileTreeDataProvider: FileTreeDataProvider | null = null;
let Vue2Disposable: vscode.Disposable = new vscode.Disposable(() => {});
let Vue3Disposable: vscode.Disposable = new vscode.Disposable(() => {});
let reactDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let taroDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let taroVueDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let arkDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let flutterDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let javaDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let treeItemDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let cancelDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let replaceAllDisposable: vscode.Disposable = new vscode.Disposable(() => {});

export default function (context: vscode.ExtensionContext) {
  // 批量转VUE2
  Vue2Disposable && Vue2Disposable.dispose();
  Vue2Disposable = vscode.commands.registerCommand('JoyCoder.ai.transformCodeVue2', async (...file) => {
    try {
      init(file, CodeTranslateChatType['Vue2.x']);
    } catch (error) {}
  });
  // 批量转VUE3
  Vue3Disposable && Vue3Disposable.dispose();
  Vue3Disposable = vscode.commands.registerCommand('JoyCoder.ai.transformCodeVue3TS', async (...file) => {
    try {
      init(file, CodeTranslateChatType['Vue3.x']);
    } catch (error) {}
  });
  // 批量转React
  reactDisposable && reactDisposable.dispose();
  reactDisposable = vscode.commands.registerCommand('JoyCoder.ai.transformCodeReactTS', async (...file) => {
    try {
      init(file, CodeTranslateChatType['React-JS']);
    } catch (error) {}
  });
  // 批量转Taro
  taroDisposable && taroDisposable.dispose();
  taroDisposable = vscode.commands.registerCommand('JoyCoder.ai.transformCodeTaroReactTS', async (...file) => {
    try {
      init(file, CodeTranslateChatType['Taro-React-TS']);
    } catch (error) {}
  });
  // 批量转Taro-Vue
  taroVueDisposable && taroVueDisposable.dispose();
  taroVueDisposable = vscode.commands.registerCommand('JoyCoder.ai.transformCodeTaroVueTS', async (...file) => {
    try {
      init(file, CodeTranslateChatType['Taro-Vue-TS']);
    } catch (error) {}
  });
  // 批量转鸿蒙
  arkDisposable && arkDisposable.dispose();
  arkDisposable = vscode.commands.registerCommand('JoyCoder.ai.transformCodeHarmonyArkTS', async (...file) => {
    try {
      init(file, CodeTranslateChatType['Harmony-ArkTS']);
    } catch (error) {
      Logger.warn('%c [ error ]-64', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  });
  // 批量转Flutter3
  flutterDisposable && flutterDisposable.dispose();
  flutterDisposable = vscode.commands.registerCommand('JoyCoder.ai.transformCodeFlutter3', async (...file) => {
    try {
      init(file, CodeTranslateChatType['Flutter3']);
    } catch (error) {
      Logger.warn('%c [ error ]-64', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  });
  // 批量转Java
  javaDisposable && flutterDisposable.dispose();
  javaDisposable = vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeJava', async (...file) => {
    try {
      init(file, CodeTranslateChatType['Java']);
    } catch (error) {
      Logger.warn('%c [ error ]-64', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  });
  // 单个节点操作
  treeItemDisposable && treeItemDisposable.dispose();
  treeItemDisposable = vscode.commands.registerCommand(
    'JoyCoderFE.multi.translation.command',
    _.debounce(async (params) => {
      if (params) {
        const abortController = params?.fileController;
        const reportInfo = params?.transformCodeReport;
        if (abortController && !abortController.signal.aborted) {
          const result = await vscode.window.showInformationMessage(
            `${params.fileName}文件正在翻译你确定要取消吗？`,
            { modal: true },
            '确定'
          );
          if (result == '确定') {
            try {
              if (fileTreeDataProvider) {
                params.statusIconPath = fileTreeDataProvider.getIconPath('abort');
                fileTreeDataProvider.updateNode(params);
              }
              !abortController.signal.aborted && abortController.abort();
            } catch {}
          }
        } else {
          // 根据filePath打开文件
          try {
            // vscode.commands.executeCommand('vscode.open', vscode.Uri.file(params.filePath));
            const activeEditor = vscode.window.activeTextEditor;
            vscode.commands
              .executeCommand(
                'vscode.diff',
                vscode.Uri.file(params.filePath),
                vscode.Uri.file(params.originalFilePath),
                `${params.fileName}<->${params.originalFileName}`,
                {
                  viewColumn: activeEditor?.viewColumn,
                }
              )
              .then(async () => {
                const fileContent = await getFileContent(params.filePath);
                const codeTranslateLanguage = new CodeTranslateLanguage(params);
                setTimeout(() => {
                  vscode.window
                    .showInformationMessage('是否要将转换后的代码一键替换到当前文档中？', '是', '否')
                    .then((selection) => {
                      if (selection === '是') {
                        // 将新的内容写入原文件
                        fs.writeFileSync(params.originalFilePath, fileContent);
                        // 删除临时文件
                        fs.unlinkSync(params.filePath);
                        reportInfo && codeTranslateLanguage.startReportLog(reportInfo);
                      }
                    });
                }, 2000);
                setTimeout(() => {
                  reportInfo && codeTranslateLanguage.startReportLog(reportInfo);
                }, 10000);
              });
          } catch {}
        }
      }
    }, 800)
  );
  // 批量取消节点操作
  cancelDisposable && cancelDisposable.dispose();
  cancelDisposable = vscode.commands.registerCommand('JoyCoderFE.translation.cancelAll', async () => {
    const fileList = fileTreeDataProvider?.getFileList() ?? [];
    if (fileList.length === 0) return;
    const result = await vscode.window.showInformationMessage(
      `多个文件正在翻译,你确定要全部取消吗？`,
      { modal: true },
      '确定'
    );
    if (result == '确定') {
      const traverseAndCancel = (nodes) => {
        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i];
          const abortController = node?.fileController;
          if (abortController && !abortController.signal.aborted) {
            try {
              if (fileTreeDataProvider) {
                node.statusIconPath = fileTreeDataProvider.getIconPath('abort');
                fileTreeDataProvider.updateNode(node);
              }
              !abortController.signal.aborted && abortController.abort();
            } catch {}
          }
          // 递归遍历子节点
          if (node.children && node.children.length > 0) {
            traverseAndCancel(node.children);
          }
        }
      };

      traverseAndCancel(fileList);
    }
  });
  // 批量取消节点操作
  replaceAllDisposable && replaceAllDisposable.dispose();
  replaceAllDisposable = vscode.commands.registerCommand('JoyCoderFE.translation.replaceAll', async () => {
    const fileList = fileTreeDataProvider?.getFileList() ?? [];
    if (fileList.length === 0) return;
    const traverseAndCancel = (nodes) => {
      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];
        const abortController = node?.fileController;
        if (abortController && !abortController.signal.aborted) {
          return true;
        }
        // 递归遍历子节点
        if (node.children && node.children.length > 0) {
          if (traverseAndCancel(node.children)) {
            return true;
          }
        }
      }
      return false;
    };
    const signalAborted = traverseAndCancel(fileList);
    if (signalAborted) {
      Logger.showWarningMessage('当前批次文件未全部转换完成，请稍后~');
    } else {
      const result = await vscode.window.showInformationMessage(
        `你确定要全部替换吗？\n确认该操作原文件将被替换\n新文件将被删除\n请再次确认？`,
        { modal: true },
        '确定'
      );
      if (result == '确定') {
        await batchReplacementFile(fileList);
        Logger.showInformationMessage('当前批次文件已全部替换完成~');
      }
    }
  });

  context.subscriptions.push(Vue2Disposable);
  context.subscriptions.push(Vue3Disposable);
  context.subscriptions.push(reactDisposable);
  context.subscriptions.push(taroDisposable);
  context.subscriptions.push(taroVueDisposable);
  context.subscriptions.push(arkDisposable);
  context.subscriptions.push(flutterDisposable);
  context.subscriptions.push(javaDisposable);
  context.subscriptions.push(treeItemDisposable);
  context.subscriptions.push(cancelDisposable);
  context.subscriptions.push(replaceAllDisposable);
  init([], '');
}
async function init(file, fileType: string) {
  try {
    const paths = file?.length > 1 ? await getDirectoryTree(file[1]) : [];
    const fileList = modifyFileTreeNode(paths, fileType);
    if (filesTreeView) {
      if (fileTreeDataProvider) {
        fileTreeDataProvider.setFileList(fileList);
        fileTreeDataProvider.setProcessType(fileType ?? '');
        fileTreeDataProvider.refresh(); // 更新数据
      }
      return;
    }
    fileTreeDataProvider = new FileTreeDataProvider(fileList ?? [], fileType ?? '');
    // filesTreeView = vscode.window.createTreeView('multiDocumentTranslation', {
    //   treeDataProvider: fileTreeDataProvider,
    // });
  } catch (error) {
    Logger.error(`翻译为${fileType}报错了`, error);
  }
}

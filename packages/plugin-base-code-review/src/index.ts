import * as vscode from 'vscode';
import * as cp from 'child_process';
import _ from 'lodash';
import { askChatGPT_ToCR, getActiveWebview } from '@joycoder/plugin-base-ai/src/dialog';
import { getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { GlobalState, Logger, getVscodeConfig, isBusiness, isBusinessLocal } from '@joycoder/shared';
import { CodeReviewChatType } from '@joycoder/plugin-base-ai/src/dialog/constant';
import initMultiCodeReview from './codeRview';
import initMultiDocumentTranslation from './multiDocumentTranslation';
import initErrorLine from './errorLine/commands';
import ActiveCodeReview from './activeCodeReview';
import GenerateCommitMessage from './commitMessage';
import { checkIHubRules } from './iHubCheck';

let disposable: vscode.Disposable = new vscode.Disposable(() => {});

interface IDiffInterface {
  modifiedContent: string; // 获取右侧（修改后）内容
  originalContent: string; // 获取左侧（原始）内容
  diffContent?: string; // 获取git的diff数据
}

/**
 * 初始化扩展功能并注册命令。
 * @param context - 扩展上下文，用于访问和管理扩展的资源和状态。
 * - 注册代码审查命令，使用防抖动处理以优化性能。
 * - 根据业务条件初始化多代码审查、多文档翻译和错误行加载功能。
 * - 执行即时代码审查和生成提交消息。
 */
export default function (context: vscode.ExtensionContext) {
  try {
    disposable && disposable.dispose();
    const debouncedSearch = _.debounce(loadCRView, 800);
    disposable = vscode.commands.registerCommand('JoyCoder.ai.code.review', () => {
      try {
        debouncedSearch();
      } catch (error) {
        Logger.log('%c [ error ]-21', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      }
    });

    context.subscriptions.push(disposable);

    if (!isBusiness() && !isBusinessLocal()) {
      initMultiCodeReview(context);
      checkIHubRules(context);
      initMultiDocumentTranslation(context);
      // errorLine 加载
      initErrorLine(context);
      new ActiveCodeReview(context).immediateCodeReview();
      new GenerateCommitMessage(context);
    }
  } catch (error) {
    Logger.log('%c [ error ]-26', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}
/**
 * 获取Git差异文件内容
 * @returns {Promise<IDiffInterface>} 返回一个Promise，解析为包含原始内容、修改后内容和差异内容的对象
 */
export async function getGitDiffFileContent() {
  return new Promise<IDiffInterface>((resolve, reject) => {
    try {
      const diffInfo = {} as IDiffInterface;
      const gitExtension = vscode.extensions.getExtension('vscode.git');
      if (gitExtension) {
        const activeEditor = vscode.window.activeTextEditor;
        // 监听活动编辑器变化
        if (activeEditor) {
          // 检查当前编辑器是否为差异编辑器
          const isDiffEditor = activeEditor.viewColumn === undefined;
          if (isDiffEditor) {
            // 获取左侧（原始）和右侧（修改后）的内容
            const visibleEditors = vscode.window.visibleTextEditors;
            // 遍历编辑器以找到diff编辑器
            for (const editor of visibleEditors) {
              if (editor.document.uri.scheme === 'output') {
                // 这里我们找到了一个非diff的编辑器，跳过
                continue;
              }
              // 检查是否是diff编辑器的一侧
              if (editor.document.uri.scheme === 'git') {
                // 获取左侧（原始）内容
                const originalContent = editor.document.getText();
                diffInfo['originalContent'] = originalContent;
              } else if (editor.document.uri.scheme === 'file') {
                // 获取右侧（修改后）内容
                const modifiedContent = editor.document.getText();
                diffInfo['modifiedContent'] = modifiedContent;
              }
            }
            try {
              const document = activeEditor.document;
              const uri = document.uri;
              if (uri.scheme !== 'file') {
                resolve({ ...diffInfo });
                return; // 当前文档不是文件
              }
              const filePath = uri.fsPath;
              const gitCommand = `git diff \"${filePath}\"`;
              // 获取git的diff数据
              cp.exec(gitCommand, { cwd: vscode.workspace.rootPath }, (err, stdout) => {
                if (err) {
                  vscode.window.showErrorMessage('Git diff failed');
                  resolve({ ...diffInfo });
                  return;
                }
                stdout = stdout.length > 0 ? stdout : diffInfo?.modifiedContent;
                resolve({ ...diffInfo, diffContent: stdout });
              });
            } catch (error) {
              resolve({ ...diffInfo });
            }
          } else {
            reject('不是diff窗口');
          }
        }
      }
    } catch (error) {
      reject(error);
    }
  });
}
/**
 * 单文件Diff窗口评审
 *
 * @returns {Promise<void>} 无返回值。
 * @throws {Error} 如果发生错误。
 */
export async function loadCRView() {
  try {
    // const activeEditor: any = vscode.window.activeTextEditor;
    const isCodeReviewing = GlobalState.get('JoyCoder.isCodeReviewing');
    if (isCodeReviewing) {
      Logger.showInformationMessage('JoyCode代码评审正在执行，请稍后~');
      return;
    }
    const diffInfo = await getGitDiffFileContent();
    let webview: any = null;
    // await vscode.commands.executeCommand('JoyCoder-left-view.focus');
    const isWebViewReady = () => {
      const maxAttempts = 50; //最大尝试500毫秒
      let attempts = 0;
      return new Promise(async (resolve) => {
        const checkWebView = async () => {
          try {
            webview = await getActiveWebview();
            if (webview) {
              resolve(true);
            } else if (attempts < maxAttempts) {
              setTimeout(checkWebView, 50);
              attempts++;
            } else {
              resolve(true);
            }
          } catch (error) {
            resolve(true);
          }
        };
        checkWebView();
      });
    };
    await isWebViewReady();
    try {
      const model = getChatModelAndConfig().label;
      const options: { webviewView?: vscode.WebviewView; pageTitle: string | undefined; type?: string } =
        model == 'LLaMA-3'
          ? {
              type: CodeReviewChatType.codeReviewOneFile,
              pageTitle: 'AI小助手',
              webviewView: webview?.webviewView,
            }
          : {
              type: CodeReviewChatType.codeReviewOneFile,
              pageTitle: 'AI小助手',
              webviewView: webview?.webviewView,
            };
      GlobalState.update('JoyCoder.isCodeReviewing', true);
      const diffCode: string | undefined = diffInfo?.diffContent;
      let code: string | undefined = diffInfo?.modifiedContent;
      if (diffCode == code) {
        code = '';
      }
      return askChatGPT_ToCR({ diffCode, code }, options);
    } catch (error) {
      GlobalState.update('JoyCoder.isCodeReviewing', false);
      Logger.error('%c [ error ]-153', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    // const documentLanguage: string = (activeEditor && activeEditor.document.languageId) || '';
  } catch (error) {
    GlobalState.update('JoyCoder.isCodeReviewing', false);
    Logger.showErrorMessage('当前不是diff窗口，请点击下方文件后重试');
  }
}

/**
 * 判断当前活动编辑器是否在差异编辑器中
 * @returns {boolean} 当前编辑器是否在差异编辑器中
 */
export function getIsInDiffEditor() {
  const activeEditor = vscode.window.activeTextEditor;
  if (activeEditor) {
    const uri = activeEditor.document.uri;
    return uri.scheme == 'git' || !activeEditor.viewColumn;
  }
  return false;
}

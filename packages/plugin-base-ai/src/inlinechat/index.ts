import * as vscode from 'vscode';
import { uniqueId } from 'lodash';
import {
  Logger,
  getSelectedTextWithoutComment,
  getSelectionInfo,
  isMac,
  sleep,
  getSelectedCodeErrors,
  setLineSelected,
  getCurrentFileEditor,
  reportAction,
  ActionType,
  JOYCODE_BRAND_NAME,
} from '@joycoder/shared';
import { CommandId, Commands } from '../cursor';
import { Scene, ChatProxyService } from '../proxy';
import { UseMode, QuickCommand, NoteComment } from './utils';

let inlineChatCommentController: vscode.CommentController | null = null;
let activeCommentThread: vscode.CommentThread | null = null;
let abortController: AbortController | null = null;
let useMode = UseMode.chat;

/**
 * 关闭评论弹窗
 */
async function closeComment() {
  abortController?.abort();

  if (inlineChatCommentController) {
    inlineChatCommentController.dispose();
    inlineChatCommentController = null;
  }

  if (activeCommentThread) {
    activeCommentThread.dispose();
    activeCommentThread = null;

    await sleep(100); // 已存在时打开另外一个，不延时会打不开另一个
  }

  await vscode.commands.executeCommand('setContext', 'when.JoyCoder.RunEsc', false);
}

/**
 * 顶部增加用于指导使用的comment
 */
function showTipComment() {
  if (!activeCommentThread) return;

  let tipText = `[☑问答模式](command:JoyCoder.ai.inlineChat.changeMode?"chat")：如解释一下这段代码 [□生成模式](command:JoyCoder.ai.inlineChat.changeMode?"gen")：如写一个匹配数字的正则 [□编辑模式](command:JoyCoder.ai.inlineChat.changeMode?"edit")：如修复这段代码中的问题。
  <br/><sub>快捷操作：[解释代码](command:JoyCoder.ai.inlineChat.quickCommand?"explain")&nbsp;&nbsp;[解释报错](command:JoyCoder.ai.inlineChat.quickCommand?"explainError")&nbsp;&nbsp;[修复报错](command:JoyCoder.ai.inlineChat.quickCommand?"fix")&nbsp;&nbsp;[增加注释](command:JoyCoder.ai.inlineChat.quickCommand?"comment")</sub>`;

  if (useMode == UseMode.gen) {
    tipText = tipText.replace('☑', '□').replace('□生成模式', '☑生成模式');
  } else if (useMode == UseMode.edit) {
    tipText = tipText.replace('☑', '□').replace('□编辑模式', '☑编辑模式');
  }

  const tipComment = new NoteComment(
    tipText,
    vscode.CommentMode.Preview,
    { name: '🛠 模式选择：' },
    activeCommentThread
  );
  activeCommentThread.comments = [tipComment];
}

/**
 * 创建inlinechat窗口
 */
async function handleCreateInlineChat(defaultMode = useMode) {
  const activeEditor: vscode.TextEditor | undefined = vscode.window.activeTextEditor;
  if (!activeEditor) return;

  useMode = defaultMode;

  await closeComment();

  const document: vscode.TextDocument = activeEditor.document;
  const selectionInfo = getSelectionInfo();

  // 评论出现的行
  const nextLine = selectionInfo?.selection.isEmpty
    ? selectionInfo?.selection.end.line
    : Math.min(selectionInfo?.selection.end.line + 1, document.lineCount - 1) - 1;
  const lineRange = new vscode.Range(nextLine, 0, nextLine, 0);

  // 创建评论控制器
  inlineChatCommentController = vscode.comments.createCommentController('joycoder-inline-chat', 'JoyCoder Inline Chat');
  inlineChatCommentController.options = {
    // Command + Enter如果不生效，可能是因为和Markdown All in One这个插件的快捷键冲突了
    placeHolder: `提交：${isMac ? 'Command + Enter' : 'Ctrl + Enter'} 退出：ESC`,
    prompt: '点此输入',
  };
  inlineChatCommentController.commentingRangeProvider = {
    provideCommentingRanges: (): vscode.Range[] => {
      return [lineRange];
    },
  };

  // 创建评论线程
  activeCommentThread = inlineChatCommentController.createCommentThread(document.uri, lineRange, []);
  activeCommentThread.label = `${JOYCODE_BRAND_NAME} InlineChat`;
  activeCommentThread.collapsibleState = vscode.CommentThreadCollapsibleState.Expanded;

  await vscode.commands.executeCommand('setContext', 'when.JoyCoder.RunEsc', true);

  await sleep(100); // 此行代码配合nextLine可使在自动插入tipComment后，光标仍可聚焦在输入框中
  showTipComment();

  scrollToComment();
}

/**
 * 向下滚动编辑器，以防弹窗看不到
 */
function scrollToComment() {
  if (!activeCommentThread) return;

  const endLine = activeCommentThread.range?.end.line;
  const range = new vscode.Range(endLine ?? 0, 0, endLine ?? 0 + 1, 0);
  const editor = getCurrentFileEditor();
  editor?.revealRange(range, vscode.TextEditorRevealType.InCenterIfOutsideViewport);
}

/**
 * 通过输入框回车或提交按钮发起的
 */
async function handleEnter({ text }: { text: string }) {
  const selectedCode = getSelectedTextWithoutComment();

  await handleAction({
    input: text,
    code: selectedCode,
  });
}

/**
 * 发起问答、生成或编辑
 */
async function handleAction({ input, code }: { input: string; code: string }) {
  if (useMode == UseMode.edit) {
    // 编辑状态时使用代码优先级：选中的代码>光标所在行代码>中断操作
    const selectedCode = getSelectedTextWithoutComment();
    if (!selectedCode) {
      setLineSelected();
      const afterSelectedCode = getSelectedTextWithoutComment();
      if (!afterSelectedCode) {
        Logger.showInformationMessage('请选中代码后操作~');
        return;
      }
    }
  }

  // 代码生成模式  代码编辑模式
  if (useMode == UseMode.gen || useMode == UseMode.edit) {
    await closeComment(); // 先关闭评论，否则CommandId中的activeEditor取到的是评论框中的文档
    const selectionInfo = getSelectionInfo();
    await vscode.commands.executeCommand(CommandId, Commands.Create, selectionInfo?.selection, input);
    return;
  }

  // 默认为问答模式
  if (!activeCommentThread) {
    await handleCreateInlineChat(); // 从codeAction唤起解释报错时没有弹窗，所以需要新建
  }

  if (!activeCommentThread) return;
  showTipComment(); // 先清空之前的问答

  const humanComment = new NoteComment(input, vscode.CommentMode.Preview, { name: '👤您：' }, activeCommentThread);
  const aiComment = new NoteComment(
    '思考中...',
    vscode.CommentMode.Preview,
    { name: '🤖JoyCoder：' },
    activeCommentThread
  );
  activeCommentThread.comments = [...activeCommentThread.comments, humanComment, aiComment];

  const question = code
    ? `${input}\n${code}`
    : input + '\n本次提问是一问一答模式，请勿向我提问，直接按你的理解给出最佳结果，回答尽可能简洁。';
  requestChat(question, aiComment);
}

/**
 * 处理问答模式请求
 */
async function requestChat(question: string, aiComment: NoteComment) {
  const setContext = (value: boolean) => {
    vscode.commands.executeCommand('setContext', 'when.JoyCoder.inlineChat.streaming', value);
  };

  const setResponseComment = (fullText: string) => {
    if (!activeCommentThread) return;
    activeCommentThread.comments = activeCommentThread.comments.map((comment) => {
      if ((comment as NoteComment).id === aiComment.id) {
        comment.body = new vscode.MarkdownString();
        comment.body.appendMarkdown(fullText);
        comment.body.isTrusted = true;
        comment.body.supportHtml = true;
      }
      return comment;
    });
  };

  setContext(true);
  abortController = new AbortController();
  const signal = abortController.signal;
  signal.addEventListener('abort', () => {
    setContext(false);
  });
  const startTime = Date.now();
  try {
    const res = await new ChatProxyService().invoke(
      {
        message: question,
        scene: Scene.InlineChat,
        respMaxTokens: 4096,
      },
      {
        abortController,
        onProgress(response) {
          const { fullText = '' } = response;
          if (!activeCommentThread) {
            abortController?.abort();
            return;
          }

          setResponseComment(fullText);
        },
      }
    );
    setResponseComment(res.fullText || '');
    setContext(false);
    // 完成后进行统一数据上报
    reportAction({
      accept: 1,
      actionCate: 'ai',
      actionType: ActionType.editorChatGen,
      conversationId: res.conversationId,
      question,
      result: res.fullText,
      consumeTime: Date.now() - startTime,
    });
  } catch (error) {
    setResponseComment('请求失败，请重试~');
    setContext(false);
    Logger.error(error);
  }
}

/**
 * 切换使用模式
 * @param selMode
 */
function handleChangeMode(selMode: UseMode) {
  useMode = selMode;
  showTipComment();
}

/**
 * 处理快捷指令
 * @param command
 */
export async function handleQuickCommand(command: QuickCommand, selection?: vscode.Selection) {
  if (selection) {
    // 悬浮报错时通过codeAction点过来的情况
    const editor = getCurrentFileEditor();
    if (editor) {
      editor.selection = selection;
    }
  } else {
    const selectedCode = getSelectedTextWithoutComment();

    if ([QuickCommand.explain, QuickCommand.explainError].includes(command)) {
      if (!selectedCode) {
        setLineSelected();
        const afterSelectedCode = getSelectedTextWithoutComment();
        if (!afterSelectedCode) {
          Logger.showInformationMessage('请选中代码后操作~');
          return;
        }
      }
    }
  }

  const code = getSelectedTextWithoutComment();
  switch (command) {
    case QuickCommand.explain:
      handleChangeMode(UseMode.chat);
      handleAction({ input: '解释一下这段代码', code });
      break;
    case QuickCommand.explainError:
      handleChangeMode(UseMode.chat);
      const errorInfoExplain = getSelectedCodeErrors();
      handleAction({
        input: `解释一下这段代码为什么报错${errorInfoExplain ? `，错误信息如下：${errorInfoExplain}` : ''}`,
        code,
      });
      break;
    case QuickCommand.fix:
      handleChangeMode(UseMode.edit);
      const errorInfo = getSelectedCodeErrors();
      handleAction({
        input: `${
          errorInfo ? `<PROBLEMCODE4179>${code}</PROBLEMCODE4179>\n${errorInfo}` : '修复这段代码中的报错或问题'
        }`,
        code,
      });
      break;
    case QuickCommand.comment:
      handleChangeMode(UseMode.edit);
      handleAction({ input: '为这段代码增加注释，函数或类使用块注释，其他使用行注释', code });
      break;
    default:
      break;
  }
}

export default function (context) {
  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCoder.ai.inlineChat.create', handleCreateInlineChat),
    vscode.commands.registerCommand('JoyCoder.ai.inlineChat.enter', handleEnter),
    vscode.commands.registerCommand('JoyCoder.ai.inlineChat.delete', closeComment),
    vscode.commands.registerCommand('JoyCoder.ai.inlineChat.abort', () => {
      abortController?.abort();
    }),
    vscode.commands.registerCommand('JoyCoder.ai.inlineChat.changeMode', handleChangeMode),
    vscode.commands.registerCommand('JoyCoder.ai.inlineChat.quickCommand', handleQuickCommand),
    vscode.workspace.onDidCloseTextDocument(async (e: vscode.TextDocument) => {
      if (e.uri.scheme !== 'comment') {
        return;
      }
      await closeComment();
    })
  );
}

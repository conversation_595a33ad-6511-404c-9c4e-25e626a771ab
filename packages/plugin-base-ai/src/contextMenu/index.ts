import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';
import { extractFunctionComments } from '@joycoder/plugin-base-code-completion/src/utils/elidableText';
import {
  isBusiness,
  isIDE,
  getCurrentFileType,
  getMarkdownCodeBlockContent,
  extractCommentFromCode,
  Logger,
  getVscodeConfig,
} from '@joycoder/shared';
import { getChatModelAndConfig } from '../model';
import { ChatProxyService, Scene } from '../proxy';
import * as vscode from 'vscode';
import {
  askChatGPT_ByConfigText,
  askChatGPT_codeLensMenu,
  askChatGPT_simplePrompt,
  askChatGPT_transformCode,
  setDecorationType,
} from '../dialog';
import { setDelLoading, setReportLog, codelensReconstruction, codelensLineComment } from '../dialog/codeLensUtil';
import { RightMenuChatType, CodeTranslateChatType, CodeLensMenuChatType } from '../dialog/constant';
import { askCoderByContextMenu } from '@joycoder/agent-driven/src/adaptor/contextMenu';

let lsFnCommitLoading = false;

export function getJoyCoderContextMenus(
  context: vscode.ExtensionContext,
  decorationType: vscode.Disposable | vscode.TextEditorDecorationType,
  coderProvider?: any
) {
  try {
    if (!isBusiness()) {
      context.subscriptions.push(
        // 编辑器按钮
        vscode.commands.registerCommand('JoyCoder.ai.chat.ask', () => {
          vscode.commands.executeCommand('joycoder.joycoder.SidebarProvider.focus');
          vscode.commands.executeCommand('JoyCoder-left-view.focus');
        }),
        // AI助手-其它
        vscode.commands.registerCommand('JoyCoder.ai.chat.other', askChatGPT_ByConfigText)
      );
      if (!isIDE()) {
        context.subscriptions.push(
          //, 神医安全检查
          vscode.commands.registerCommand('JoyCoder.sec', () => {
            askChatGPT_simplePrompt(RightMenuChatType.codeShenYiFix, '集团安全神医大模型正在提供安全检测服务:');
          })
        );
      }
    }
    getTranslateMenu(context);
    context.subscriptions.push(
      // AI助手-解释代码
      vscode.commands.registerCommand('JoyCoder.ai.chat.explain', () => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (isAgentView) {
          const selectContext = getSelectionText();
          askCoderByContextMenu(
            {
              type: RightMenuChatType.codeExplain,
              text: '解释下这段代码?',
              data: {
                functionContent: selectContext,
              },
            },
            coderProvider
          );
          return;
        }
        askChatGPT_simplePrompt(RightMenuChatType.codeExplain, '解释下这段代码?');
      }),
      // AI助手-为什么报错？
      vscode.commands.registerCommand('JoyCoder.ai.chat.broken', () => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (isAgentView) {
          const selectContext = getSelectionText();
          askCoderByContextMenu(
            {
              type: RightMenuChatType.codeWhyThrowErr,
              text: '这段代码为什么报错?',
              data: {
                functionContent: selectContext,
              },
            },
            coderProvider
          );
          return;
        }
        askChatGPT_simplePrompt(RightMenuChatType.codeWhyThrowErr, '这段代码为什么报错?');
      }),
      // AI助手-重构建议
      vscode.commands.registerCommand('JoyCoder.ai.chat.refactor', () => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (isAgentView) {
          const selectContext = getSelectionText();
          askCoderByContextMenu(
            {
              type: RightMenuChatType.codeReconstruction,
              text: '重构这段代码并解析为什么这么做',
              data: {
                functionContent: selectContext,
              },
            },
            coderProvider
          );
          return;
        }
        askChatGPT_simplePrompt(RightMenuChatType.codeReconstruction, '重构这段代码并解析为什么这么做');
      }),
      // AI助手-如何做单测？
      vscode.commands.registerCommand('JoyCoder.ai.chat.test', (data) => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (isAgentView) {
          const selectContext = getSelectionText();
          askCoderByContextMenu(
            {
              type: RightMenuChatType.codeUnitTest,
              text: '编写这段代码的单元测试用例代码',
              data: {
                functionContent: selectContext,
              },
            },
            coderProvider
          );
          return;
        }
        askChatGPT_simplePrompt(RightMenuChatType.codeUnitTest, '编写这段代码的单元测试用例代码');
      }),
      // AI助手-帮写文档
      vscode.commands.registerCommand('JoyCoder.ai.chat.doc', () => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (isAgentView) {
          const selectContext = getSelectionText();
          askCoderByContextMenu(
            {
              type: RightMenuChatType.codeDoc,
              text: '根据提供信息，帮我写一个文档,包含定义和使用示例，用markdown表格方式提供给我，优先用中文',
              data: {
                functionContent: selectContext,
              },
            },
            coderProvider
          );
          return;
        }
        askChatGPT_simplePrompt(
          RightMenuChatType.codeDoc,
          '根据提供信息，帮我写一个文档,包含定义和使用示例，用markdown表格方式提供给我，优先用中文'
        );
      }),
      // AI助手-代码评审
      vscode.commands.registerCommand('JoyCoder.ai.chat.review', () => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (isAgentView) {
          const selectContext = getSelectionText();
          askCoderByContextMenu(
            {
              type: RightMenuChatType.codeReview,
              text: '选中代码进行代码评审',
              data: {
                functionContent: selectContext,
              },
            },
            coderProvider
          );
          return;
        }
        askChatGPT_simplePrompt(RightMenuChatType.codeReview, '选中代码进行代码评审');
      }),
      // AI助手-自动填充
      vscode.commands.registerCommand('JoyCoder.ai.chat.autoFill', () => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (isAgentView) {
          const selectContext = getSelectionText();
          askCoderByContextMenu(
            {
              type: RightMenuChatType.codeAutoFill,
              text: '根据代码规则填充代码',
              data: {
                functionContent: selectContext,
              },
            },
            coderProvider
          );
          return;
        }
        askChatGPT_simplePrompt(RightMenuChatType.codeAutoFill, '根据代码规则填充代码');
      }),
      // 函数注释
      vscode.commands.registerCommand('JoyCoder.codelens.functionComment', async (data) => {
        if (lsFnCommitLoading) {
          return;
        }
        const disposable = vscode.window.setStatusBarMessage('正在生成代码，请稍后...');
        let text = '';
        try {
          const code = data.functionContent;
          if (!code) {
            return;
          }
          const decorationItem = setDecorationType(data);
          lsFnCommitLoading = true;
          const abortController = new AbortController();
          setDelLoading(decorationItem, abortController, () => {
            lsFnCommitLoading = false;
          });
          const editor = vscode.window.activeTextEditor;
          const isClass = /class\s/.test(data.text);
          const model = getChatModelAndConfig().label;
          const lexeme = model.toLocaleLowerCase().indexOf('gpt') > -1 ? '' : '只输出注释不要输出代码，';
          const languageId = getCurrentFileType();
          const message = isClass
            ? `针对${code}代码，给我一个${languageId}语言的类文档注释，带上文档注释符号，${lexeme}尽量简洁用最少字数，不要额外附带其他信息，用中文语言，不要含有\`\`\``
            : `针对${code}代码，给我一个${languageId}语言的函数文档注释，带上文档注释符号，${lexeme}尽量简洁字数尽可能的少，不要额外附带其他信息，用中文语言，不要含有\`\`\``;
          const result = await new ChatProxyService().invoke(
            {
              message,
              scene: Scene.Cursor,
              respMaxTokens: 2500,
            },
            {
              abortController,
              onProgress: () => {},
            }
          );
          text = result.fullText || '';
          const reporter = setReportLog(
            message,
            model,
            '0.1',
            [
              {
                completionCode: text,
                index: 0,
              },
            ],
            result.conversationId,
            'Functioncomments'
          );
          try {
            const comment = text.includes('```') ? getMarkdownCodeBlockContent(text) : text;
            text = extractCommentFromCode(comment);
            if (!text) {
              text = await extractFunctionComments(languageId, comment);
            }
          } catch (error) {
            text = '';
            Logger.error(
              '%c [ error ]-901',
              'font-size:13px; background:pink; color:#bf2c9f;',
              '行间菜单去```失败',
              error
            );
          }
          if (editor && text) {
            // 函数注释结果结果缓存，用于后续AI代码占比统计
            AdoptResultCache.setRemote(text, model, AdoptResultCache.ADOPT_CODE_SOURCE.COMMENT, result.conversationId);
            await editor.insertSnippet(new vscode.SnippetString(text + '\n'), new vscode.Position(data.lineNumber, 0));
            reporter && reporter.success({ result: text, acceptIndex: 0 }, () => {});
          }
          decorationType && decorationType.dispose();
          decorationItem && decorationItem.dispose();
          lsFnCommitLoading = false;
          disposable.dispose();
        } catch (e) {
          vscode.window.showErrorMessage(e.message || e);
          lsFnCommitLoading = false;
          decorationType && decorationType.dispose();
          disposable.dispose();
        }
      }),
      //, 解释代码
      vscode.commands.registerCommand('JoyCoder.codelens.explain', (data) => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (isAgentView) {
          askCoderByContextMenu(
            {
              type: CodeLensMenuChatType.codelensExplain,
              text: '解释下这段代码？',
              data: data,
            },
            coderProvider
          );
          return;
        }
        askChatGPT_codeLensMenu(CodeLensMenuChatType.codelensExplain, '解释下这段代码？', data);
      }),
      // 代码重构
      vscode.commands.registerCommand('JoyCoder.codelens.reconstruction', (data, endLine) =>
        // askChatGPT_codeLensMenu(CodeLensMenuChatType.codelensReconstruction, '重构这段代码？', data)
        codelensReconstruction(data, endLine)
      ),
      //行间代码评审
      vscode.commands.registerCommand('JoyCoder.codelens.codeReview', (data) => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (!!isAgentView) {
          askCoderByContextMenu(
            {
              type: CodeLensMenuChatType.codelensCodeReview,
              text: '评审这段代码？',
              data: data,
            },
            coderProvider
          );
          return;
        }
        askChatGPT_codeLensMenu(CodeLensMenuChatType.codelensCodeReview, '评审这段代码？', data);
      }),
      //, 单元测试
      vscode.commands.registerCommand('JoyCoder.codelens.test', (data) => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (isAgentView) {
          askCoderByContextMenu(
            {
              type: CodeLensMenuChatType.codelensTest,
              text: '帮输出我单元测试用例？',
              data: data,
            },
            coderProvider
          );
          return;
        }
        askChatGPT_codeLensMenu(CodeLensMenuChatType.codelensTest, '帮输出我单元测试用例？', data);
      }),
      //, 代码优化
      vscode.commands.registerCommand('JoyCoder.codelens.optimization', (data) => {
        const isAgentView = getVscodeConfig('JoyCoder.switch.AgentView');
        if (isAgentView) {
          askCoderByContextMenu(
            {
              type: CodeLensMenuChatType.codelensOptimization,
              text: '帮我优化代码？',
              data: data,
            },
            coderProvider
          );
          return;
        }
        askChatGPT_codeLensMenu(CodeLensMenuChatType.codelensOptimization, '帮我优化代码？', data);
      }),
      //, 逐行注释
      vscode.commands.registerCommand('JoyCoder.codelens.comment', (data, endLine) =>
        // askChatGPT_codeLensMenu(CodeLensMenuChatType.codelensLineComment, '帮我逐行注释代码？', data)
        codelensLineComment(data, endLine)
      )
    );
  } catch (error) {
    console.warn('%c [ old-chat-error ]-1270', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}

/**
 * 注册代码翻译相关的命令
 * @param context VSCode扩展上下文
 * @description 为不同框架和语言的代码转换注册命令，包括Vue、React、Taro、TypeScript等
 */
function getTranslateMenu(context: vscode.ExtensionContext) {
  try {
    context.subscriptions.push(
      // 代码翻译
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeVue', () =>
        askChatGPT_transformCode(CodeTranslateChatType['Vue2.x'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeVue2TS', () =>
        askChatGPT_transformCode(CodeTranslateChatType['Vue2.x-TS'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeVue3', () =>
        askChatGPT_transformCode(CodeTranslateChatType['Vue3.x'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeVue3TS', () =>
        askChatGPT_transformCode(CodeTranslateChatType['Vue3.x-TS'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeReact', () =>
        askChatGPT_transformCode(CodeTranslateChatType['React-JS'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeReactTS', () =>
        askChatGPT_transformCode(CodeTranslateChatType['React-TS'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeTaro', () =>
        askChatGPT_transformCode(CodeTranslateChatType['Taro-React'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeTaroVueTS', () =>
        askChatGPT_transformCode(CodeTranslateChatType['Taro-Vue-TS'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeTaroReactTS', () =>
        askChatGPT_transformCode(CodeTranslateChatType['Taro-React-TS'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.optimizedHTML', () =>
        askChatGPT_transformCode(CodeTranslateChatType['HTMLOptimization'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeTS', () =>
        askChatGPT_transformCode(CodeTranslateChatType['TypeScript'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeJS', () =>
        askChatGPT_transformCode(CodeTranslateChatType['JavaScript'])
      ),
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeHarmonyArkTS', () =>
        askChatGPT_transformCode(CodeTranslateChatType['Harmony-ArkTS'])
      ),
      // 转{Flutter3
      vscode.commands.registerCommand('JoyCoder.ai.chat.transformCodeFlutter3', () =>
        askChatGPT_transformCode(CodeTranslateChatType['Flutter3'])
      )
    );
  } catch (error) {}
}

function getSelectionText() {
  const editor = vscode.window.activeTextEditor;
  const code: string = editor?.document.getText(vscode.window.activeTextEditor?.selection) || '';
  return code;
}

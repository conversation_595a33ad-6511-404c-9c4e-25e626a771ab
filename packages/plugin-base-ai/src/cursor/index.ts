import * as vscode from 'vscode';
import { ActionType, GlobalState, getCurrentFileType, reportAction, reportRd } from '@joycoder/shared';
import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';
import { v4 as uuidv4 } from 'uuid';
import { uniqueId, isEqual } from 'lodash';
import { Ask } from './ai';
import { handleQuickCommand } from '../inlinechat';
import { QuickCommand, UseMode, getQuickShort } from '../inlinechat/utils';
import { getChatModelAndConfig } from '../model';
// import { GlobalState } from '@joycoder/shared';
// import to from 'await-to-js';

const ask = new Ask();

export const CommandId = 'JoyCoder.ai.chat.cursor';
export const enum Commands {
  Create,
  Edit,
  Optim,
  Ask,
  Chat,
  Fix,
  Comment,
  CloseTopGuide,
}

class CursorMergeProvider {
  constructor() {}

  provideCodeLenses(document) {
    return this.updateCodeLenses(document);
  }

  updateCodeLenses(document) {
    const codeLenses: vscode.CodeLens[] = [];

    // 如果用户隐藏了则不再展示顶部codelens
    if (!GlobalState.get('JoyCoder.isHideEditorTopGuide')) {
      const topCodeLens = new vscode.CodeLens(new vscode.Range(0, 0, 0, 0), {
        title: `JoyCode小知识: 点我或按下(${getQuickShort()})开启AI编码旅程!`,
        command: 'JoyCoder.ai.inlineChat.create',
        arguments: [UseMode.gen],
      });
      const closeCodeLens = new vscode.CodeLens(new vscode.Range(0, 0, 0, 0), {
        title: '不再提示',
        command: CommandId,
        arguments: [Commands.CloseTopGuide],
      });
      codeLenses.push(topCodeLens, closeCodeLens);
    }

    const editor = vscode.window.activeTextEditor;
    if (!editor || !isEqual(editor.document, document)) {
      return codeLenses; // 如果没有打开编辑器或者文档不匹配，则退出
    }
    const selection = editor.selection;
    // 在选中行前面添加一个新的 CodeLens
    // eslint-disable-next-line init-declarations
    let range: vscode.Range;
    if (selection && !selection.isEmpty) {
      range = new vscode.Range(selection.start.line, 0, selection.start.line, 0);
    } else {
      return codeLenses;
    }
    const commands = [
      /*
      {
        title: `编辑(${getQuickShort()})`,
        command: CommandId,
        arguments: [Commands.Edit, selection],
        tooltip: '使用AI重构选中区域内的代码',
      },
      {
        title: '生成代码注释',
        command: CommandId,
        arguments: [Commands.Comment, selection],
        tooltip: '为选中区域内的代码生成注释信息',
      },
      {
        title: '解答',
        command: CommandId,
        arguments: [Commands.Ask, selection],
        tooltip: '解答选中区域内的代码',
      },
      {
        title: 'Chat(Shift+Alt+I)',
        command: CommandId,
        arguments: [Commands.Chat, selection],
        tooltip: '跟选中区域内的代码进行自由互动',
      },
      */
    ];
    commands.forEach((command) => {
      const codeLens = new vscode.CodeLens(range, command);
      codeLenses.push(codeLens);
    });
    return codeLenses;
  }
}

const provider = new CursorMergeProvider();

export async function refreshEditor() {
  await vscode.commands.executeCommand('workbench.action.focusStatusBar');
  await vscode.commands.executeCommand('workbench.action.focusActiveEditorGroup');
}

export async function commandHandle(type, selection, input = '') {
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    return;
  }
  if (type == Commands.Create && !editor.selection?.isEmpty) {
    // 创建模式下有选中区域则切换成编辑模式
    type = Commands.Edit;
    selection = editor.selection;
  }
  let question = '';
  switch (type) {
    case Commands.Create: {
      const languageId = getCurrentFileType();
      const defaultInputValue = '用react写一个hello world';
      const userInput =
        input ||
        (await vscode.window.showInputBox({ prompt: '描述你所需要生成的代码', value: defaultInputValue })) ||
        '';
      if (userInput.trim()) {
        question = `生成一个"${userInput}"的代码，基于${languageId}，回答尽量简洁`;
        createCode({ editor, question, type });
        reportRd(31);
      }
      break;
    }
    case Commands.Edit: {
      // 将用户选择的代码片段约束为整行，避免选择某行中的一部分，方便缩进及diff
      const startPosition = new vscode.Position(selection.start.line, 0);
      const endPosition = new vscode.Position(selection.end.line, Infinity);
      const finalSelection = new vscode.Selection(startPosition, endPosition);

      const selectedText: any = editor.document.getText(finalSelection) || '';
      if (!selectedText.trim()) {
        return vscode.window.showErrorMessage('必须选中代码片段！');
      }
      const userInput =
        input ||
        (await vscode.window.showInputBox({
          prompt: '描述你所需编辑调整的内容',
        })) ||
        '';
      if (userInput.trim()) {
        question = `\`\`\`\n${selectedText}\n\`\`\`\r\n\r\n针对以上代码做修改，${userInput}，不要注释信息`;
        // question = `${selectedText}，${userInput}`;
        editCode({ editor, selection, question, selectedText, type });
        reportRd(32);
      }
      break;
    }
    case Commands.Comment:
      if (!selection) {
        return vscode.window.showErrorMessage('必须选中代码片段！');
      }
      const selectedText = editor.document.getText(selection);
      if (!selectedText) {
        return vscode.window.showErrorMessage('必须选中代码片段！');
      }
      // const [, result] = await to(
      //   Logger.showWarningMessage('该操作会发送选中代码至OpenAI，请确认代码中不存在敏感信息', '确认', '取消')
      // );
      // if (result == '确认') {
      const disposable = vscode.window.setStatusBarMessage('正在生成代码，请稍后...');
      try {
        const text = await ask.question(
          `针对${selectedText}代码，给我一个不要超过20个字的注解，并带上注释符号一起，不要额外附带其他信息`,
          () => {}
        );
        await editor.insertSnippet(new vscode.SnippetString(text + '\n'), new vscode.Position(selection.start.line, 0));
      } catch (e) {
        vscode.window.showErrorMessage(e.message || e);
      } finally {
        disposable.dispose();
      }
      // }
      break;
    case Commands.Ask:
      if (!selection) {
        return vscode.window.showErrorMessage('必须选中代码片段！');
      }
      vscode.commands.executeCommand('JoyCoder.ai.chat.explain');
      break;
    case Commands.Chat:
      vscode.commands.executeCommand('JoyCoder.ai.chat.other');
      reportRd(33);
      break;
    case Commands.Fix:
      handleQuickCommand(QuickCommand.fix);
      break;
    case Commands.CloseTopGuide:
      GlobalState.update('JoyCoder.isHideEditorTopGuide', true);
      refreshEditor();
      break;
  }
}

type Code = {
  pos: vscode.Position;
  ch: string;
};

// 代码插入完后自动打开代码diff
// function openDiff() {
//   setTimeout(async () => {
//     try {
//       const allProviders: any = await vscode.commands.executeCommand(
//         'vscode.executeCodeLensProvider',
//         vscode.window.activeTextEditor?.document.uri
//       );
//       const compareCommand = allProviders?.filter((item) => item?.command?.command == 'merge-conflict.compare')[0];
//       if (compareCommand) {
//         vscode.commands.executeCommand(compareCommand.command.command, ...compareCommand.command.arguments);
//       }
//     } catch (error) {
//       Logger.error(error);
//     }
//   }, 500);
// }

export async function batchRender({ editor, question, line, type, selectedText }) {
  let finish = false;
  try {
    let character = 0;
    const codeList: Code[] = [];
    let prevCode: Code[] = [];
    let firstCh = '';

    const insertCode = async (code?) => {
      await editor.edit((editBuilder) => {
        if (prevCode.length) {
          if (!code) {
            code = {
              pos: null,
              ch: '',
            };
          }
          code.pos = prevCode[0].pos;
          code.ch = prevCode.map((item) => item.ch).join('') + code.ch;
          prevCode = [];
        }
        if (code) {
          editBuilder.insert(code.pos, code.ch, { undoStopAfter: false });
        }
      });
    };
    // 将render和quesion分离是因为question异步的，editor渲染需要同步进行
    const render = async () => {
      const code = codeList.shift();
      if (finish && !code) {
        // 结束之前将未写入的代码补录进去
        await insertCode();
        // openDiff(); // 自动打开diff会中断生产流程，暂时关闭
        return;
      }
      if (code) {
        if (!/[A-Za-z0-9_]/.test(code.ch)) {
          // if (prevCode.length < 2) {
          prevCode.push(code);
        } else {
          await insertCode(code);
        }
      }
      // 修改的场景速度放慢一点，可以在插入之前去掉结尾的换行
      setTimeout(render, type == Commands.Edit ? 50 : 10);
    };
    render();
    const chatRenderText = await ask.questionCode(question, async (ch) => {
      if (!firstCh) {
        // 第一个字符前决定是否插入缩进
        firstCh = ch;
        const preIndent = selectedText.match(/^\s*/)?.[0];
        if (preIndent && firstCh && !firstCh.match(/^\s*/)?.[0]) {
          codeList.push({
            pos: new vscode.Position(line, character),
            ch: preIndent,
          });
          character += preIndent.length;
        }
      }

      codeList.push({
        pos: new vscode.Position(line, character),
        ch,
      });
      character++;
      if (ch == '\n') {
        line++;
        character = 0;
      }
    });
    if (chatRenderText) {
      // 去掉最后一个换行
      if (codeList[codeList.length - 1]?.ch == '\n') {
        codeList.pop();
      }
    }
    // 标记AI(代码修复)生成代码
    AdoptResultCache.setRemote(
      chatRenderText,
      getChatModelAndConfig().label,
      AdoptResultCache.ADOPT_CODE_SOURCE.INLINE_CHAT_EDIT
    );
    return chatRenderText;
  } finally {
    finish = true;
    // recover();
  }
}

export async function editCode({ editor, selection, question, selectedText, type }) {
  const disposable = vscode.window.setStatusBarMessage('正在生成编辑后的代码，请稍后...');
  const languageId = getCurrentFileType();
  const startTime = Date.now();
  try {
    const { start, end } = selection;
    const mergeList: string[] = [];
    mergeList.push('<<<<<<< 修改之前');
    mergeList.push(selectedText);
    mergeList.push('=======');
    mergeList.push('');
    mergeList.push('>>>>>>> 修改后的');
    const mergeCode = mergeList.join('\r\n');
    await editor.edit(async (editBuilder) => {
      await editBuilder.replace(new vscode.Range(start.line, 0, end.line, end.character), mergeCode);
    });
    const line = start.line + mergeCode.split('\n').length - 2;
    const chatRenderText = await batchRender({ editor, question, line, type, selectedText });
    reportAction({
      accept: 1,
      actionCate: 'ai',
      actionType: ActionType.editorChatGen,
      conversationId: uuidv4(),
      question,
      result: `
\`\`\`${languageId}
${chatRenderText}
\`\`\``,
      consumeTime: Date.now() - startTime,
    });
  } catch (e) {
    vscode.window.showErrorMessage(e.message || e);
  } finally {
    disposable.dispose();
  }
}

export async function createCode({ editor, question, type }) {
  const disposable = vscode.window.setStatusBarMessage('正在生成代码，请稍后...');
  const languageId = getCurrentFileType();
  const startTime = Date.now();
  try {
    const position = editor.selection.active;
    const line = position.line + 1;
    const chatRenderText = await batchRender({ editor, question, line, type, selectedText: '' });
    reportAction({
      accept: 1,
      actionCate: 'ai',
      actionType: ActionType.editorChatGen,
      conversationId: uuidv4(),
      question,
      result: `
\`\`\`${languageId}
${chatRenderText}
\`\`\``,
      consumeTime: Date.now() - startTime,
      extendMsg: {
        messageId: uniqueId(),
      },
    });
  } catch (e) {
    vscode.window.showErrorMessage(e.message || e);
  } finally {
    disposable.dispose();
  }
}

/**
 * 小灯泡逻辑
 */
class CursorCodeActionPrivider implements vscode.CodeActionProvider {
  public static readonly providedCodeActionKinds = [
    vscode.CodeActionKind.QuickFix,
    vscode.CodeActionKind.RefactorRewrite,
  ];

  public provideCodeActions(document: vscode.TextDocument, range: vscode.Range): vscode.CodeAction[] | undefined {
    const editor = vscode.window.activeTextEditor;
    if (!editor || editor.document !== document) {
      return;
    }

    let selection = editor.selection;
    let selectedText = editor.document.getText(selection);
    if (!selectedText) {
      // 未选中时默认指定为当前行（不一定是鼠标所在行，所以用provideCodeActions的range参数）
      const line = document.lineAt(range.start);
      selectedText = document.getText(line.range);
      selection = new vscode.Selection(line.range.start, line.range.end);
    }

    const createCommandAction = this.createCommand(
      `使用 JoyCoder 生成(${getQuickShort()})`,
      'JoyCoder.ai.inlineChat.create',
      vscode.CodeActionKind.RefactorRewrite,
      [UseMode.gen]
    );

    const chatCommandAction = this.createCommand(
      `使用 JoyCoder 问答(${getQuickShort()})`,
      'JoyCoder.ai.inlineChat.create',
      vscode.CodeActionKind.RefactorRewrite,
      [UseMode.chat]
    );
    const editCommandAction = this.createCommand(
      `使用 JoyCoder 编辑(${getQuickShort()})`,
      'JoyCoder.ai.inlineChat.create',
      vscode.CodeActionKind.RefactorRewrite,
      [UseMode.edit]
    );
    const fixCommandAction = this.createCommand(
      '使用 JoyCoder 修复',
      'JoyCoder.ai.inlineChat.quickCommand',
      vscode.CodeActionKind.QuickFix,
      [QuickCommand.fix, selection]
    );
    const explainErrorCommandAction = this.createCommand(
      '使用 JoyCoder 解释',
      'JoyCoder.ai.inlineChat.quickCommand',
      vscode.CodeActionKind.QuickFix,
      [QuickCommand.explainError, selection]
    );
    const commentCommandAction = this.createCommand(
      '使用 JoyCoder 注释',
      'JoyCoder.ai.inlineChat.quickCommand',
      vscode.CodeActionKind.RefactorRewrite,
      [QuickCommand.comment, selection]
    );

    const actionList: vscode.CodeAction[] = [];

    if (!selectedText) {
      // 所在行无代码
      actionList.push(createCommandAction, chatCommandAction);
    } else {
      actionList.push(
        chatCommandAction,
        editCommandAction,
        fixCommandAction,
        explainErrorCommandAction,
        commentCommandAction
      );
    }

    return actionList;
  }

  private createCommand(title, commandId, kind, commandArgs): vscode.CodeAction {
    const action = new vscode.CodeAction(title, kind);
    action.command = {
      command: commandId,
      title: title,
      arguments: commandArgs,
    };
    return action;
  }
}

export default function (context) {
  // 注册 CodeLens 提供程序
  context.subscriptions.push(vscode.languages.registerCodeLensProvider({ language: '*', scheme: '*' }, provider));
  /*
  context.subscriptions.push(
    // 选中发生变化后重新更新触发下CodeLens
    vscode.window.onDidChangeTextEditorSelection(
      debounce(() => {
        const selectText = getSelectedText();
        if (selectText?.length) {
          refreshEditor();
        }
      }, 1000)
    )
  );
  */
  context.subscriptions.push(
    vscode.languages.registerCodeActionsProvider('*', new CursorCodeActionPrivider(), {
      providedCodeActionKinds: CursorCodeActionPrivider.providedCodeActionKinds,
    })
  );
  context.subscriptions.push(vscode.commands.registerCommand(CommandId, commandHandle));
}

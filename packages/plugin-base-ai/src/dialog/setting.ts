import * as vscode from 'vscode';
import { GlobalState, setVscodeConfig } from '@joycoder/shared';
import CompletionCodeLensProvider from '@joycoder/plugin-base-code-completion/src/utils/codeLensProcider';

export async function setConfigInfo(data, isLeft) {
  switch (data.type) {
    case 'genTask':
      setVscodeConfig('JoyCoder.config.codeCompletionsGenTask', data.genTask);
      break;
    case 'completionDelay':
      GlobalState.update('JoyCoder.completion.delay', data.completionDelay);
      break;
    case 'completionsMoreContext':
      setVscodeConfig('JoyCoder.config.codeCompletionsMoreContext', data.completionsMoreContext);
      break;
    case 'commitCodeReview':
      setVscodeConfig('JoyCoder.config.codeReview.commit', data.commitCodeReview);
      break;
    case 'errorLine':
      setVscodeConfig('JoyCoder.config.codeReview.errorLine', data.errorLine);
      break;
    case 'isShenYi':
      setVscodeConfig('JoyCoder.config.shenYi.commit', data.isShenYi);
      break;
    case 'commitMessage':
      setVscodeConfig('JoyCoder.config.codeReview.commitMessageStyle', data.commitMessage);
      break;
    case 'codeLens':
      GlobalState.update('JoyCoder.codeLens.menuList', data.codeLens);
      const codeLensProvider = CompletionCodeLensProvider.vsCodeLensInstance;
      codeLensProvider?.disposeCodeLenses?.();
      codeLensProvider?.rerenderCodeLenses?.();
      break;
    case 'moreSetting':
      vscode.commands.executeCommand('JoyCoder.openConfigPage');
      break;
    case 'chatgpt-setting':
      vscode.commands.executeCommand('JoyCoder.config.setting', isLeft);
      break;
    case 'writeAction':
      GlobalState.update('diffEnabled', data.writeAction === 'diff');
      GlobalState.update('JoyCoder.autoCode.writeAction', data.writeAction);
      break;
    case 'autoApprovalSettings':
      GlobalState.update('autoApprovalSettings', data.autoApprovalSettings);
      vscode.commands.executeCommand('joycoder.autoCode.syncState');
      break;
    case 'customInstructions':
      GlobalState.update('customInstructions', data.customInstructions);
      vscode.commands.executeCommand('joycoder.autoCode.syncState');
      break;
    case 'createProjectRule':
      try {
        // 获取工作区路径
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
          throw new Error('未找到有效的工作区');
        }

        // 使用工作区URI来构建路径
        const ruleDir = vscode.Uri.joinPath(workspaceFolder.uri, '.joycode', 'rules');
        const ruleFile = vscode.Uri.joinPath(ruleDir, data.filePath.split('/').pop() || '');

        // 确保目录存在
        try {
          await vscode.workspace.fs.createDirectory(ruleDir);
        } catch (err) {
          // 如果创建目录失败，尝试在用户目录下创建
          const userHomeDir = vscode.Uri.joinPath(workspaceFolder.uri, '.joycoder-user', 'rules');
          await vscode.workspace.fs.createDirectory(userHomeDir);
          const ruleFile = vscode.Uri.joinPath(userHomeDir, data.filePath.split('/').pop() || '');
          await vscode.workspace.fs.writeFile(ruleFile, Buffer.from(data.ruleContent, 'utf-8'));
          vscode.window.showInformationMessage(`规则文件已创建在用户目录: ${ruleFile.fsPath}`);
          const document = await vscode.workspace.openTextDocument(ruleFile);
          await vscode.window.showTextDocument(document);
          break;
        }

        // 检查文件是否已存在
        try {
          await vscode.workspace.fs.stat(ruleFile);
          // 如果文件存在，显示提醒并打开现有文件
          vscode.window.showWarningMessage(`规则文件 ${data.filePath} 已存在，可直接在此文件内修改。`);
        } catch {
          // 如果文件不存在，创建新文件
          await vscode.workspace.fs.writeFile(ruleFile, Buffer.from(data.ruleContent, 'utf-8'));
          vscode.window.showInformationMessage(`规则文件 ${data.filePath} 创建成功`);
        }

        // 打开文件（无论是新创建的还是已存在的）
        const document = await vscode.workspace.openTextDocument(ruleFile);
        await vscode.window.showTextDocument(document);
      } catch (error) {
        vscode.window.showErrorMessage(`操作规则文件失败: ${error.message}`);
      }
      break;
    // case 'codebaseIndexing':
    //   const action = data.action;
    //   console.log('codebaseIndexing', action);
    //   const postCodebaseIndexingProcess = (codebaseProgress: CodebaseProgress) => {
    //     console.log('postCodebaseIndexingProcess', codebaseProgress);
    //     postMessageToWebview({
    //       type: 'COMMON',
    //       payload: {
    //         type: 'codebase-update-progress',
    //         data: {
    //           codebaseIndexingProgress: codebaseProgress.progress,
    //           codebaseIndexingStatus: codebaseProgress.status,
    //         },
    //         isLeft: true,
    //       },
    //     });
    //   };
    //   switch (action) {
    //     case 'start':
    //       postCodebaseIndexingProcess({
    //         progress: 0,
    //         status: CodebaseIndexingStatus.PREPARING,
    //       });
    //       await startIndex();
    //       getProgressCallback({
    //         onProgressChange: postCodebaseIndexingProcess,
    //         onComplete: postCodebaseIndexingProcess,
    //       });
    //       break;
    //     case 'cancel':
    //       await cancelIndex();
    //       // 其实cancel了进度还在，已经索引的东西也没删，但是就处理成进展归零了
    //       postCodebaseIndexingProcess({
    //         progress: 0,
    //         status: CodebaseIndexingStatus.UNINDEXED,
    //       });
    //       break;
    //     case 'remove':
    //       await removeIndex();
    //       postCodebaseIndexingProcess({
    //         progress: 0,
    //         status: CodebaseIndexingStatus.UNINDEXED,
    //       });
    //       break;
    //     case 'getProgress':
    //       getProgressCallback({
    //         onProgressChange: postCodebaseIndexingProcess,
    //         onComplete: postCodebaseIndexingProcess,
    //       });
    //       break;
    //     default:
    //       // const progress = await getProgress();
    //       // console.log('获取当前工作目录索引状态为', progress);
    //       // 测试用的，点击按钮触发一些我想执行的东西
    //       const result = await codebaseSearch('状态机的状态转化是如何实现的');
    //       console.log('LOOK AT ME!!!', result);
    //       break;
    //   }
    //   break;
    case 'chatgpt-setting-error':
      vscode.window.showErrorMessage(data.message || '发生未知错误');
      break;
    default:
      break;
  }
}

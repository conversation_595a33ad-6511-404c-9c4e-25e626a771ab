import * as vscode from 'vscode';
import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';
import {
  getCurrentFileType,
  getMarkdownCodeBlockContent,
  Logger,
  ActionType,
  startReportAction,
  Choice,
} from '@joycoder/shared';
import { ChatProxyService, Scene } from '../proxy';
import { setDecorationType } from './index';
import { CodeLensMenuChatType, constant } from './constant';
import { getChatModelAndConfig } from '../model';

let lsFnReconstructionLoading = false;
let disposable: vscode.Disposable | null = null;
let removeCodeLensDisposable: vscode.Disposable | null = null;

/**
 *  代码重构实现。
 * @param data 包含函数内容的数据对象
 * @param endLine 结束行的位置信息
 */
export async function codelensReconstruction(data, endLine) {
  if (lsFnReconstructionLoading) {
    return;
  }
  const code = data?.functionContent;
  if (!code) {
    return;
  }
  const errorMes = '代码重构失败，请检查代码！！';
  // const startTime = Date.now();
  let text = '';
  let decorationType: vscode.Disposable | vscode.TextEditorDecorationType = new vscode.Disposable(() => {});
  try {
    lsFnReconstructionLoading = true;
    const abortController = new AbortController();
    decorationType = setDecorationType(data, '代码重构思考中...');
    setDelLoading(decorationType, abortController);
    const model = getChatModelAndConfig().label;
    const editor = vscode.window.activeTextEditor;
    const languageId = getCurrentFileType();
    const transformText = constant(CodeLensMenuChatType.codelensReconstruction);
    const message = `${transformText}: \n\`\`\`${languageId}\n\n${code} \n\`\`\``;
    const result = await new ChatProxyService().invoke(
      {
        message,
        scene: Scene.CodeLens,
        respMaxTokens: 2500,
      },
      {
        abortController,
        onProgress: () => {},
      }
    );
    text = result.fullText ?? '';
    const reporter = setReportLog(
      message,
      model,
      '0.1',
      [
        {
          completionCode: text,
          index: 0,
        },
      ],
      result.conversationId,
      'CodeRefactoring'
    );
    const mergeList: string[] = [];
    try {
      const codeMatch = text.match(/```(\w+)?\s*([\s\S]*?)\s*```/);
      text = codeMatch?.[2] ?? text;
      text = text.includes('```') ? getMarkdownCodeBlockContent(text) : text;
    } catch (error) {
      text = '';
      Logger.error('%c [ error ]-901', 'font-size:13px; background:pink; color:#bf2c9f;', '行间菜单去```失败', error);
    }
    if (editor && text) {
      mergeList.push('<<<<<<< 修改之前');
      mergeList.push(code);
      mergeList.push('=======');
      mergeList.push(text);
      mergeList.push('>>>>>>> 修改后的');
      // 函数注释结果结果缓存，用于后续AI代码占比统计
      AdoptResultCache.setRemote(
        text,
        model,
        AdoptResultCache.ADOPT_CODE_SOURCE.CODE_RECONSTRUCTION,
        result.conversationId
      );
      const mergeCode = mergeList.join('\r\n');
      const endCharacter = endLine.range.end.character;
      await editor.edit(async (editBuilder) => {
        await editBuilder.replace(new vscode.Range(data.lineNumber, 0, endLine.lineNumber, endCharacter), mergeCode);
      });
      disposable && disposable.dispose();
      disposable = vscode.workspace.onDidChangeTextDocument(async (event) => {
        const editor = vscode.window.activeTextEditor;
        if (editor && event.document === editor.document) {
          const changes = event.contentChanges;
          const lastChange = changes[changes.length - 1];
          const insertedText = lastChange?.text;
          if (insertedText.includes(text)) {
            reporter && reporter.success({ result: text, acceptIndex: 0 }, () => {});
          } else {
            reporter && reporter.fail({ result: text });
          }
        }
      });
      // await editor.insertSnippet(new vscode.SnippetString(mergeCode), new vscode.Position(data.lineNumber, 0));
    }
    decorationType && decorationType.dispose();
    lsFnReconstructionLoading = false;
  } catch (e) {
    vscode.window.showErrorMessage(e.message || errorMes);
    decorationType && decorationType.dispose();
    disposable && disposable.dispose();
    lsFnReconstructionLoading = false;
  }
}

/**
 * 生成逐行注释
 * @param data 包含函数内容和行号等信息的数据对象
 * @param endLine 结束行对象
 */
export async function codelensLineComment(data, endLine) {
  if (lsFnReconstructionLoading) {
    return;
  }
  const code = data?.functionContent;
  if (!code) {
    return;
  }
  let text = '';
  let decorationType: vscode.Disposable | vscode.TextEditorDecorationType = new vscode.Disposable(() => {});
  const errorMes = '逐行注释生成失败，请检查代码！！';
  try {
    lsFnReconstructionLoading = true;
    const abortController = new AbortController();
    decorationType = setDecorationType(data, '逐行注释生成中...');
    setDelLoading(decorationType, abortController);
    const model = getChatModelAndConfig().label;
    const editor = vscode.window.activeTextEditor;
    const languageId = getCurrentFileType();
    const transformText = constant(CodeLensMenuChatType.codelensLineComment);
    const message = `${transformText}: \n\`\`\`${languageId}\n\n${code} \n\`\`\``;
    const result = await new ChatProxyService().invoke(
      {
        message,
        scene: Scene.CodeLens,
        respMaxTokens: 2500,
      },
      {
        abortController,
        onProgress: () => {},
      }
    );
    text = result.fullText ?? '';
    const reporter = setReportLog(
      message,
      model,
      '0.1',
      [
        {
          completionCode: text,
          index: 0,
        },
      ],
      result.conversationId,
      'LbLcomments'
    );
    try {
      const codeMatch = text.match(/```(\w+)?\s*([\s\S]*?)\s*```/);
      text = codeMatch?.[2] ?? text;
      text = text.includes('```') ? getMarkdownCodeBlockContent(text) : text;
    } catch (error) {
      text = '';
      Logger.error('%c [ error ]-901', 'font-size:13px; background:pink; color:#bf2c9f;', '行间菜单去```失败', error);
    }
    if (editor && text) {
      // 函数注释结果结果缓存，用于后续AI代码占比统计
      const endCharacter = endLine.range.end.character;
      await editor.edit(async (editBuilder) => {
        await editBuilder.replace(new vscode.Range(data.lineNumber, 0, endLine.lineNumber, endCharacter), text);
      });
      reporter && reporter.success({ result: text, acceptIndex: 0 }, () => {});
      AdoptResultCache.setRemote(text, model, AdoptResultCache.ADOPT_CODE_SOURCE.LINE_COMMENT, result.conversationId);
      // await editor.insertSnippet(new vscode.SnippetString(mergeCode), new vscode.Position(data.lineNumber, 0));
    } else if (!text) {
      reporter && reporter.fail({ result: text }, () => {});
    }
    decorationType && decorationType.dispose();
    lsFnReconstructionLoading = false;
  } catch (e) {
    vscode.window.showErrorMessage(e.message || errorMes);
    decorationType && decorationType.dispose();
    lsFnReconstructionLoading = false;
  }
}
export function setReportLog(
  question: string,
  model: string,
  temperature: string,
  choices: Choice[],
  conversationId: string,
  action_type?: string,
  nextCursor?: string,
  prevCursor?: string,
  cursorPosition?: vscode.Position,
  lang?: string
) {
  try {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;
    const reporter = startReportAction({
      actionCate: 'ai',
      actionType: ActionType.completion,
      question,
      model,
      // @completion专用 代码语言
      codeLanguage: lang ?? editor.document.languageId,
      // @completion专用 温度
      temperature,
      // @completion专用 光标位置
      offset: editor?.document.offsetAt(cursorPosition ?? editor.selection.active),
      // @completion专用 前缀代码
      prefixCode: prevCursor,
      // @completion专用 后缀代码
      sufixCode: nextCursor,
      // @completion专用 采纳选项数据，json串
      choices,
      // 将completionId当做conversationId
      conversationId,
      extendMsg: {
        action_type: action_type ?? 'CodeRefactoring',
      },
    });

    return reporter;
    // insertText && reporter.success({ result: insertText, acceptIndex: 0 }, () => {});
    // reporter.fail({ result: insertText }, () => {});
  } catch (error) {
    Logger.error('%c [ error ]-417', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
  return null;
}

/**
 * 设置删除加载状态的命令。
 * @param decorationType - 要处理的装饰类型。
 * @param abortController - 控制中止操作的控制器。
 */
export function setDelLoading(decorationType, abortController, cb?: () => void) {
  removeCodeLensDisposable && removeCodeLensDisposable.dispose();
  removeCodeLensDisposable = vscode.commands.registerCommand('JoyCoder.codelens.del.loading', (_data) => {
    if (abortController) {
      abortController.abort();
    }
    lsFnReconstructionLoading = false;
    decorationType && decorationType.dispose();
    cb && cb();
  });
}

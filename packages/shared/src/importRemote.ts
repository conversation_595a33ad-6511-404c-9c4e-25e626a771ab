import path from 'path';
import fs from 'fs';
import os from 'os';
import axios from 'axios';
import AdmZ<PERSON> from 'adm-zip';
import to from 'await-to-js';
import { downloadFile } from './download';
import { deleteFolderRecursive, getGlobalPath } from './path';
import { safeExecSync } from './exec';
import { getJdhCgiUrl, isIDE } from './business';
import { getBaseUrl } from './';
import { Logger } from './logger';
import { decompressTarGzFile } from './file';
import { modelUrl } from './constantData';

interface RemoteBinaryConfig {
  [key: string]: string;
  name: string;
  version: string;
}

const globalPath = getGlobalPath();
const localBinaryDir = path.join(globalPath, isIDE() ? './remote_binary_ide' : './remote_binary');
const platfrom = os.platform();
const arch = os.arch();

const nodeABI = `electron-v${process.versions.modules}`;

// 兜底的本地配置信息
const BINARY_CONFIG_MAP: Record<string, RemoteBinaryConfig> = {
  vectordb: {
    name: 'vectordb',
    version: '4.20.0',
    'vectordb-darwin-arm': 'https://aichat.s3.cn-north-1.jdcloud-oss.com/joycoder/<EMAIL>',
    'vectordb-darwin-x64': 'https://aichat.s3.cn-north-1.jdcloud-oss.com/joycoder/<EMAIL>',
    'vectordb-linux-x64': 'https://aichat.s3.cn-north-1.jdcloud-oss.com/joycoder/<EMAIL>',
    'vectordb-win32-x64': 'https://aichat.s3-internal.cn-north-1.jdcloud-oss.com/joycoder/<EMAIL>',
  },
};

/**
 * Require a module from the remote server.
 *
 * <AUTHOR>
 * @param {string} module
 * @return {*}
 */
export async function importRemote<T>(module: string): Promise<T> {
  const localFilePath = await ensureRemoteBinaryFile(module);

  return import(localFilePath);
}

/**
 * 确保远程二进制文件存在于本地
 * @param module 模块名称
 * @returns 本地文件路径
 * @throws 模块未找到错误
 */
export async function ensureRemoteBinaryFile(module: string) {
  const config = await getModuleConfig(module);
  Logger.info(`[${module}] 配置信息:`, config);

  const targetNameRegexp = new RegExp(`${platfrom}-${arch}$`);
  const targetNameNone64Regexp = new RegExp(`${platfrom}-${arch.replace(/64$/, '')}$`);
  let baseFileName =
    Object.keys(config).find(
      (baseNameOrVerStr) => targetNameRegexp.test(baseNameOrVerStr) || targetNameNone64Regexp.test(baseNameOrVerStr)
    ) || '';

  if (!baseFileName && config.baseFileNameTpl) {
    baseFileName = config.baseFileNameTpl
      .replace('{{nodeABI}}', nodeABI)
      .replace('{{platform}}', platfrom)
      .replace('{{arch}}', arch);
    config[baseFileName] = config.downloadFileUrlTpl
      .replace('{{nodeABI}}', nodeABI)
      .replace('{{platform}}', platfrom)
      .replace('{{arch}}', arch);
  }
  if (!baseFileName) {
    Logger.error(`[${module}] 不支持的系统或架构: ${platfrom}-${arch}`);
    return '';
  }

  let localFileName = baseFileName;
  if (config.version) localFileName += `@${config.version}`;
  let localFilePath = path.join(localBinaryDir, module, localFileName);

  // 判断本地本间是否存在
  const localFile = module === 'joycoder_rag' ? path.join(localFilePath + '.zip') : localFilePath;
  let isShowDownload =
    !fs.existsSync(localFile) || (!fs.existsSync(localFile.replace(/\.zip$/, '')) && module !== 'joycoder_rag');
  if (!isShowDownload) {
    const localFileStat = fs.statSync(localFile);
    if (localFileStat.size === 0) {
      isShowDownload = true;
    }
  }

  if (isShowDownload) {
    const downloadFileUrl = config[baseFileName];
    if (!downloadFileUrl) throw new Error(`remote file url not found for module ${module}`);

    const parsedRemoteFileUrl = new URL(downloadFileUrl);
    const downloadFileName = path.basename(parsedRemoteFileUrl.pathname);
    const downloadFileDir = path.dirname(localFilePath);
    const downloadFilePath = path.join(downloadFileDir, downloadFileName);

    async function extractFile() {
      if (downloadFilePath.endsWith('.zip')) {
        const zip = new AdmZip(downloadFilePath);
        zip.extractAllTo(downloadFileDir, true);

        if (module !== 'joycoder_rag') {
          const files = fs.readdirSync(downloadFileDir);
          const hasLocalFilePath = files.some((file) => file === path.basename(localFilePath));
          if (!hasLocalFilePath && files[0]) {
            const renameFilePath = path.join(downloadFileDir, files[0]);
            fs.renameSync(renameFilePath, localFilePath);
            Logger.error(`[${module}] zip包中没有 ${path.basename(localFilePath)}，重命名文件: ${files[0]}`);
          }
        }
      } else if (downloadFilePath.endsWith('.gz')) {
        await decompressTarGzFile(downloadFilePath, downloadFileDir);

        if (module !== 'joycoder_rag') {
          const files = fs.readdirSync(downloadFileDir);
          const hasLocalFilePath = files.some((file) => file === path.basename(localFilePath));
          if (!hasLocalFilePath) {
            const binaryDir = path.join(downloadFileDir, './build/Release');
            const binaryFiles = fs.readdirSync(binaryDir);
            const binaryFile = binaryFiles.find((file) => file.endsWith('.node'));
            if (binaryFile) {
              const renameFilePath = path.join(binaryDir, binaryFile);
              fs.renameSync(renameFilePath, localFilePath);
              fs.copyFileSync(localFilePath, `${localFilePath}.node`);
              deleteFolderRecursive(binaryDir);
              Logger.error(`[${module}] gz包中没有 ${path.basename(localFilePath)}，重命名文件: ${renameFilePath}`);
            }
          }
        }
      }
    }

    try {
      Logger.info(
        `[${module}] 开始下载远程文件, url: ${downloadFileUrl}, dir: ${downloadFileDir}, name: ${downloadFileName}`
      );

      // 使用 try-catch 包装 downloadFile
      try {
        await downloadFile(downloadFileUrl, downloadFileDir, downloadFileName);
      } catch (downloadError) {
        Logger.error(`[${module}] 下载文件失败:`, downloadError);
        throw downloadError; // 重新抛出错误以触发外层错误处理
      }

      // 验证文件是否真的下载成功
      const downloadedFilePath = path.join(downloadFileDir, downloadFileName);
      if (!fs.existsSync(downloadedFilePath)) {
        throw new Error(`[${module}] 下载文件未找到: ${downloadedFilePath}`);
      }

      // 确保目标目录存在
      await fs.promises.mkdir(downloadFileDir, { recursive: true });

      // 删除目录下的其它文件
      fs.readdirSync(downloadFileDir).forEach((file) => {
        if (file !== downloadFileName) {
          const currentPath = path.join(downloadFileDir, file);
          if (fs.lstatSync(currentPath).isDirectory()) {
            deleteFolderRecursive(currentPath);
          } else {
            fs.unlinkSync(currentPath);
          }
        }
      });

      // 下载完成后立即解压
      await extractFile();
    } catch (error) {
      Logger.error(`[${module}] 发生错误:`, error);

      // 检查是否需要进行备份
      if (fs.existsSync(localFilePath)) {
        let currentExecPathBak = localFilePath.substring(0, localFilePath.length - 4) + '.bak.exe';
        while (fs.existsSync(currentExecPathBak)) {
          currentExecPathBak = currentExecPathBak.substring(0, currentExecPathBak.length - 4) + '.bak.exe';
        }
        fs.renameSync(localFilePath, currentExecPathBak);
      }

      // 重试下载和解压
      try {
        await downloadFile(downloadFileUrl, downloadFileDir, downloadFileName);
        await extractFile();
      } catch (retryError) {
        Logger.error(`[${module}] 重试失败:`, retryError);
        throw retryError;
      }
    }
  }
  localFilePath = module === 'joycoder_rag' ? localFilePath.replace(localFileName, '') : localFilePath;
  Logger.info(`[${module}] 远程二进制文件已准备就绪: ${localFilePath}`);
  return localFilePath;
}

/**
 * 使指定路径的文件可执行
 * @param executePath 要设置可执行权限的文件路径
 * @async
 */
export async function makeExecutable(executePath: string) {
  if (os.platform() !== 'win32') {
    await safeExecSync(`chmod +rx "${executePath}"`);
  }
}

/**
 * 获取指定模块的二进制配置
 * @param moduleName 模块名称
 * @returns 模块的二进制配置，如果不存在则返回null
 */
async function getModuleConfig(moduleName: string): Promise<RemoteBinaryConfig | any> {
  let config = BINARY_CONFIG_MAP[moduleName];
  if (!config) {
    const remote_config = await getModuleConfigFromRemote(moduleName);
    if (!remote_config) {
      throw new Error(`module ${module} not found`);
    }
    config = remote_config;
  }
  return config;
}

/**
 * 从JDH统一配置平台拉取远程模块配置
 *
 * @param {string} moduleName
 * @return {*}
 */
async function getModuleConfigFromRemote(moduleName: string): Promise<RemoteBinaryConfig | any> {
  const pluginRunBaseConfigUrl = isIDE()
    ? getBaseUrl() + `/api/saas/models/v1/config/getPluginRunBaseConfig`
    : `${modelUrl}/config/getPluginRunBaseConfig`;
  const [, response] = await to(
    axios.post(getJdhCgiUrl(pluginRunBaseConfigUrl), {
      sceneType: moduleName,
    })
  );
  // 获取配置数据路径
  const configPath = isIDE() ? response?.data?.data : response?.data;
  if (!response || !configPath || !configPath[moduleName]) {
    return null;
  }

  // 获取模块配置
  const moduleConfig = configPath[moduleName];

  // 如果配置是字符串类型，直接返回
  if (typeof moduleConfig === 'string') {
    return moduleConfig;
  }

  // 如果是对象类型，添加name属性后返回
  return {
    ...moduleConfig,
    name: moduleName,
  };
}

/**
 * 清除本地二进制文件目录。
 */
export function clearLocalBinaryFile() {
  deleteFolderRecursive(localBinaryDir);
}

export async function getFeedbackConfig() {
  const config = await getModuleConfigFromRemote('bug_report_type');
  return config;
}
export async function getIhubConfig(): Promise<string> {
  try {
    const config: any = await getModuleConfigFromRemote('ihub_rule_url');
    return config;
  } catch (error) {
    console.error('%c [ error ]-239', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
  return '';
}

export type IExtendMsg = object;

export interface Choice {
  // 补全的代码
  completionCode: string;
  // 补全的选项下标
  index: number;
}

export type ActionCate = 'ai' | 'cli' | 'tool' | 'sec';

export enum ActionType {
  // 常规聊天
  chatQ = 'chatQ',
  // 代码补全
  completion = 'completion',
  // 开启代码补全
  completionOpen = 'completionOpen',
  // 关闭代码补全
  completionClose = 'completionClose',
  // 代码区自动生成（暂无）
  autoEditor = 'autoEditor',
  // 代码区对话生成代码（前端版独占、类似GithubColpitInlineChat）
  editorChatGen = 'editorChatGen',
  // 复制代码功能
  copy = 'copy',
  // 复制结果功能
  aCopy = 'a_copy',
  // 复制提问功能
  qCopy = 'q_copy',
  // 替换功能
  replace = 'replace',
  // 删除功能（暂无）
  delete = 'delete',
  // 重新生成
  regenerate = 'regenerate',
  // 自定义指令
  customEnum = 'CustomEnum',
  // 解释代码
  explainCodeEnum = 'ExplainCodeEnum',
  // 代码评审
  codeReviewEnum = 'CodeReviewEnum',
  // 代码评审
  codeReviewOneFileEnum = 'CodeReviewOneFileEnum',
  // 生成注释（暂无）
  generateCommentEnum = 'GenerateCommentEnum',
  // 代码优化
  codeOptimizationEnum = 'CodeOptimizationEnum',
  // 生成单元测试
  generateUnitTestsEnum = 'GenerateUnitTestsEnum',
  // 自定义配置指令（暂无）
  customeDefineEnum = 'CustomeDefineEnum',
  // 安全检查（暂无）
  securityCheckEnum = 'SecurityCheckEnum',
  // 性能检查（暂无）
  performanceCheckEnum = 'PerformanceCheckEnum',
  // 接口文档生成
  docGenerateEnum = 'DocGenerateEnum',
  // 生成mock单元测试（暂无）
  mockTestGenerateEnum = 'MockTestGenerateEnum',
  // 日志解读（暂无）
  assistExplainEnum = 'ASSISTEXPLAINENUM',
  // 问题修复（暂无）
  assistRepairEnum = 'ASSISTREPAIRENUM',
  // 生成函数注释
  autoJavaDocGen = 'autoJavaDocGen',
  // 翻译代码（前端版独占）
  codeTranslateEnum = 'CodeTranslateEnum',
  // 批量翻译代码（前端版独占）
  batchCodeTranslateEnum = 'BatchCodeTranslateEnum',
  // 批量代码评审（前端版独占）
  batchCodeReviewEnum = 'batchCodeReviewEnum',
  // 主动代码评审（前端版独占）
  activeCodeReviewEnum = 'activeCodeReviewEnum',
  // 增量（commit）代码评审（前端版独占）
  commitCodeReviewEnum = 'commitCodeReviewEnum',
  // 神医大模型修复
  codeFix = 'CodeFix',
  // 神医大模型自动修复场景
  codeFixChat = 'CodeFixChat',
  // Agent个性助手
  customhelper = 'AgentCustomHelper',
  // Agent图片转代码
  pic2code = 'AgentPic2Code',
  // Agent D2C
  d2c = 'D2C',
  // Agent联网搜索
  search2llm = 'AgentSearch',
  // 神医大模型修复
  webcrawler = 'AgentWebcrawler',
  // Agent知识库
  knowledge = 'AgentKnowledge',
  // 终端右击解释
  terminalExplain = 'TerminalExplain',
  // 文字转图表
  text2chart = 'Text2chart',
  //多Agent
  multiAgent = 'MultiAgent',
  // 生commitMessage
  generateCommitMessageEnum = 'GenerateCommitMessageEnum',
  // 神医编辑区发起右键检测
  eRightKey = 'E_RIGHTKEY',
  // 神医文件区发起右键检测
  fRightKey = 'F_RIGHTKEY',
  // 神医增量检测发起
  commitCheck = 'CIOMMITCHECK_v',
  // 神医检测发起并且检测到问题(针对本次检测的全部文件)
  commitFind = 'CIOMMITFIND_v',
  // 增量检测发起并且检测到问题(针对单个文件)
  commitFindOne = 'CIOMMITFIND_ONE_v',
  // 神医增量检测修复
  commitFix = 'CIOMMITFIX_v',
  // 点赞点踩
  like = 'like',
  Repository = '@repo',
  // 通天塔
  IHub = 'IHub',
  // 自动化编程
  AutoGenerator = 'agent_generator',
  // 联网搜索
  WebSearch = 'web_search',
  // Codebase
  Codebase = 'codebase',
  // MCP
  MCP = 'mcp',
}

export enum ActionJDHType {
  // 问答聊天
  chatQ = 'chatQ',
  // 代码补全
  completion = 'completion',
  // 开启代码补全
  completionOpen = 'completionOpen',
  // 关闭代码补全
  completionClose = 'completionClose',
  // 代码区自动生成
  autoEditor = 'autoEditor',
  // 代码区对话生成代码（前端版独占、类似GithubColpitInlineChat）
  editorChatGen = 'editorChatGen',
  // 复制代码功能
  copy = 'copy',
  // 复制结果功能
  aCopy = 'a_copy',
  // 复制提问功能
  qCopy = 'q_copy',
  // 替换功能
  replace = 'replace',
  // 删除功能
  delete = 'delete',
  // 重新生成
  regenerate = 'regenerate',
  // 自定义指令
  customEnum = 'CustomEnum',
  // 解释代码
  explainCodeEnum = 'ExplainCodeEnum',
  // 代码评审
  CodeReviewEnum = 'CodeReviewEnum',
  // 生成注释
  generateCommentEnum = 'GenerateCommentEnum',
  // 代码优化
  codeOptimizationEnum = 'CodeOptimizationEnum',
  // 生成单元测试
  generateUnitTestsEnum = 'GenerateUnitTestsEnum',
  // 自定义配置指令
  customeDefineEnum = 'CustomeDefineEnum',
  // 安全检查
  securityCheckEnum = 'SecurityCheckEnum',
  // 性能检查
  performanceCheckEnum = 'PerformanceCheckEnum',
  // 接口文档生成
  docGenerateEnum = 'DocGenerateEnum',
  // 生成mock单元测试
  mockTestGenerateEnum = 'MockTestGenerateEnum',
  // 日志解读
  assistExplainEnum = 'ASSISTEXPLAINENUM',
  // 问题修复
  assistRepairEnum = 'ASSISTREPAIRENUM',
  // 生成函数注释
  autoJavaDocGen = 'autoJavaDocGen',
  // 翻译代码（前端版独占）
  codeTranslateEnum = 'CodeTranslateEnum',
  // 批量翻译代码（前端版独占）
  batchCodeTranslateEnum = 'BatchCodeTranslateEnum',
  // 批量代码评审（前端版独占）
  batchCodeReviewEnum = 'batchCodeReviewEnum',
  // 主动代码评审（前端版独占）
  activeCodeReviewEnum = 'activeCodeReviewEnum',
  // 增量（commit）代码评审（前端版独占）
  commitCodeReviewEnum = 'commitCodeReviewEnum',
  // 神医大模型修复
  codeFix = 'CodeFix',
  // 神医大模型自动修复场景
  codeFixChat = 'CodeFixChat',
  // 点赞点踩
  like = 'like',
  // 通天塔
  IHub = 'IHub',
  // 自动化编程
  AutoGenerator = 'agent_generator',
}

export interface IActionBaseReportParam {
  // 操作系统环境
  osName: string;
  // IDE版本号
  ideVersion: string;
  // 插件版本号
  pluginVersion: string;
  // ERP信息
  loginErp: string;
  // 部门ID
  loginOrgId: string;
  // 注册的团队ID
  workspaceId: string;
  // 计算机名称
  computerName: string;
  // 计算机域名
  computerDomain: string;
  // 计算机域名
  mac: string;
  // 项目名称
  projectName: string;
  // 当前文件路径
  curFilePath: string;
}

export interface IActionCustomReportParam {
  // 操作分类
  actionCate: ActionCate;
  // 操作类型
  actionType: ActionType;
  // 模型名称
  model?: string;
  // @completion专用 代码语言
  codeLanguage?: string;
  // @completion专用 温度
  temperature?: string;
  // @completion专用 光标位置
  offset?: number;
  // @completion专用 前缀代码
  prefixCode?: string;
  // @completion专用 后缀代码
  sufixCode?: string;
  // @completion专用 采纳选项数据，json串
  choices?: Choice[];
  // @completion专用 采纳的下标
  acceptIndex?: number;
  // 输入的完整GPT提示词或其它前端功能的输入
  question?: string;
  // 结果，GPT预测结果或者其他操作的结果
  result?: string;
  // GPT推荐的代码行数
  resultCodeLine?: number;
  // 用户采纳的代码行数
  acceptCodeLine?: number;
  // 操作开始时间
  startTime?: Date;
  // 操作整体耗时
  consumeTime?: number;
  // 用户是否采纳
  accept?: number;
  // 会话ID
  conversationId?: string;
  // 代码评审Diff
  diffContent?: string;
  // 代码评审 源文件
  originContent?: string;
  // 扩展上报信息
  extendMsg?: IExtendMsg;
}

export type IActionReportParam = IActionBaseReportParam & IActionCustomReportParam;

interface IJDHBaseReportParam {
  // 操作系统环境（可选）
  osName?: string;
  // 研发ide版本号，例如1.83.1或者IC-2022.3.365
  ideaVersion: string;
  // 插件版本号
  // jetbrains系列版本号规则：数字.数字.数字（例如：2.3.1）
  // vsCode版本号规则：vsfront-数字.数字.数字（例如：vsfront-1.2.1）
  // vsCode AI专业版版本号规则：vscode-数字.数字.数字（例如：vscode-1.2.1）
  pluginVersion: string;
  // 计算机名称（可选）
  computerName?: string;
  // 计算机域名（可选）
  computerDomain?: string;
  // ERP信息（可选）
  loginErp?: string;
  // 用户名，登录接口3.插件登录接口中入参的userName字段
  user: string;
  // 用户Token，登录接口3.插件登录接口中获取登录结果中的userToken值
  userToken: string;
  // 项目名称（可选）
  projectName?: string;
  // 当前文件名（可选）
  curFileName?: string;
  // 当前文件路径（可选）
  curFilePath?: string;
}

interface IJDHCustomReportParam {
  // 发送类型
  actionType: string;
  // 输入的完整GPT提示词或其它前端功能的输入
  question: string;
  // 结果，聊天问答结果或者其他操作的结果（可选）
  result?: string;
  // 开始时间（可选）
  startTime?: number;
  // 耗时（可选）
  consumeTime?: number;
  // 结束时间（可选）
  endTime?: number;
  // 会话ID（可选）
  conversationId?: string;
  // 模型名称
  model?: string;
  // GPT推荐的代码行数
  // resultCodeLine?: number;
  // 用户采纳的代码行数
  // acceptCodeLine?: number;
  // 用户是否采纳
  // accept?: number;
  // 扩展上报信息
  extendMsg?: IExtendMsg;
  // 指定Token（可选）
  tokenAssert?: string;
}

export type IJoyCoderReportParam = IJDHBaseReportParam & IJDHCustomReportParam;

export type ISecReportParam = {
  type: string;
  vulnId?: number;
};

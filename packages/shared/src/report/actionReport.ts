import * as vscode from 'vscode';
import axios from 'axios';
import * as os from 'os';
import moment from 'moment';
import { getJdhLoginInfo } from '../loginJdh';
import { getSsoLoginInfo } from '../login';
import { getVscodeConfig } from '../config';
import { safeExecSync } from '../exec';
import to from 'await-to-js';
import { convertActionReportToCompletionReport, sendReportToCompletion } from './completionReport';
import {
  ActionType,
  IActionBaseReportParam,
  IActionCustomReportParam,
  IActionReportParam,
  IJoyCoderReportParam,
  ISecReportParam,
} from './ationType';
import { pluginVersion, ideVersion } from '../pluginId';
import { getJdhCgiUrl, isBusiness, isIDE } from '../business';
import { getJoyCoderVersion } from '../config/globalConfig';
import { getBaseUrl } from '../';
import { GitStateManager } from '../git';
import { v4 as uuidv4 } from 'uuid';
import { getExtHeaders } from '@joycoder/agent-common/src/vscode/index';

export const userName = os.userInfo().username || '';
const osName = `${os.platform()} ${os.arch()} ${os.release()}`;
const computerName = os.hostname();
let computerDomain = '';
let mac = '';
const networkInterfaces = os.networkInterfaces();
Object.keys(networkInterfaces).forEach((ifname) => {
  if (networkInterfaces[ifname] && networkInterfaces[ifname]?.length) {
    networkInterfaces[ifname]?.map((iface) => {
      // Skip over internal (i.e. 127.0.0.1) and non-IPv4 addresses
      if ('IPv4' !== iface.family || iface.internal !== false) {
        return false;
      }
      computerDomain = iface.address;
      mac = iface.mac;
      return true;
    });
  }
});
if (os.platform() === 'win32') {
  computerDomain = safeExecSync('echo %USERDOMAIN%') || computerDomain || '';
}

/**
 * 读取上报公共字段
 *
 * @return {*}  {IActionBaseReportParam}
 */
export function getActionBaseParam(): IActionBaseReportParam {
  // 获取当前打开的文件夹的URI
  const workspaceFolders = vscode.workspace.workspaceFolders;
  // 只获取第一个工作区文件夹的名称，如果有多个工作区文件夹，可能需要其他逻辑来处理
  const projectName = workspaceFolders?.[0]?.name || '';
  const curFilePath = vscode.window.activeTextEditor?.document.uri.fsPath || '';

  // 填充公共字段
  const ssoLoginInfo = getSsoLoginInfo();
  const report: IActionBaseReportParam = {
    osName,
    ideVersion: isIDE() ? getJoyCoderVersion() : ideVersion,
    pluginVersion,
    computerName,
    computerDomain,
    mac,
    loginErp: getJdhLoginInfo()?.erp || '',
    loginOrgId: ssoLoginInfo?.orgId || '',
    workspaceId: getVscodeConfig('JoyCoder.config.workSpaceId'),
    projectName,
    curFilePath,
  };
  return report;
}

/**
 * 上报用户行为
 *
 * @example
 * ```ts
 * // 上报单次行为（图片上传功能）
 * reportAction({ actionCate: 'tool', actionType: 'unplodImage', result: 'https://img10.360buyimg.com/xxx', resultCodeLine: 1, acceptCodeLine: 1, });
 *
 * // 上报操作过程
 * reportAction({ actionCate: 'ai', actionType: 'completion', quesiton: 'code', result: 'xxxx', accept: 1, consumeTime: 100 }); // 开始上报
 * ```
 *
 * @export
 * @param {IActionCustomReportParam} reportData
 */
export function reportAction(reportData: IActionCustomReportParam) {
  let messageId: any = (reportData?.extendMsg as any)?.messageId;
  messageId = !messageId ? uuidv4() : messageId;
  const extendMsg = { ...(reportData?.extendMsg || {}), messageId };
  reportData.extendMsg = extendMsg;
  if (reportData.actionCate === 'sec') {
    sendReportToSec({ type: reportData.actionType, vulnId: (reportData.extendMsg as any)?.vulnId });
    return;
  }
  const reportParam: IActionReportParam = {
    ...getActionBaseParam(),
    startTime: new Date(),
    consumeTime: 0,
    ...reportData, // 覆盖或添加传入的报告数据字段
    accept: !!reportData.accept ? 1 : 0,
    resultCodeLine: countCodeLinesInMarkdown(reportData.result, reportData.actionType),
  };
  if (reportParam.accept === 1) {
    reportParam.acceptCodeLine = reportParam.resultCodeLine;
  }
  // 上报到前端数据统计
  sendReportToServer(reportParam);

  // 上报到JoyCoder侧统一数据(completion不上报JoyCoder日志表，单独上报到JoyCoder补全表)
  reportData.actionType !== ActionType.completion &&
    sendReportToJoyCoder(convertActionReportToJoyCoderReport(reportParam));
}

/**
 * 上报过程行为方式（语法糖，不需要自己记录耗时）
 *
 * @example
 * ```ts
 * const reporter = startReportAction({ quesiton: 'code', actionCate: 'ai', actionType: 'completion' }); // 开始上报
 *
 * await to(processGPT()); // 执行过程
 *
 * reporter.success({ result: 'xxxx', resultCodeLine: 10, acceptCodeLine: 8, }); // 上报成功
 *
 * ```
 *
 * @export
 * @param {IActionCustomReportParam} reportData
 * @return {*}  {{
 *   success: (successData?: Partial<IActionCustomReportParam>) => void;
 *   fail: (failData?: Partial<IActionCustomReportParam>) => void;
 * }}
 */
export function startReportAction(
  reportData: IActionCustomReportParam,
  callback?: () => void
): {
  success: (successData?: Partial<IActionCustomReportParam>, successCallback?: () => void) => void;
  fail: (failData?: Partial<IActionCustomReportParam>, failCallback?: () => void) => void;
} {
  let isDone = false;
  reportData.startTime = new Date();
  // 代码补全[返回数据](上报到JoyCoder补全表)
  if (reportData.actionType === ActionType.completion) {
    sendReportToCompletion(convertActionReportToCompletionReport({ ...getActionBaseParam(), ...reportData }));
    if (typeof callback === 'function') {
      try {
        callback();
      } catch (error) {
        console.error(error);
      }
    }
  }
  return {
    success: (successData?: Partial<IActionCustomReportParam>, successCallback?: () => void) => {
      if (isDone) return;
      isDone = true;
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const startMS = reportData.startTime!.getTime();
      reportAction({
        ...reportData,
        ...successData,
        accept: 1,
        consumeTime: new Date().getTime() - startMS,
      });
      // 代码补全[采纳](上报到JoyCoder补全表)，不采纳时不上报
      if (reportData.actionType === ActionType.completion) {
        sendReportToCompletion(
          convertActionReportToCompletionReport({ ...getActionBaseParam(), ...reportData, ...successData })
        );
        if (typeof successCallback === 'function') {
          try {
            successCallback();
          } catch (error) {
            console.error(error);
          }
        }
      }
    },
    fail: (failData?: Partial<IActionCustomReportParam>, failCallback?: () => void) => {
      if (isDone) return;
      isDone = true;
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const startMS = reportData.startTime!.getTime();
      reportAction({
        ...reportData,
        ...failData,
        accept: 0,
        consumeTime: new Date().getTime() - startMS,
      });

      if (reportData.actionType === ActionType.completion) {
        if (typeof failCallback === 'function') {
          try {
            failCallback();
          } catch (error) {
            console.error(error);
          }
        }
      }
    },
  };
}

/**
 * 上报到服务端
 *
 * @param {IActionReportParam} reportParam
 */
async function sendReportToServer(reportParam: IActionReportParam) {
  // 端侧日志接口只支持string
  let extendMsg = '';
  if (reportParam.extendMsg) {
    extendMsg = JSON.stringify(reportParam.extendMsg);
  }
  console.log('[Reporting to VSCodeLog]:', { ...reportParam, extendMsg });
  const [err, res] = await to(
    axios.post('http://chatgpt-relay.jd.com/chatgptrelay/logJoyCoderUsage', {
      ...reportParam,
      extendMsg,
      startTime: moment(reportParam.startTime).format('YYYY-MM-DD HH:mm:ss'),
    })
  );
  if (err || res?.data?.code !== 0) {
    console.error('[VSCodeLog日志上报异常]:', err, res);
  }
}

/**
 * 上报到JoyCoder统一数据 非补全数据上报
 *
 * @see https://cf.jd.com/pages/viewpage.action?pageId=1384345319
 * @see https://joyspace.jd.com/pages/bWWM7JhhAwfM9Kep4hl2
 * @param {IJoyCoderReportParam} reportParam
 */
async function sendReportToJoyCoder(reportParam: IJoyCoderReportParam) {
  console.log('[Reporting to JoyCoder]:', reportParam);
  let reportUrl = 'http://jdhgpt.jd.com/history/opeartionSave';
  if (isBusiness()) {
    reportUrl = getJdhCgiUrl(reportUrl);
  } else if (isIDE()) {
    reportUrl = getBaseUrl() + '/api/saas/stat/v1/log/history';
  }
  const [err, res] = await to(
    axios.post(
      reportUrl,
      {
        ...reportParam,
      },
      {
        headers: getExtHeaders().headers,
      }
    )
  );
  if (err || res?.data?.code != 0) {
    console.error('[JoyCoder日志上报异常]:', err, res);
  }
}

/**
 * 上报到安全侧
 *
 * @param {IActionReportParam} reportParam
 */
async function sendReportToSec(reportParam: ISecReportParam) {
  const [err, res] = await to(
    axios.get('http://shenyisec.jd.com/joycoderhelp/userBehavior', {
      params: {
        ...reportParam,
        erp: getJdhLoginInfo()?.erp || '' + '_v',
        time: moment(new Date()).format('yyyy-MM-dd-HH:mm:ss'),
      },
    })
  );
  console.log('[神医日志上报]:', (getJdhLoginInfo()?.erp || '') + '_v', reportParam);
  err && console.error('[神医日志上报异常]:', err, res);
}

/**
 * 转换上报参数
 *
 * @param {IActionReportParam} actionParam
 * @return {*}  {IJoyCoderReportParam}
 */
function convertActionReportToJoyCoderReport(actionParam: IActionReportParam): IJoyCoderReportParam {
  const jdhLoginInfo = getJdhLoginInfo();
  const gitState = GitStateManager.getGitState(actionParam.curFilePath);
  const jdhParam: IJoyCoderReportParam = {
    // 从IActionBaseReportParam映射到IJDHBaseReportParam
    ideaVersion: actionParam.ideVersion,
    pluginVersion: actionParam.pluginVersion,
    user: jdhLoginInfo?.userName || userName || '',
    userToken: jdhLoginInfo?.userToken || '',
    osName: actionParam.osName,
    computerName: actionParam.computerName,
    computerDomain: actionParam.computerDomain,
    loginErp: jdhLoginInfo?.erp,
    projectName: actionParam.projectName,
    curFileName: actionParam.curFilePath.split(/[/\\]/).pop(),
    curFilePath: actionParam.curFilePath,

    actionType: actionParam.actionType,
    question: actionParam.question || '',
    result: actionParam.result,
    startTime: (actionParam.startTime || new Date()).getTime(),
    endTime:
      actionParam.startTime && actionParam.consumeTime
        ? actionParam.startTime.getTime() + actionParam.consumeTime
        : new Date().getTime(),
    consumeTime: actionParam.consumeTime,
    conversationId: actionParam.conversationId,
    model: actionParam.model,
    extendMsg: Object.assign(gitState, actionParam.extendMsg),
    // tokenAssert字段在IActionReportParam中不存在，不设置
  };

  return jdhParam;
}

/**
 * 计算代码块中的函数
 *
 * @param {*} markdownText
 * @return {*}
 */
export const countCodeLinesInMarkdown = (markdownText = '', actionType?: ActionType) => {
  if (!markdownText) return 0;
  // 正则表达式匹配三个反引号包围的代码块
  const codeBlockRegex = /```[\s\S]*?```/g;
  let totalLineCount = 0;

  // 查找所有代码块
  let codeBlocks = markdownText.match(codeBlockRegex) || [];

  // 所有文本都算是代码的场景
  if (!codeBlocks.length) {
    codeBlocks = [markdownText];
  }

  if (!codeBlocks.length) return 0;

  codeBlocks.forEach((block: string) => {
    // 移除代码块的包围符号
    const code = block.replace(/```/g, '');
    // 统计每个代码块的行数
    const lineCount = code.split('\n').length;
    totalLineCount += lineCount;
  });

  return totalLineCount;
};

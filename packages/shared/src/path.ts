import * as fs from 'fs';
import * as file from 'fs-extra';
import * as os from 'os';
import * as path from 'path';
import AdmZip from 'adm-zip';

/**
 * 获取全局文件目录，用于存放joycoder相关的所有文件
 * @returns
 */
export function getGlobalPath(): string {
  const homeDir = os.homedir();
  const joycodePath = path.join(homeDir, '.joycode');
  const joycoderPath = path.join(homeDir, '.joycoder');

  try {
    // 检查 .joycode 是否存在
    if (file.existsSync(joycodePath)) {
      return joycodePath;
    }

    // 检查 .joycoder 是否存在
    if (file.existsSync(joycoderPath)) {
      // 压缩 .joycoder 文件夹
      const zip = new AdmZip();
      const archivePath = path.join(homeDir, 'joycoder_backup.zip');

      zip.addLocalFolder(joycoderPath);
      zip.writeZip(archivePath);

      // 重命名 .joycoder 为 .joycode
      file.moveSync(joycoderPath, joycodePath, { overwrite: true });
    } else {
      file.mkdirSync(joycodePath, { recursive: true });
    }

    return joycodePath;
  } catch (error) {
    console.error('Error in getGlobalPath:', error);
    // 如果出现任何错误，尝试创建并返回 .joycode 路径
    try {
      file.mkdirSync(joycodePath, { recursive: true });
      return joycodePath;
    } catch (mkdirError) {
      console.error('Failed to create .joycode directory:', mkdirError);
      throw new Error('Unable to create or access .joycode directory');
    }
  }
}

export function getTempFolderPath(): string {
  const tempPath = path.join(getGlobalPath(), 'temp');
  if (!fs.existsSync(tempPath)) {
    fs.mkdirSync(tempPath);
  }
  return tempPath;
}

/**
 * 递归删除指定路径的文件夹及其所有子文件和子文件夹。
 * @param directoryPath 要删除的文件夹路径。
 */
export function deleteFolderRecursive(directoryPath) {
  if (fs.existsSync(directoryPath)) {
    fs.readdirSync(directoryPath).forEach((file) => {
      const curPath = path.join(directoryPath, file);
      if (fs.lstatSync(curPath).isDirectory()) {
        // 递归删除文件夹
        deleteFolderRecursive(curPath);
        fs.rmdirSync(curPath);
      } else {
        // 删除文件
        fs.unlinkSync(curPath);
      }
    });
  }
}

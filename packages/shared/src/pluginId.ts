import * as vscode from 'vscode';

/** 本插件ID */
const PLUGINS = {
  business: 'JoyCode.joycoder-business',
  ide: 'JoyCode.joycoder-editor',
};
export const PLUGIN_ID = PLUGINS[process?.env?.PLUGIN_VER ?? ''] || 'JoyCode.joycoder-fe';
// process && process?.env?.PLUGIN_VER === 'business' ? 'JoyCoder.joycoder-business' : 'JoyCoder.joycoder-fe';

export const ideVersion = vscode.version;
export const ideAppName = vscode.env.appName;
export const pluginVersion = `${
  process.env.PLUGIN_VER === 'ide' ? 'joycodeIDE' : process.env.PLUGIN_VER === 'business' ? 'vsbusiness' : 'vsfront'
}-${vscode.extensions.getExtension(PLUGIN_ID)?.packageJSON.version ?? 'unknown'}`;

import React from 'react';
import styled from 'styled-components';
import Avatar, { AvatarProps } from './Avatar';

export interface ModeInfoProps {
  /** 模式名称 */
  name: string;
  /** 头像图片URL */
  avatar?: string;
  /** 头像大小，默认为20px */
  avatarSize?: number;
  /** 是否显示头像，默认为true */
  showAvatar?: boolean;
  /** 自定义className */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 头像的额外属性 */
  avatarProps?: Partial<AvatarProps>;
}

const ModeInfoContainer = styled.div`
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  height: 20px;

  @media (max-width: 600px) {
    gap: 8px;
    margin-bottom: 6px;
  }
`;

const ModeName = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: var(--vscode-foreground);
  line-height: 16px;
  height: 16px;
  word-break: break-word;
  overflow-wrap: break-word;

  @media (max-width: 600px) {
    font-size: 12px;
  }
`;

/**
 * ModeInfo组件 - 显示模式信息（头像+名称）
 * 用于聊天界面中显示AI模式或用户信息
 */
export const ModeInfo: React.FC<ModeInfoProps> = ({
  name,
  avatar,
  avatarSize = 20,
  showAvatar = true,
  className,
  style,
  avatarProps = {}
}) => {
  return (
    <ModeInfoContainer className={className} style={style}>
      {showAvatar && (
        <Avatar
          src={avatar}
          alt={name}
          name={name}
          size={avatarSize}
          {...avatarProps}
        />
      )}
      <ModeName>{name}</ModeName>
    </ModeInfoContainer>
  );
};

export default ModeInfo;

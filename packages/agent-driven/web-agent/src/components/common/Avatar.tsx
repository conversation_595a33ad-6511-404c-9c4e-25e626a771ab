import React, { useState } from 'react';
import styled from 'styled-components';

export interface AvatarProps {
  /** 头像图片URL */
  src?: string;
  /** 图片alt文本 */
  alt: string;
  /** 用于fallback显示的名称 */
  name: string;
  /** 头像大小，默认为20px */
  size?: number;
  /** 自定义className */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

const AvatarContainer = styled.div<{ $size: number }>`
  flex-shrink: 0;
  width: ${props => props.$size}px;
  height: ${props => props.$size}px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--vscode-badge-background);

  @media (max-width: 600px) {
    width: ${props => props.$size}px;
    height: ${props => props.$size}px;
  }
`;

const AvatarImage = styled.img`
  width: 70%;
  height: 70%;
  object-fit: cover;

  &:error {
    display: none;
  }
`;

const AvatarFallback = styled.div<{ $size: number }>`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  font-size: ${props => Math.max(props.$size * 0.5, 10)}px;
  font-weight: 500;
`;

/**
 * Avatar组件 - 显示用户或AI的头像
 * 支持图片头像和文字fallback
 */
export const Avatar: React.FC<AvatarProps> = ({
  src,
  alt,
  name,
  size = 20,
  className,
  style
}) => {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  // Use fallback if no src, empty src, or image error
  const shouldUseFallback = imageError || !src || src.trim() === '';

  return (
    <AvatarContainer 
      $size={size} 
      className={className} 
      style={style}
    >
      {shouldUseFallback ? (
        <AvatarFallback $size={size}>
          {name && name.length > 0 ? name.charAt(0).toUpperCase() : '?'}
        </AvatarFallback>
      ) : (
        <AvatarImage
          src={src}
          alt={alt}
          onError={handleImageError}
        />
      )}
    </AvatarContainer>
  );
};

export default Avatar;

import { memo, useCallback, useState, useMemo } from 'react';
import { JoyCoderMessage } from '../../../../src/shared/ExtensionMessage';
import { CopyButton } from '../common';
import { CheckmarkControlAdaptor } from '../../adaptor/components/CheckmarkControlAdaptor';

export interface MessageToolbarProps {
  text: string;
  shouldShow?: boolean;
  isGenerating?: boolean;
  allMessages?: (JoyCoderMessage | JoyCoderMessage[])[];
  messageIndex?: number;
  currentMessage?: JoyCoderMessage;
  /** 消息时间戳，用于CheckmarkControlAdaptor */
  messageTs?: number;
}

/**
 * 消息工具条组件
 * 提供复制功能，支持合并连续AI消息的文本内容
 */
export const MessageToolbar = memo(({
  text,
  shouldShow = true,
  isGenerating = false,
  allMessages,
  messageIndex,
  currentMessage,
  messageTs
}: MessageToolbarProps) => {
  const [isVisible, setIsVisible] = useState(false);

  // 判断消息是否为用户消息
  const isUserMessage = useCallback((msg: JoyCoderMessage) => {
    return msg.say === 'user_feedback' ||
           msg.say === 'user_feedback_diff' ||
           // 特殊情况：第一条消息可能是say: "text"但实际是用户输入
           // 通过检查conversationHistoryIndex来判断是否是用户的初始输入
           (msg.say === 'text' && msg.conversationHistoryIndex === -1);
  }, []);

  // 判断消息是否为checkpoint或transition消息
  const isCheckpointOrTransitionMessage = useCallback((msg: JoyCoderMessage) => {
    return (
      msg.say === 'checkpoint_created' ||
      msg.ask === 'resume_completed_task' ||
      msg.ask === 'resume_task' ||
      msg.say === 'api_req_started' ||
      msg.say === 'api_req_finished' ||
      msg.say === 'api_req_retried' ||
      msg.say === 'deleted_api_reqs'
    );
  }, []);

  // 判断消息是否为AI消息（非用户消息且非checkpoint/transition消息）
  const isAIMessage = useCallback((msg: JoyCoderMessage) => {
    return !isUserMessage(msg) && !isCheckpointOrTransitionMessage(msg);
  }, [isUserMessage, isCheckpointOrTransitionMessage]);

  // 获取下一条消息
  const getNextMessage = useCallback(() => {
    if (!allMessages || messageIndex === undefined) return null;
    const nextMessageIndex = messageIndex + 1;
    if (nextMessageIndex >= allMessages.length) return null;
    const nextMessage = allMessages[nextMessageIndex];
    return Array.isArray(nextMessage) ? nextMessage[0] : nextMessage;
  }, [allMessages, messageIndex]);

  // 获取上一条消息
  const getPreviousMessage = useCallback(() => {
    if (!allMessages || messageIndex === undefined) return null;
    const prevMessageIndex = messageIndex - 1;
    if (prevMessageIndex < 0) return null;
    const prevMessage = allMessages[prevMessageIndex];
    return Array.isArray(prevMessage) ? prevMessage[prevMessage.length - 1] : prevMessage;
  }, [allMessages, messageIndex]);

  const nextMessage = getNextMessage();
  const previousMessage = getPreviousMessage();

  // 判断是否应该显示CheckmarkControlAdaptor（回滚按钮）
  const shouldShowCheckmarkControl = useCallback(() => {
    // 当前消息是checkpoint_created时显示回滚按钮
    if (currentMessage?.say === 'checkpoint_created' && messageTs != null) {
      return true;
    }

    // 当下一条消息是checkpoint_created时，在当前消息上显示回滚按钮（合并显示）
    // 但是要排除checkpoint/transition消息
    if (nextMessage?.say === 'checkpoint_created') {
      // 如果当前消息是checkpoint或transition消息，不显示回滚按钮
      if (currentMessage && isCheckpointOrTransitionMessage(currentMessage)) {
        return false;
      }
      return true;
    }

    return false;
  }, [currentMessage, nextMessage, messageTs, isCheckpointOrTransitionMessage]);

  // 获取回滚按钮对应的消息时间戳
  const getCheckmarkMessageTs = useCallback(() => {
    // 如果当前消息是checkpoint_created，使用当前消息的时间戳
    if (currentMessage?.say === 'checkpoint_created' && messageTs != null) {
      return messageTs;
    }

    // 如果下一条消息是checkpoint_created，使用下一条消息的时间戳
    // 但是要排除checkpoint/transition消息
    if (nextMessage?.say === 'checkpoint_created') {
      // 如果当前消息是checkpoint或transition消息，不返回时间戳
      if (currentMessage && isCheckpointOrTransitionMessage(currentMessage)) {
        return undefined;
      }
      return nextMessage.ts;
    }

    return undefined;
  }, [currentMessage, nextMessage, messageTs, isCheckpointOrTransitionMessage]);

  // 获取回滚按钮对应的checkpoint消息的上一条消息
  const getCheckmarkPreviousMessage = useCallback(() => {
    // 如果当前消息是checkpoint_created，返回当前消息的上一条消息
    if (currentMessage?.say === 'checkpoint_created') {
      return previousMessage;
    }

    // 如果下一条消息是checkpoint_created，返回当前消息（因为当前消息是checkpoint的上一条）
    // 但是要排除checkpoint/transition消息
    if (nextMessage?.say === 'checkpoint_created') {
      // 如果当前消息是checkpoint或transition消息，不返回
      if (currentMessage && isCheckpointOrTransitionMessage(currentMessage)) {
        return null;
      }
      return currentMessage;
    }

    return null;
  }, [currentMessage, nextMessage, previousMessage, isCheckpointOrTransitionMessage]);



  // 判断是否应该显示复制按钮
  const shouldShowCopyButton = useCallback(() => {
    if (!currentMessage) return false;

    // 用户消息始终显示复制按钮
    if (isUserMessage(currentMessage)) {
      return true;
    }

    // checkpoint_created消息不显示复制按钮，只显示回滚按钮
    if (currentMessage.say === 'checkpoint_created') {
      return false;
    }

    // 其他checkpoint或transition消息不显示复制按钮
    if (isCheckpointOrTransitionMessage(currentMessage)) {
      return false;
    }

    // AI消息需要判断是否是连续AI消息序列的最后一条
    if (isAIMessage(currentMessage)) {
      if (!allMessages || messageIndex === undefined) {
        return true; // 如果没有消息列表信息，默认显示
      }

      if (!nextMessage) {
        return true; // 如果是最后一条消息，显示复制按钮
      }

      // 如果下一条消息是checkpoint_created，则在当前消息上显示复制按钮（合并显示）
      if (nextMessage.say === 'checkpoint_created') {
        return true;
      }

      // 如果下一条消息是用户消息或其他checkpoint/transition消息，则显示复制按钮
      if (isUserMessage(nextMessage) || isCheckpointOrTransitionMessage(nextMessage)) {
        return true;
      }

      // 如果下一条消息也是AI消息，则不显示复制按钮（将在最后一条AI消息中显示）
      if (isAIMessage(nextMessage)) {
        return false;
      }

      // 默认显示复制按钮
      return true;
    }

    // 默认不显示复制按钮
    return false;
  }, [currentMessage, nextMessage, allMessages, messageIndex, isUserMessage, isCheckpointOrTransitionMessage, isAIMessage]);

  // 获取合并后的文本内容
  const getMergedText = useCallback(() => {
    if (!allMessages || messageIndex === undefined || !currentMessage) {
      return text; // 如果没有足够信息，返回当前文本
    }

    // 检查当前消息是否是用户消息
    if (isUserMessage(currentMessage)) {
      return text; // 用户消息不需要合并
    }

    // checkpoint_created消息直接返回当前文本，不需要合并
    if (currentMessage.say === 'checkpoint_created') {
      return text;
    }

    // 收集连续的AI消息
    const consecutiveAIMessages: string[] = [];

    // 向前查找连续的AI消息起点
    let startIndex = messageIndex;
    while (startIndex > 0) {
      const prevMessage = allMessages[startIndex - 1];
      if (!prevMessage) break;

      const actualPrevMessage = Array.isArray(prevMessage) ? prevMessage[prevMessage.length - 1] : prevMessage;
      if (!actualPrevMessage) break;

      // 如果前一条消息不是AI消息，则找到起点
      if (!isAIMessage(actualPrevMessage)) {
        break;
      }

      startIndex--;
    }

    // 从起点开始收集所有连续的AI消息文本
    for (let i = startIndex; i <= messageIndex; i++) {
      const msg = allMessages[i];
      if (!msg) continue;

      const actualMessage = Array.isArray(msg) ? msg[msg.length - 1] : msg;
      if (!actualMessage) continue;

      // 只处理AI消息
      if (!isAIMessage(actualMessage)) {
        continue;
      }

      // 提取消息文本
      let messageText = '';
      if (actualMessage.say === 'text' || actualMessage.say === 'completion_result') {
        try {
          const parsed = JSON.parse(actualMessage.text || '{}');
          messageText = parsed.text || actualMessage.text || '';
        } catch {
          messageText = actualMessage.text || '';
        }
      } else {
        messageText = actualMessage.text || '';
      }

      if (messageText.trim()) {
        consecutiveAIMessages.push(messageText.trim());
      }
    }

    // 如果只有一条消息，返回原文本
    if (consecutiveAIMessages.length <= 1) {
      return text;
    }

    // 合并多条消息，用换行分隔
    return consecutiveAIMessages.join('\n\n');
  }, [allMessages, messageIndex, currentMessage, text, isUserMessage, isAIMessage]);


  // 计算是否应该显示复制按钮和回滚按钮
  const showCopyButton = shouldShowCopyButton();
  const showCheckmarkControl = shouldShowCheckmarkControl();
  const checkmarkMessageTs = getCheckmarkMessageTs();

  // 使用 useMemo 来稳定布局计算，避免闪烁
  const alignLeft = useMemo(() => {
    // 确保必要的数据已准备好
    if (!currentMessage) return false;

    // 情况1：当前消息是checkpoint_created且上一条消息是AI消息，则靠左排列
    if (currentMessage.say === 'checkpoint_created' && previousMessage) {
      const isUser = isUserMessage(previousMessage);
      const isCheckpointOrTransition =
        previousMessage.say === 'checkpoint_created' ||
        previousMessage.ask === 'resume_completed_task' ||
        previousMessage.ask === 'resume_task' ||
        previousMessage.say === 'api_req_started' ||
        previousMessage.say === 'api_req_finished' ||
        previousMessage.say === 'api_req_retried' ||
        previousMessage.say === 'deleted_api_reqs';
      const isPreviousAI = !isUser && !isCheckpointOrTransition;
      return isPreviousAI;
    }

    // 情况2：当前消息不是checkpoint_created，但下一条消息是checkpoint_created
    // 且当前消息是AI消息，则靠左排列（合并显示的情况）
    if (nextMessage?.say === 'checkpoint_created') {
      const isCurrentUser = isUserMessage(currentMessage);
      const isCurrentCheckpointOrTransition =
        currentMessage.say === 'checkpoint_created' ||
        currentMessage.ask === 'resume_completed_task' ||
        currentMessage.ask === 'resume_task' ||
        currentMessage.say === 'api_req_started' ||
        currentMessage.say === 'api_req_finished' ||
        currentMessage.say === 'api_req_retried' ||
        currentMessage.say === 'deleted_api_reqs';
      const isCurrentAI = !isCurrentUser && !isCurrentCheckpointOrTransition;
      return isCurrentAI;
    }

    return false;
  }, [currentMessage, previousMessage, nextMessage, isUserMessage]);

  // 检查数据是否准备完成，避免闪烁
  const isDataReady = useMemo(() => {
    // 基本数据检查
    if (!currentMessage) return false;

    // 如果需要显示回滚按钮且涉及到消息间的关系判断，确保相关数据已准备好
    if (showCheckmarkControl) {
      // 情况1：当前消息是checkpoint_created，需要previousMessage数据
      if (currentMessage.say === 'checkpoint_created') {
        return allMessages && messageIndex !== undefined;
      }

      // 情况2：下一条消息是checkpoint_created，需要nextMessage数据
      if (nextMessage?.say === 'checkpoint_created') {
        return allMessages && messageIndex !== undefined;
      }
    }

    // 其他情况认为数据已准备好
    return true;
  }, [showCheckmarkControl, currentMessage, nextMessage, allMessages, messageIndex]);

  // 如果不应该显示或者正在生成中，则返回null
  if (!shouldShow || isGenerating) {
    return null;
  }

  // 如果既不显示复制按钮也不显示回滚按钮，则返回null
  if (!showCopyButton && !showCheckmarkControl) {
    return null;
  }

  // 如果数据未准备好，返回null避免闪烁
  if (!isDataReady) {
    return null;
  }

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: alignLeft ? 'flex-start' : 'flex-end', // 根据alignLeft决定对齐方式
        alignItems: 'center',
        gap: '4px', // 减小间距使图标更紧凑
        marginTop: '4px',
        opacity: isVisible ? 1 : 0.3, // 悬停时所有图标都可见
        transition: 'opacity 0.2s ease',
      }}
      className="message-toolbar"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {/* 当需要靠左排列时，回滚按钮在前 */}
      {alignLeft && showCheckmarkControl && checkmarkMessageTs && (
        <CheckmarkControlAdaptor
          messageTs={checkmarkMessageTs}
          previousMessage={getCheckmarkPreviousMessage()}
        />
      )}

      {/* 根据条件显示复制按钮 */}
      {showCopyButton && (
        <CopyButton text={getMergedText()} />
      )}

      {/* 当不需要靠左排列时，回滚按钮在后（保持原有顺序） */}
      {!alignLeft && showCheckmarkControl && checkmarkMessageTs && (
        <CheckmarkControlAdaptor
          messageTs={checkmarkMessageTs}
          previousMessage={getCheckmarkPreviousMessage()}
        />
      )}
    </div>
  );
});

MessageToolbar.displayName = 'MessageToolbar';

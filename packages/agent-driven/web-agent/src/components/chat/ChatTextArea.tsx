import React, { forwardRef, useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { mentionRegex, mentionRegexGlobal } from '../../../../src/shared/context-mentions';
import { useExtensionState } from '../../context/ExtensionStateContext';
import {
  ContextMenuOptionType,
  delContextItem,
  getContextItem,
  getContextMenuOptions,
  insertMention,
  insertMentionDirectly,
  removeMention,
  shouldShowContextMenu,
} from '../../utils/context-mentions';
import { MAX_IMAGES_PER_MESSAGE } from '../../adaptor/locales/types';
import { vscode } from '../../utils/vscode';
import './index.css';
import ChatTextAreaAdaptor from '../../adaptor/components/ChatTextAreaAdaptor';
import { useEvent } from 'react-use';
import { ExtensionMessage } from '../../../../src/shared/ExtensionMessage';
import {
  getMatchingSlashCommands,
  insertSlashCommand,
  removeSlashCommand,
  shouldShowSlashCommandsMenu,
  SlashCommand,
  slashCommandDeleteRegex,
  validateSlashCommand,
} from '../../utils/slash-commands';

interface ChatTextAreaProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  handleHistoryPrompt: (e: any, value: string) => void;
  isStreaming: boolean;
  textAreaDisabled: boolean;
  handleSecondaryButtonClick: (value: string, selectedImages: string[], isTerminate?: boolean) => void;
  placeholderText: string;
  selectedImages: string[];
  setSelectedImages: React.Dispatch<React.SetStateAction<string[]>>;
  onSend: () => void;
  onSelectImages: () => void;
  shouldDisableImages: boolean;
  messages?: any;
  onHeightChange?: (height: number) => void;
  setShowPromptView?: () => void;
}

const ChatTextArea = forwardRef<HTMLTextAreaElement, ChatTextAreaProps>(
  (
    {
      inputValue,
      setInputValue,
      handleHistoryPrompt,
      isStreaming,
      textAreaDisabled,
      handleSecondaryButtonClick,
      placeholderText,
      selectedImages,
      setSelectedImages,
      onSend,
      onSelectImages,
      shouldDisableImages,
      messages,
      onHeightChange,
      setShowPromptView,
    },
    ref,
  ) => {
    const { filePaths, ruleFiles, updateStatus, promptList } = useExtensionState();
    const [isTextAreaFocused, setIsTextAreaFocused] = useState(false);
    const [thumbnailsHeight, setThumbnailsHeight] = useState(0);
    const [textAreaBaseHeight, setTextAreaBaseHeight] = useState<number | undefined>(undefined);
    const [showContextMenu, setShowContextMenu] = useState(false);
    const [cursorPosition, setCursorPosition] = useState(0);
    const [searchQuery, setSearchQuery] = useState('');
    const textAreaRef = useRef<HTMLTextAreaElement | null>(null);
    const [isMouseDownOnMenu, setIsMouseDownOnMenu] = useState(false);
    const highlightLayerRef = useRef<HTMLDivElement>(null);
    const contextLayerRef = useRef<HTMLDivElement>(null);
    const [selectedMenuIndex, setSelectedMenuIndex] = useState(-1);
    const [selectedType, setSelectedType] = useState<ContextMenuOptionType | null>(null);
    const [justDeletedSpaceAfterMention, setJustDeletedSpaceAfterMention] = useState(false);
    const [intendedCursorPosition, setIntendedCursorPosition] = useState<number | null>(null);
    const contextMenuContainerRef = useRef<HTMLDivElement>(null);
    const filePathsRef = useRef(filePaths);

    const [showSlashCommandsMenu, setShowSlashCommandsMenu] = useState(false);
    const [selectedSlashCommandsIndex, setSelectedSlashCommandsIndex] = useState(0);
    const [slashCommandsQuery, setSlashCommandsQuery] = useState('');
    const slashCommandsMenuContainerRef = useRef<HTMLDivElement>(null);
    const [justDeletedSpaceAfterSlashCommand, setJustDeletedSpaceAfterSlashCommand] = useState(false);

    // const getPopupContainer = (id: string) => {
    //   return () => document.getElementById(id) || document.createElement('div');
    // };
    const queryItems = useMemo(() => {
      return [
        { type: ContextMenuOptionType.Problems, value: 'problems' },
        { type: ContextMenuOptionType.Web, value: 'web' },
        { type: ContextMenuOptionType.Codebase, value: 'codebase' },
        ...filePaths
          .map((file) => '/' + file)
          .map((path) => ({
            type: path.endsWith('/') ? ContextMenuOptionType.Folder : ContextMenuOptionType.File,
            value: path,
          })),
        ...ruleFiles.map((rule) => ({
          type: ContextMenuOptionType.UserRules,
          value: `${rule}`,
        })),
      ];
    }, [filePaths, ruleFiles]);

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (contextMenuContainerRef.current && !contextMenuContainerRef.current.contains(event.target as Node)) {
          setShowContextMenu(false);
        }
      };

      if (showContextMenu) {
        document.addEventListener('mousedown', handleClickOutside);
      }

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [showContextMenu, setShowContextMenu]);

    const handleMentionSelect = useCallback(
      (type: ContextMenuOptionType, value?: string) => {
        if (type === ContextMenuOptionType.NoResults) {
          return;
        }

        if (
          (type === ContextMenuOptionType.File ||
            type === ContextMenuOptionType.Folder ||
            type === ContextMenuOptionType.UserRules) &&
          !value
        ) {
          setSelectedType(type);
          setSearchQuery('');
          setSelectedMenuIndex(0);
          return;
        }

        setShowContextMenu(false);
        setSelectedType(null);
        if (textAreaRef.current) {
          let insertValue = value || '';
          if (type === ContextMenuOptionType.URL) {
            insertValue = value || '';
          } else if (type === ContextMenuOptionType.File || type === ContextMenuOptionType.Folder) {
            insertValue = value || '';
          } else if (type === ContextMenuOptionType.Problems) {
            insertValue = 'problems';
          } else if (type === ContextMenuOptionType.Web) {
            insertValue = 'web';
          } else if (type === ContextMenuOptionType.UserRules) {
            insertValue = value || 'rules';
          } else if (type === ContextMenuOptionType.Codebase) {
            insertValue = 'codebase';
          }
          const { newValue, mentionIndex } = insertMention(textAreaRef.current.value, cursorPosition, insertValue);
          setInputValue(newValue);
          const newCursorPosition = newValue.indexOf(' ', mentionIndex + insertValue.length) + 1;
          setCursorPosition(newCursorPosition);
          setIntendedCursorPosition(newCursorPosition);

          // scroll to cursor
          setTimeout(() => {
            if (textAreaRef.current) {
              textAreaRef.current.blur();
              textAreaRef.current.focus();
            }
          }, 0);
        }
      },
      [setInputValue, cursorPosition],
    );

    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (textAreaDisabled) {
          if (event.key === 'Enter' || event.key === 'Tab') {
            event.preventDefault();
          }
          return;
        }
        if (showSlashCommandsMenu) {
          if (event.key === 'Escape') {
            setShowSlashCommandsMenu(false);
            return;
          }

          if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
            event.preventDefault();
            setSelectedSlashCommandsIndex((prevIndex) => {
              const direction = event.key === 'ArrowUp' ? -1 : 1;
              const commands = getMatchingSlashCommands(slashCommandsQuery, promptList);

              if (commands.length === 0) {
                return prevIndex;
              }

              const newIndex = (prevIndex + direction + commands.length) % commands.length;
              return newIndex;
            });
            return;
          }

          if ((event.key === 'Enter' || event.key === 'Tab') && selectedSlashCommandsIndex !== -1) {
            event.preventDefault();
            const commands = getMatchingSlashCommands(slashCommandsQuery, promptList);
            if (commands.length > 0) {
              handleSlashCommandsSelect(commands[selectedSlashCommandsIndex]);
            }
            return;
          }
        }
        if (showContextMenu) {
          if (event.key === 'Escape') {
            // event.preventDefault()
            setSelectedType(null);
            setSelectedMenuIndex(3); // File by default
            return;
          }

          if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
            event.preventDefault();
            setSelectedMenuIndex((prevIndex) => {
              const direction = event.key === 'ArrowUp' ? -1 : 1;
              const options = getContextMenuOptions(searchQuery, selectedType, queryItems);
              const optionsLength = options.length;

              if (optionsLength === 0) return prevIndex;

              // Find selectable options (non-URL types)
              const selectableOptions = options.filter(
                (option) =>
                  option.type !== ContextMenuOptionType.URL && option.type !== ContextMenuOptionType.NoResults,
              );

              if (selectableOptions.length === 0) return -1; // No selectable options

              // Find the index of the next selectable option
              const currentSelectableIndex = selectableOptions.findIndex((option) => option === options[prevIndex]);

              const newSelectableIndex =
                (currentSelectableIndex + direction + selectableOptions.length) % selectableOptions.length;

              // Find the index of the selected option in the original options array
              return options.findIndex((option) => option === selectableOptions[newSelectableIndex]);
            });
            return;
          }
          if ((event.key === 'Enter' || event.key === 'Tab') && selectedMenuIndex !== -1) {
            event.preventDefault();
            const selectedOption = getContextMenuOptions(searchQuery, selectedType, queryItems)[selectedMenuIndex];
            if (
              selectedOption &&
              selectedOption.type !== ContextMenuOptionType.URL &&
              selectedOption.type !== ContextMenuOptionType.NoResults
            ) {
              handleMentionSelect(selectedOption.type, selectedOption.value);
            }
            return;
          }
        }

        const isComposing = event.nativeEvent?.isComposing ?? false;
        if (event.key === 'Enter' && !event.shiftKey && !isComposing) {
          event.preventDefault();
          onSend();
        }

        if (event.key === 'Backspace' && !isComposing) {
          const charBeforeCursor = inputValue[cursorPosition - 1];
          const charAfterCursor = inputValue[cursorPosition + 1];

          const charBeforeIsWhitespace =
            charBeforeCursor === ' ' || charBeforeCursor === '\n' || charBeforeCursor === '\r\n';
          const charAfterIsWhitespace =
            charAfterCursor === ' ' || charAfterCursor === '\n' || charAfterCursor === '\r\n';
          // checks if char before cusor is whitespace after a mention
          if (
            charBeforeIsWhitespace &&
            inputValue.slice(0, cursorPosition - 1).match(new RegExp(mentionRegex.source + '$')) // "$" is added to ensure the match occurs at the end of the string
          ) {
            const newCursorPosition = cursorPosition - 1;
            // if mention is followed by another word, then instead of deleting the space separating them we just move the cursor to the end of the mention
            if (!charAfterIsWhitespace) {
              event.preventDefault();
              textAreaRef.current?.setSelectionRange(newCursorPosition, newCursorPosition);
              setCursorPosition(newCursorPosition);
            }
            setCursorPosition(newCursorPosition);
            setJustDeletedSpaceAfterMention(true);
            setJustDeletedSpaceAfterSlashCommand(false);
          } else if (charBeforeIsWhitespace && inputValue.slice(0, cursorPosition - 1).match(slashCommandDeleteRegex)) {
            // New slash command handling
            const newCursorPosition = cursorPosition - 1;
            if (!charAfterIsWhitespace) {
              event.preventDefault();
              textAreaRef.current?.setSelectionRange(newCursorPosition, newCursorPosition);
              setCursorPosition(newCursorPosition);
            }
            setCursorPosition(newCursorPosition);
            setJustDeletedSpaceAfterSlashCommand(true);
            setJustDeletedSpaceAfterMention(false);
          } else if (justDeletedSpaceAfterMention) {
            const { newText, newPosition } = removeMention(inputValue, cursorPosition);
            if (newText !== inputValue) {
              event.preventDefault();
              setInputValue(newText);
              console.log(
                '%c [ justDeletedSpaceAfterMention-newText ]-319',
                'font-size:13px; background:pink; color:#bf2c9f;',
                newText,
              );
              setIntendedCursorPosition(newPosition); // Store the new cursor position in state
            }
            setJustDeletedSpaceAfterMention(false);
            setShowContextMenu(false);
          } else if (justDeletedSpaceAfterMention) {
            const { newText, newPosition } = removeMention(inputValue, cursorPosition);
            if (newText !== inputValue) {
              event.preventDefault();
              setInputValue(newText);
              setIntendedCursorPosition(newPosition);
            }
            setJustDeletedSpaceAfterMention(false);
            setShowContextMenu(false);
          } else if (justDeletedSpaceAfterSlashCommand) {
            // New slash command deletion
            const { newText, newPosition } = removeSlashCommand(inputValue, cursorPosition);
            if (newText !== inputValue) {
              event.preventDefault();
              setInputValue(newText);
              setIntendedCursorPosition(newPosition);
            }
            setJustDeletedSpaceAfterSlashCommand(false);
            setShowSlashCommandsMenu(false);
          } else {
            setJustDeletedSpaceAfterMention(false);
            setJustDeletedSpaceAfterSlashCommand(false);
          }
        }
      },
      [
        textAreaDisabled,
        showSlashCommandsMenu,
        showContextMenu,
        selectedSlashCommandsIndex,
        slashCommandsQuery,
        selectedMenuIndex,
        searchQuery,
        selectedType,
        queryItems,
        handleMentionSelect,
        onSend,
        inputValue,
        cursorPosition,
        justDeletedSpaceAfterMention,
        justDeletedSpaceAfterSlashCommand,
        setInputValue,
      ],
    );

    useLayoutEffect(() => {
      if (intendedCursorPosition !== null && textAreaRef.current) {
        textAreaRef.current.setSelectionRange(intendedCursorPosition, intendedCursorPosition);
        setIntendedCursorPosition(null); // Reset the state
      }
    }, [inputValue, intendedCursorPosition]);

    const handleInputChange = useCallback(
      (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const newValue = e.target.value;
        const newCursorPosition = e.target.selectionStart;
        setInputValue(newValue);
        setCursorPosition(newCursorPosition);
        let showMenu = shouldShowContextMenu(newValue, newCursorPosition);
        const showSlashCommandsMenu = shouldShowSlashCommandsMenu(newValue, newCursorPosition);

        // we do not allow both menus to be shown at the same time
        // the slash commands menu has precedence bc its a narrower component
        if (showSlashCommandsMenu) {
          showMenu = false;
        }

        setShowSlashCommandsMenu(showSlashCommandsMenu);
        setShowContextMenu(showMenu);

        if (showSlashCommandsMenu) {
          const slashIndex = newValue.indexOf('/');
          const query = newValue.slice(slashIndex + 1, newCursorPosition);
          setSlashCommandsQuery(query);
          setSelectedSlashCommandsIndex(0);
        } else {
          setSlashCommandsQuery('');
          setSelectedSlashCommandsIndex(0);
        }
        setShowContextMenu(showMenu);
        if (showMenu) {
          const lastAtIndex = newValue.lastIndexOf('@', newCursorPosition - 1);
          const query = newValue.slice(lastAtIndex + 1, newCursorPosition);
          setSearchQuery(query);
          if (query.length > 0) {
            setSelectedMenuIndex(0);
          } else {
            setSelectedMenuIndex(3); // Set to "File" option by default
          }
        } else {
          setSearchQuery('');
          setSelectedMenuIndex(-1);
        }
      },
      [setInputValue],
    );

    useEffect(() => {
      if (!showContextMenu) {
        setSelectedType(null);
      }
    }, [showContextMenu]);
    const handleBlur = useCallback(() => {
      // Only hide the context menu if the user didn't click on it
      if (!isMouseDownOnMenu) {
        setShowContextMenu(false);
        setShowSlashCommandsMenu(false);
      }
      setIsTextAreaFocused(false);
    }, [isMouseDownOnMenu]);

    const handlePaste = useCallback(
      async (e: React.ClipboardEvent) => {
        if (textAreaDisabled) {
          return;
        }

        const items = e.clipboardData.items;

        const pastedText = e.clipboardData.getData('text');
        // Check if the pasted content is a URL, add space after so user can easily delete if they don't want it
        const urlRegex = /^\S+:\/\/\S+$/;
        if (urlRegex.test(pastedText.trim())) {
          e.preventDefault();
          const trimmedUrl = pastedText.trim();
          const newValue = inputValue.slice(0, cursorPosition) + trimmedUrl + ' ' + inputValue.slice(cursorPosition);
          setInputValue(newValue);
          const newCursorPosition = cursorPosition + trimmedUrl.length + 1;
          setCursorPosition(newCursorPosition);
          setIntendedCursorPosition(newCursorPosition);
          setShowContextMenu(false);

          // Scroll to new cursor position
          // https://stackoverflow.com/questions/29899364/how-do-you-scroll-to-the-position-of-the-cursor-in-a-textarea/40951875#40951875
          setTimeout(() => {
            if (textAreaRef.current) {
              textAreaRef.current.blur();
              textAreaRef.current.focus();
            }
          }, 0);
          // NOTE: callbacks dont utilize return function to cleanup, but it's fine since this timeout immediately executes and will be cleaned up by the browser (no chance component unmounts before it executes)

          return;
        }

        const acceptedTypes = ['png', 'jpeg', 'webp']; // supported by anthropic and openrouter (jpg is just a file extension but the image will be recognized as jpeg)
        const imageItems = Array.from(items).filter((item) => {
          const [type, subtype] = item.type.split('/');
          return type === 'image' && acceptedTypes.includes(subtype);
        });
        if (!shouldDisableImages && imageItems.length > 0) {
          e.preventDefault();
          const imagePromises = imageItems.map((item) => {
            return new Promise<string | null>((resolve) => {
              const blob = item.getAsFile();
              if (!blob) {
                resolve(null);
                return;
              }
              const reader = new FileReader();
              reader.onloadend = () => {
                if (reader.error) {
                  console.error('Error reading file:', reader.error);
                  resolve(null);
                } else {
                  const result = reader.result;
                  resolve(typeof result === 'string' ? result : null);
                }
              };
              reader.readAsDataURL(blob);
            });
          });
          const imageDataArray = await Promise.all(imagePromises);
          const dataUrls = imageDataArray.filter((dataUrl): dataUrl is string => dataUrl !== null);
          //.map((dataUrl) => dataUrl.split(",")[1]) // strip the mime type prefix, sharp doesn't need it
          if (dataUrls.length > 0) {
            setSelectedImages((prevImages) => [...prevImages, ...dataUrls].slice(0, MAX_IMAGES_PER_MESSAGE));
          } else {
            console.warn('No valid images were processed');
          }
        }
      },
      [shouldDisableImages, setSelectedImages, cursorPosition, setInputValue, inputValue, textAreaDisabled],
    );

    const handleThumbnailsHeightChange = useCallback((height: number) => {
      setThumbnailsHeight(height);
    }, []);

    useEffect(() => {
      if (selectedImages.length === 0) {
        setThumbnailsHeight(20);
      }
    }, [selectedImages]);

    const handleMenuMouseDown = useCallback(() => {
      setIsMouseDownOnMenu(true);
    }, []);

    const updateHighlights = useCallback((fileInfo?: any) => {
      if (!textAreaRef.current || !highlightLayerRef.current) return;
      // 匹配编辑区选择
      if (fileInfo && contextLayerRef.current) {
        const contextItem = getContextItem(fileInfo, true, fileInfo.selectText);
        contextLayerRef.current.innerHTML = contextItem;
        return;
      }

      let processedText = textAreaRef.current.value;
      processedText = processedText
        .replace(/\n$/, '\n\n')
        .replace(/[<>&]/g, (c) => ({ '<': '&lt;', '>': '&gt;', '&': '&amp;' })[c] || c)
        .replace(mentionRegexGlobal, '<mark class="mention-context-textarea-highlight">$&</mark>');
      if (/^\s*\//.test(processedText)) {
        const slashIndex = processedText.indexOf('/');
        // end of command is end of text or first whitespace
        const spaceIndex = processedText.indexOf(' ', slashIndex);
        const endIndex = spaceIndex > -1 ? spaceIndex : processedText.length;
        // extract and validate the exact command text
        const commandText = processedText.substring(slashIndex + 1, endIndex);
        const isValidCommand = validateSlashCommand(commandText, promptList);
        if (isValidCommand) {
          const fullCommand = processedText.substring(slashIndex, endIndex); // includes slash
          const highlighted = `<mark class="mention-context-textarea-highlight">${fullCommand}</mark>`;
          processedText = processedText.substring(0, slashIndex) + highlighted + processedText.substring(endIndex);
        }
      }
      highlightLayerRef.current.innerHTML = processedText;
      highlightLayerRef.current.scrollTop = textAreaRef.current.scrollTop;
      highlightLayerRef.current.scrollLeft = textAreaRef.current.scrollLeft;
    }, []);

    useLayoutEffect(() => {
      updateHighlights();
    }, [inputValue, updateHighlights]);

    const updateCursorPosition = useCallback(() => {
      if (textAreaRef.current) {
        setCursorPosition(textAreaRef.current.selectionStart);
      }
    }, []);

    const handleKeyUp = useCallback(
      (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (textAreaDisabled) {
          return;
        }
        if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(e.key)) {
          updateCursorPosition();
        }
        const value = e.currentTarget.value;
        const isEdit = (value && value.split('\n').length === 1) || !value;
        if ((e.key === 'ArrowUp' || e.key === 'ArrowDown') && isEdit) {
          handleHistoryPrompt(e, inputValue.trim());
        }
      },
      [updateCursorPosition, handleHistoryPrompt, inputValue, textAreaDisabled],
    );
    const handleSlashCommandsSelect = useCallback(
      (command: SlashCommand) => {
        setShowSlashCommandsMenu(false);

        if (textAreaRef.current) {
          const { newValue, commandIndex } = insertSlashCommand(textAreaRef.current.value, command.name);
          const newCursorPosition = newValue.indexOf(' ', commandIndex + 1 + command.name.length) + 1;

          setInputValue(newValue);
          setCursorPosition(newCursorPosition);
          setIntendedCursorPosition(newCursorPosition);

          setTimeout(() => {
            if (textAreaRef.current) {
              textAreaRef.current.blur();
              textAreaRef.current.focus();
            }
          }, 0);
        }
      },
      [setInputValue],
    );

    useEffect(() => {
      vscode.postMessage({ type: 'chatgpt-get-model' });
      vscode.postMessage({ type: 'chatgpt-get-model-list' });
    }, [ref]);
    const handleMessage = useCallback(
      (e: MessageEvent) => {
        const message: ExtensionMessage = e.data;
        switch (message.type) {
          case 'updateSelectionContext': {
            const selection = message.selection;
            if (selection && selection.selectText) {
              const selectInfo = selection.selectInfo;
              let selectionFile =
                '/' +
                selectInfo?.filePath +
                `: L${selectInfo?.startLine}#${selectInfo?.startCharacter}-L${selectInfo?.endLine}#${selectInfo?.endCharacter}`;
              updateHighlights({
                ...selectInfo,
                selectText: selection.selectText,
                filePath: selectInfo?.filePath,
                selectionFileText: `@${selectionFile}`,
              });
            } else {
              delContextItem();
            }
            break;
          }
          case 'state':
          default: {
            break;
          }
        }
      },
      [updateHighlights],
    );

    useEvent('message', handleMessage);

    useEffect(() => {
      filePathsRef.current = filePaths;
    }, [filePaths]);

    return (
      <ChatTextAreaAdaptor
        searchQuery={searchQuery}
        isTextAreaFocused={isTextAreaFocused}
        handleInputChange={handleInputChange}
        handleKeyDown={handleKeyDown}
        handleKeyUp={handleKeyUp}
        handleMentionSelect={handleMentionSelect}
        showContextMenu={showContextMenu}
        setShowContextMenu={setShowContextMenu}
        textAreaDisabled={textAreaDisabled}
        shouldDisableImages={shouldDisableImages}
        ref={ref}
        contextMenuContainerRef={contextMenuContainerRef}
        queryItems={queryItems}
        updateStatus={updateStatus}
        selectedMenuIndex={selectedMenuIndex}
        setSelectedMenuIndex={setSelectedMenuIndex}
        selectedType={selectedType}
        handleMenuMouseDown={handleMenuMouseDown}
        highlightLayerRef={highlightLayerRef}
        contextLayerRef={contextLayerRef}
        thumbnailsHeight={thumbnailsHeight}
        textAreaRef={textAreaRef}
        inputValue={inputValue}
        updateHighlights={updateHighlights}
        handleBlur={handleBlur}
        handlePaste={handlePaste}
        updateCursorPosition={updateCursorPosition}
        textAreaBaseHeight={textAreaBaseHeight}
        onHeightChange={onHeightChange}
        placeholderText={placeholderText}
        selectedImages={selectedImages}
        onSend={onSend}
        promptList={promptList}
        handleThumbnailsHeightChange={handleThumbnailsHeightChange}
        onSelectImages={onSelectImages}
        setShowPromptView={setShowPromptView}
        setIsTextAreaFocused={(e) => {
          setIsTextAreaFocused(e);
        }}
        setTextAreaBaseHeight={(e) => {
          setTextAreaBaseHeight(e);
        }}
        setSelectedImages={setSelectedImages}
        isStreaming={isStreaming}
        messages={messages}
        showSlashCommandsMenu={showSlashCommandsMenu}
        selectedSlashCommandsIndex={selectedSlashCommandsIndex}
        slashCommandsQuery={slashCommandsQuery}
        handleSlashCommandsSelect={handleSlashCommandsSelect}
        setSelectedSlashCommandsIndex={setSelectedSlashCommandsIndex}
        slashCommandsMenuContainerRef={slashCommandsMenuContainerRef}
        handleSecondaryButtonClick={handleSecondaryButtonClick}
      />
    );
  },
);

export default ChatTextArea;

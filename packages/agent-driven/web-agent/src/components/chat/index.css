.joycoder-chatgpt-model-dropdown {
  cursor: pointer;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top{
  box-sizing: border-box;
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  min-width: 250px !important;
  z-index: 1200;
  top: -9999px;
  display: none;
  left: -9999px;
}
.joycoder-chatgpt-model-dropdown.ant-dropdown-open .ant-dropdown-placement-top{
  display: block;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu{
  position: relative;
  padding: 4px 0;
  text-align: left;
  background-color: #1f1f1f;
  background-clip: padding-box;
  border-radius: 2px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  min-width: 250px;
  overflow: hidden;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item{
  clear: both;
  margin: 0;
  /* padding: 5px 12px 5px 10px; */
  color: rgba(255, 255, 255, 0.85);
  font-weight: normal;
  font-size: 14px;
  line-height: 22px;
  cursor: pointer !important;
  transition: all 0.3s;
  overflow: hidden;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu {
  max-width: 250px;
  font-size: 11px;
  padding: 5px 12px 5px 10px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_action {
  display: inline-flex;
  align-items: center;
  color: #D9D9D9;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_content {
  display: inline-flex;
  flex-direction: column;
  margin-left: 5px;
  font-size: 11px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_content_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_content_header_icons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_btn {
  margin-left: 4px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_content_tip {
  max-width: 200px;
  padding-right: 12px;
  font-size: 9px;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0.55;
}
.joycoder-chatgpt-model-dropdown .joycoder-dark-dropdown-menu-title-content {
  flex: auto;
}
.joycoder-chatgpt-model-dropdown .model-icon{
  width: 12px;
  height: 12px;
  /* padding-bottom: 1px; */
  border-radius: 50%;
  overflow: hidden;
  vertical-align: middle;
  border-style: none;
}
.joycoder-chatgpt-model-dropdown .model-icon + span{
  font-size: 11px;
  /* padding-bottom: 1px; */
  max-width: 90%;
  margin-left: 4px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  display: inline-block;
  color: rgba(255, 255, 255, 0.85);
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--recommend{
  color: #e36459 !important;
  padding: 2px;
  border-radius: 2px;
  font-size: 9px !important;
  background: rgba(227, 100, 89, 0.15);
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--function {
  color: #55b467 !important;
  padding: 2px;
  border-radius: 2px;
  font-size: 9px !important;
  background: rgba(85, 180, 103, 0.15);
  margin-left: 4px;
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--tokens {
  font-size: 9px !important;
  padding: 0px 2px;
  display: block;
  line-height: 12px;
  border-radius: 2px;
  background: #333;
  color: #999;
  margin-left: 4px;
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--vision {
  margin-left: 4px;
  color: #369eff !important;
  padding: 2px;
  border-radius: 2px;
  font-size: 9px !important;
  background: rgba(54, 158, 255, 0.15);
}
.joycoder-chatgpt-input-icons .codicon-send.codicon[class*='codicon-'] {
  transform: rotate(-30deg);
  height: 17px;
}
.joycoder-chatgpt-input-icons .codicon-send:before {
  content: "\ec0f";
  background: linear-gradient(197.97deg, rgba(76, 213, 255, 1) 0%, rgba(54, 87, 255, 1) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.joycoder-chatgpt-input-icons .codicon-device-camera:hover {
  background-color: rgba(255,255,255,0.08);
  border-radius: 2px;
}
.joycoder-chatgpt-input-icons .codicon-device-camera:before {
  content: "";
}
.mention-context-textarea-highlight-layer-inner{
  width: 100%;
  border-radius: 6px !important;
  box-sizing: border-box;
  overflow: hidden;
  padding: 1px;
  margin: -1px;
}
.mention-context-textarea-highlight-layer-inner.focus {
  background: linear-gradient(241.16deg, rgba(76,213,255,1) 0%,rgba(54,87,255,1) 100%);
}
.vscode-light .mention-context-textarea-highlight-layer-inner {
  background: #cacaca;
}
.mention-context-textarea-highlight-layer-inner:hover {
  background: linear-gradient(241.16deg, rgba(76,213,255,1) 0%,rgba(54,87,255,1) 100%) !important;
}
.joycoder-chatgpt-model-dropdown-button:hover{
  background: rgba(255,255,255,0.08) !important;
}
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top{
  color: rgba(0, 0, 0, 0.85);
}
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu{
  background-color: rgba(31, 31, 31, 0.1);
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
}
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu{
  background-color: #ffff;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
}
.joycoder-login-btn{
  color: rgba(76,213,255,1) !important;
  cursor: pointer;
}

.vscode-light .joycoder-termination {
  width: 20px;
  height: 20px;
  font-size: 15px;
  background: url(https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/light-termination.svg) no-repeat bottom center;
  cursor: pointer;
}
.joycoder-termination {
  width: 60px !important;
  height: 20px;
  font-size: 15px;
  background: url(https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/termination.svg) no-repeat bottom left;
  cursor: pointer;
  opacity: 0.8;
  background-position: bottom left !important;

}
.joycoder-termination::after {
  content: '停止';
  font-size: 12px;
  margin-left: 22px;
}

.joycoder-task-box li{
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: normal;
}

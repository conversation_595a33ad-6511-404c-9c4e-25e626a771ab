import { VSCodeBadge } from '@vscode/webview-ui-toolkit/react';
import deepEqual from 'fast-deep-equal';
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useEvent, useSize } from 'react-use';
import styled from 'styled-components';
import {
  JoyCoderApiReqInfo,
  JoyCoderAskUseMcpServer,
  JoyCoderMessage,
  JoyCoderSayTool,
  COMPLETION_RESULT_CHANGES_FLAG,
  ExtensionMessage,
  ButtonText,
  JoyCoderPlanModeResponse,
  JoyCoderAskQuestion,
  JoyCoderSayText,
} from '../../../../src/shared/ExtensionMessage';
import { COMMAND_OUTPUT_STRING, COMMAND_REQ_APP_STRING } from '../../../../src/shared/combineCommandSequences';
import { useExtensionState } from '../../context/ExtensionStateContext';
import { findMatchingResourceOrTemplate } from '../../utils/mcp';
import { vscode } from '../../utils/vscode';
import { CheckpointControls, CheckpointOverlay } from '../common/CheckpointControls';
import CodeAccordianAdaptor, { cleanPathPrefix } from '../../adaptor/components/CodeAccordianAdaptor';
import CodeBlock, { CODE_BLOCK_BG_COLOR } from '../common/CodeBlock';
import MarkdownBlock from '../common/MarkdownBlock';
import SuccessButton from '../common/SuccessButton';
import Thumbnails from '../common/Thumbnails';
import McpResourceRow from '../mcp/McpResourceRow';
import McpToolRow from '../mcp/McpToolRow';
import { highlightMentions } from '../../adaptor/components/TaskHeaderAdaptor';
import { OptionsButtons } from './OptionsButtons';
import NewTaskPreview from './NewTaskPreview';
import FileList from './FileList';
import { CommandExecution } from './CommandExecution';
import { safeJsonParse } from '../../utils/safeJsonParse';
import { FollowUpSuggest } from './FollowUpSuggest';
import { ActionType, IActionCustomReportParam } from '@joycoder/shared/src/report/ationType';
import { modes, getModeBySlug } from '../../utils/modes';
import ModeInfo from '../common/ModeInfo';
import { MessageToolbar } from '../chat/MessageToolbar';

const ChatRowContainer = styled.div<{ $isUserMessage: boolean; $isLastMessage: boolean; $isMiddleAIMessage: boolean }>`
  padding: ${props => {
    // 用户消息或最后一条任何消息：padding: 10px 6px 10px 15px
    if (props.$isUserMessage || props.$isLastMessage) {
      return '10px 6px 10px 15px';
    }
    // AI消息且为中间的AI消息：padding: 10px 6px 0 15px
    if (props.$isMiddleAIMessage) {
      return '10px 6px 0 15px';
    }
    // 默认值
    return '10px 6px 10px 15px';
  }};
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;

  &:hover ${CheckpointControls} {
    opacity: 1;
  }
`;

const ContentContainer = styled.div`
  flex: 1;
  min-width: 0;
  position: relative;

  &:hover .message-toolbar {
    opacity: 1;
  }
`;

// 折叠文本组件
const CollapsibleText = memo(({
  text,
  maxLines = 15,
  backgroundColor = 'var(--vscode-badge-background)',
  textColor = 'var(--vscode-badge-foreground)',
  align = 'left'
}: {
  text: string;
  maxLines?: number;
  backgroundColor?: string;
  textColor?: string;
  align?: 'left' | 'right';
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showSeeMore, setShowSeeMore] = useState(false);
  const textContainerRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (textRef.current && textContainerRef.current) {
      const { scrollHeight } = textRef.current;
      const { clientHeight } = textContainerRef.current;
      const isOverflowing = scrollHeight > clientHeight;
      setShowSeeMore(isOverflowing);
    }
  }, [text]);

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: align === 'right' ? 'flex-end' : 'flex-start',
        width: '100%',
      }}
    >
      <div
        ref={textContainerRef}
        style={{
          backgroundColor,
          color: textColor,
          borderRadius: '3px',
          padding: showSeeMore && !isExpanded ? '6px 8px 24px 8px' : '6px 8px',
          whiteSpace: 'pre-line',
          wordWrap: 'break-word',
          position: 'relative',
          overflow: 'hidden',
          maxWidth: '100%',
          minWidth: 'auto',
        }}
      >
        <div
        ref={textRef}
        style={{
          display: '-webkit-box',
          WebkitLineClamp: isExpanded ? 'unset' : maxLines,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          lineHeight: '18px'
        }}
      >
        {highlightMentions(text)}
      </div>
      {!isExpanded && showSeeMore && (
        <div
          style={{
            position: 'absolute',
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
          }}
          onClick={() => setIsExpanded(true)}
        >
          <div
            style={{
              // color: 'var(--vscode-textLink-foreground)',
              backgroundColor,
              fontSize: 'inherit',
              flex: 1,
              textAlign: 'center',
              height: '24px',
              background: `linear-gradient(180deg, rgba(32,32,35,0) 0%,rgba(32,32,35,1) 89.99999761581421%)`,
              paddingTop: '4px'
            }}
          >
            <span className="icon iconfont icon-xiajiantou"/>
          </div>
        </div>
      )}
      {isExpanded && (
        <div
          style={{
            marginTop: '8px',
            display: 'flex',
            justifyContent: 'flex-end',
            cursor: 'pointer',
            width: '100%',
          }}
          onClick={() => setIsExpanded(false)}
        >
          <div
            style={{
              // color: 'var(--vscode-textLink-foreground)',
              fontSize: 'inherit',
              width: '100%',
              textAlign: 'center',
            }}
          >
            <span className="icon iconfont icon-shangjiantou"/>
          </div>
        </div>
      )}
      </div>
    </div>
  );
});



interface ChatRowProps {
  message: JoyCoderMessage;
  isExpanded: boolean;
  onToggleExpand: () => void;
  lastModifiedMessage?: JoyCoderMessage;
  isLast: boolean;
  onHeightChange: (isTaller: boolean) => void;
  handleSecondaryButtonClick?: () => void;
  handlePrimaryButtonClick?: () => void;
  isStreaming?: boolean;
  buttonText?: ButtonText;
  buttonInfo?: Record<string, any>;
  onCopy?: () => void;
  currentModeSlug?: string;
  currentMode?: any; // 完整的模式对象，包含头像、名称等
  allMessages?: (JoyCoderMessage | JoyCoderMessage[])[];
  messageIndex?: number;
}

interface ChatRowContentProps extends Omit<ChatRowProps, 'onHeightChange'> {
  isStreaming?: boolean;
}

const ChatRow = memo(
  (props: ChatRowProps) => {
    const { isLast, onHeightChange, message, lastModifiedMessage, currentModeSlug, currentMode, allMessages, messageIndex } = props;
    // 存储前一个高度以便与当前高度进行比较
    // 这允许我们检测变化而不会导致重新渲染
    const prevHeightRef = useRef(0);

    // 使用ref存储currentMode以避免它成为依赖项
    const currentModeRef = useRef(currentMode);
    currentModeRef.current = currentMode;

    // 获取模式信息并确定这是否是用户消息以及是否应该显示头像
    const { displayMode, shouldShowAvatar } = useMemo(() => {
      // 检查这是否是用户消息
      const isUser = message.say === 'user_feedback' ||
                     message.say === 'user_feedback_diff' ||
                     // 特殊情况：第一条消息可能是say: "text"但实际是用户输入
                     (message.say === 'text' && message.conversationHistoryIndex === -1);

      // 检查这是否是不应该显示头像的检查点或过渡消息
      const isCheckpointOrTransition =
        message.say === 'checkpoint_created' ||
        message.ask === 'resume_completed_task' ||
        message.ask === 'resume_task' ||
        message.say === 'api_req_started' ||
        message.say === 'api_req_finished' ||
        message.say === 'api_req_retried' ||
        message.say === 'deleted_api_reqs';

      if (isCheckpointOrTransition) {
        return {
          displayMode: null,
          shouldShowAvatar: false
        };
      }

      // 检查是否应该与前一条消息合并（为连续的AI消息隐藏头像）
      const shouldMergeWithPrevious = () => {
        if (!allMessages || messageIndex === undefined || messageIndex === 0) {
          return false;
        }

        const prevMessage = allMessages[messageIndex - 1];
        if (!prevMessage) {
          return false;
        }

        // 处理前一条消息可能是数组的情况（分组消息）
        const actualPrevMessage = Array.isArray(prevMessage) ? prevMessage[prevMessage.length - 1] : prevMessage;
        if (!actualPrevMessage) {
          return false;
        }

        // 如果当前消息是用户消息，总是显示头像
        if (isUser) {
          return false;
        }

        // 如果前一条消息是用户消息，不合并（为第一个AI响应显示头像）
        const prevIsUser = actualPrevMessage.say === 'user_feedback' ||
                          actualPrevMessage.say === 'user_feedback_diff' ||
                          // 特殊情况：第一条消息可能是say: "text"但实际是用户输入
                          (actualPrevMessage.say === 'text' && actualPrevMessage.conversationHistoryIndex === -1);
        if (prevIsUser) {
          return false;
        }

        // 如果前一条消息是检查点/过渡消息，不合并
        const prevIsCheckpointOrTransition =
          actualPrevMessage.say === 'checkpoint_created' ||
          actualPrevMessage.ask === 'resume_completed_task' ||
          actualPrevMessage.ask === 'resume_task' ||
          actualPrevMessage.say === 'api_req_started' ||
          actualPrevMessage.say === 'api_req_finished' ||
          actualPrevMessage.say === 'api_req_retried' ||
          actualPrevMessage.say === 'deleted_api_reqs';

        if (prevIsCheckpointOrTransition) {
          return false;
        }

        // 当前和前一条都是AI消息，所以合并（隐藏当前头像）
        return true;
      };

      if (isUser) {
        // 对于用户消息，不显示头像
        return {
          displayMode: {
            name: '用户',
            avatar: '', // 使用空字符串触发回退到首字母
            agentId: 'user'
          },
          shouldShowAvatar: false
        };
      } else {
        // 对于AI消息，检查是否应该与前一条合并
        // 优先级：1. 消息存储的模式信息，2. 传递的currentMode，3. 从slug获取
        let mode;
        if (message.modeInfo) {
          // 使用消息中存储的模式信息（保留历史模式）
          mode = {
            agentId: message.modeInfo.agentId,
            name: message.modeInfo.name,
            avatar: message.modeInfo.avatar
          };
        } else {
          // 对于没有存储模式信息的消息，回退到当前模式
          mode = currentModeRef.current || (currentModeSlug ? (getModeBySlug(currentModeSlug) || modes[0]) : modes[0]);
        }

        const shouldMerge = shouldMergeWithPrevious();

        // 确保模式具有正确的头像处理
        const processedMode = {
          ...mode,
          // 保留原始头像值，仅在真正未定义/null时回退
          avatar: mode.avatar != null ? mode.avatar : '',
          // 确保名称存在以供回退
          name: mode.name || '智能体'
        };



        return {
          displayMode: processedMode,
          shouldShowAvatar: !shouldMerge
        };
      }
    }, [currentModeSlug, message.say, message.ask, message.modeInfo, allMessages, messageIndex]);

    // 注意：对于被中断且未响应（批准或拒绝）的工具，不会有检查点哈希
    let shouldShowCheckpoints =
      message.lastCheckpointHash != null &&
      (message.say === 'tool' ||
        message.ask === 'tool' ||
        message.say === 'command' ||
        message.ask === 'command' ||
        // message.say === "completion_result" ||
        // message.ask === "completion_result" ||
        message.say === 'use_mcp_server' ||
        message.ask === 'use_mcp_server');

    if (shouldShowCheckpoints && isLast) {
      shouldShowCheckpoints =
        lastModifiedMessage?.ask === 'resume_completed_task' || lastModifiedMessage?.ask === 'resume_task';
    }

    // 辅助函数：检查是否是checkpoint或transition消息
    const isCheckpointOrTransitionMessage = useCallback((msg: JoyCoderMessage) => {
      return (
        msg.say === 'checkpoint_created' ||
        msg.ask === 'resume_completed_task' ||
        msg.ask === 'resume_task' ||
        msg.say === 'api_req_started' ||
        msg.say === 'api_req_finished' ||
        msg.say === 'api_req_retried' ||
        msg.say === 'deleted_api_reqs'
      );
    }, []);

    // 计算消息类型和位置信息用于样式
    const messageStyleInfo = useMemo(() => {
      // 检查是否是用户消息
      const isUserMessage = message.say === 'user_feedback' ||
                            message.say === 'user_feedback_diff' ||
                            // 特殊情况：第一条消息可能是say: "text"但实际是用户输入
                            (message.say === 'text' && message.conversationHistoryIndex === -1);

      // 检查是否是最后一条消息
      const isLastMessage = isLast;

      // 检查是否是中间的AI消息
      let isMiddleAIMessage = false;

      // 如果不是用户消息且不是最后一条消息，检查是否是中间的AI消息
      if (!isUserMessage && !isLastMessage) {
        // 检查是否是AI消息（非checkpoint/transition消息）
        const isAI = !isCheckpointOrTransitionMessage(message);

        if (isAI && allMessages && messageIndex !== undefined) {
          // 检查下一条消息是否也是AI消息
          const nextMessage = allMessages[messageIndex + 1];
          if (nextMessage) {
            const actualNextMessage = Array.isArray(nextMessage) ? nextMessage[0] : nextMessage;
            if (actualNextMessage) {
              const nextIsAI = !isCheckpointOrTransitionMessage(actualNextMessage) &&
                              actualNextMessage.say !== 'user_feedback' &&
                              actualNextMessage.say !== 'user_feedback_diff' &&
                              // 排除特殊的用户输入消息
                              !(actualNextMessage.say === 'text' && actualNextMessage.conversationHistoryIndex === -1);
              isMiddleAIMessage = nextIsAI;
            }
          }
        }
      }

      return {
        isUserMessage,
        isLastMessage,
        isMiddleAIMessage
      };
    }, [message, isLast, allMessages, messageIndex, isCheckpointOrTransitionMessage]);

    // 检查是否应该渲染整个ChatRow
    const shouldRenderChatRow = useMemo(() => {
      // 对于checkpoint_created消息，如果不显示工具条且不显示头像，则不渲染整个容器
      if (message.say === 'checkpoint_created') {
        // 重新计算shouldShowToolbar的逻辑，避免依赖外部变量
        let showToolbar = false;

        // checkpoint_created消息的特殊处理
        // 查找前面最近的非checkpoint/transition消息
        let foundNonTransitionMessage = false;
        if (allMessages && messageIndex !== undefined) {
          // 向前查找最近的非checkpoint/transition消息
          for (let i = messageIndex - 1; i >= 0; i--) {
            const prevMsg = allMessages[i];
            const actualPrevMsg = Array.isArray(prevMsg) ? prevMsg[prevMsg.length - 1] : prevMsg;
            if (!actualPrevMsg) continue;

            // 如果找到非checkpoint/transition消息，说明前面有消息会显示工具条（包含回滚按钮）
            const isCheckpointOrTransition =
              actualPrevMsg.say === 'checkpoint_created' ||
              actualPrevMsg.ask === 'resume_completed_task' ||
              actualPrevMsg.ask === 'resume_task' ||
              actualPrevMsg.say === 'api_req_started' ||
              actualPrevMsg.say === 'api_req_finished' ||
              actualPrevMsg.say === 'api_req_retried' ||
              actualPrevMsg.say === 'deleted_api_reqs';

            if (!isCheckpointOrTransition) {
              foundNonTransitionMessage = true;
              break;
            }
          }
        }

        // 如果前面有非checkpoint/transition消息，则当前checkpoint消息不显示工具条
        // 因为前面的消息工具条会包含回滚按钮
        if (foundNonTransitionMessage) {
          showToolbar = false;
        } else {
          // 如果前面没有非checkpoint/transition消息，则在当前checkpoint消息显示工具条（只显示回滚按钮）
          showToolbar = true;
        }

        return shouldShowAvatar || showToolbar;
      }
      // 其他消息类型正常渲染
      return true;
    }, [message.say, shouldShowAvatar, allMessages, messageIndex]);

    const [chatrow, { height }] = useSize(
      shouldRenderChatRow ? (
        <ChatRowContainer
          $isUserMessage={messageStyleInfo.isUserMessage}
          $isLastMessage={messageStyleInfo.isLastMessage}
          $isMiddleAIMessage={messageStyleInfo.isMiddleAIMessage}
        >
          {shouldShowAvatar && displayMode && (
            <ModeInfo
              name={displayMode.name}
              avatar={displayMode.avatar}
              showAvatar={true}
            />
          )}
          <ContentContainer>
            <ChatRowContent {...props} />
          </ContentContainer>
          {shouldShowCheckpoints && <CheckpointOverlay messageTs={message.ts} />}
        </ChatRowContainer>
      ) : (
        <div style={{ height: 0, overflow: 'hidden' }} />
      ),
    );

    useEffect(() => {
      // 用于部分内容、命令输出等
      // 注意：这里不区分部分或完整很重要，因为我们在chatview中的滚动效果需要处理从部分到完整的高度变化
      const isInitialRender = prevHeightRef.current === 0; // 防止添加新元素时滚动，因为我们已经为此滚动了
      // 高度从无穷大开始
      if (isLast && height !== 0 && height !== Infinity && height !== prevHeightRef.current) {
        if (!isInitialRender) {
          onHeightChange(height > prevHeightRef.current);
        }
        prevHeightRef.current = height;
      }
    }, [height, isLast, onHeightChange, message]);

    // 我们不能返回null，因为virtuoso不支持它，所以我们使用单独的visibleMessages数组来过滤不应该渲染的消息
    return chatrow;
  },
  // memo进行浅比较，所以我们需要对可能改变属性的数组/对象进行深比较
  deepEqual,
);

export default ChatRow;

export const ChatRowContent = ({
  message,
  isExpanded,
  onToggleExpand,
  lastModifiedMessage,
  isLast,
  onCopy,
  allMessages,
  messageIndex,
}: ChatRowContentProps) => {
  const { mcpServers } = useExtensionState();

  // 判断消息是否正在生成中
  const isMessageGenerating = useMemo(() => {
    // 检查消息是否为partial状态
    if (message.partial === true) {
      return true;
    }

    // 检查是否是最后一条消息且正在流式传输
    if (isLast && lastModifiedMessage) {
      // 如果最后修改的消息是当前消息且为partial状态
      if (lastModifiedMessage.ts === message.ts && lastModifiedMessage.partial === true) {
        return true;
      }
    }

    return false;
  }, [message.partial, message.ts, isLast, lastModifiedMessage]);

  // 判断消息是否为用户消息
  const isUserMessage = useCallback((msg: JoyCoderMessage) => {
    return msg.say === 'user_feedback' ||
           msg.say === 'user_feedback_diff' ||
           // 特殊情况：第一条消息可能是say: "text"但实际是用户输入
           (msg.say === 'text' && msg.conversationHistoryIndex === -1);
  }, []);

  // 判断消息是否为checkpoint或transition消息
  const isCheckpointOrTransitionMessage = useCallback((msg: JoyCoderMessage) => {
    return (
      msg.say === 'checkpoint_created' ||
      msg.ask === 'resume_completed_task' ||
      msg.ask === 'resume_task' ||
      msg.say === 'api_req_started' ||
      msg.say === 'api_req_finished' ||
      msg.say === 'api_req_retried' ||
      msg.say === 'deleted_api_reqs'
    );
  }, []);

  // 判断消息是否为AI消息（非用户消息且非checkpoint/transition消息）
  const isAIMessage = useCallback((msg: JoyCoderMessage) => {
    return !isUserMessage(msg) && !isCheckpointOrTransitionMessage(msg);
  }, [isUserMessage, isCheckpointOrTransitionMessage]);

  // 简化工具条显示逻辑，让MessageToolbar组件自己决定是否显示
  const shouldShowToolbar = useMemo(() => {
    // 用户消息总是尝试显示工具条
    if (isUserMessage(message)) {
      return true;
    }

    // checkpoint_created消息的特殊处理
    if (message.say === 'checkpoint_created') {
      // 查找前面最近的非checkpoint/transition消息
      let foundNonTransitionMessage = false;
      if (allMessages && messageIndex !== undefined) {
        // 向前查找最近的非checkpoint/transition消息
        for (let i = messageIndex - 1; i >= 0; i--) {
          const prevMsg = allMessages[i];
          const actualPrevMsg = Array.isArray(prevMsg) ? prevMsg[prevMsg.length - 1] : prevMsg;
          if (!actualPrevMsg) continue;

          // 如果找到非checkpoint/transition消息，说明前面有消息会显示工具条（包含回滚按钮）
          if (!isCheckpointOrTransitionMessage(actualPrevMsg)) {
            foundNonTransitionMessage = true;
            break;
          }
        }
      }

      // 如果前面有非checkpoint/transition消息，则当前checkpoint消息不显示工具条
      // 因为前面的消息工具条会包含回滚按钮
      if (foundNonTransitionMessage) {
        return false;
      }

      // 如果前面没有非checkpoint/transition消息，则在当前checkpoint消息显示工具条（只显示回滚按钮）
      return true;
    }

    // resume_task消息明确不显示工具条
    if (message.ask === 'resume_task') {
      return false;
    }

    // 其他checkpoint或transition消息不显示工具条
    if (isCheckpointOrTransitionMessage(message)) {
      return false;
    }

    // AI消息总是尝试显示工具条，让MessageToolbar组件内部决定具体逻辑
    if (isAIMessage(message)) {
      return true;
    }

    // 默认显示工具条
    return true;
  }, [message, allMessages, messageIndex, isUserMessage, isCheckpointOrTransitionMessage, isAIMessage]);

  const [seeNewChangesDisabled, setSeeNewChangesDisabled] = useState(false);
  // const [lastOne, setLastOne] = useState(false);

  const [cost, apiReqCancelReason, apiReqStreamingFailedMessage] = useMemo(() => {
    if (message.text != null && message.say === 'api_req_started') {
      const info: JoyCoderApiReqInfo = JSON.parse(message.text);
      return [info.cost, info.cancelReason, info.streamingFailedMessage];
    }
    return [undefined, undefined, undefined];
  }, [message.text, message.say]);
  // 恢复任务时，最后一条不会是api_req_failed而是resume_task消息，所以api_req_started会显示加载旋转器。这就是为什么我们只是删除最后一个没有流式传输任何内容就失败的api_req_started
  const apiRequestFailedMessage =
    isLast && lastModifiedMessage?.ask === 'api_req_failed' // 如果请求被重试，那么最新的消息是api_req_retried
      ? lastModifiedMessage?.text
      : undefined;
  const isCommandExecuting =
    isLast &&
    (lastModifiedMessage?.ask === 'command' || lastModifiedMessage?.say === 'command') &&
    lastModifiedMessage?.text?.includes(COMMAND_OUTPUT_STRING);

  const isMcpServerResponding = isLast && lastModifiedMessage?.say === 'mcp_server_request_started';

  const type = message.type === 'ask' ? message.ask : message.say;

  const normalColor = 'var(--vscode-foreground)';
  const errorColor = 'var(--vscode-errorForeground)';
  const warnColor = 'var(--vscode-foreground)';
  // const successColor = 'var(--vscode-charts-green)';
  const infoColor = 'var(--vscode-charts-blue)';
  const cancelledColor = 'var(--vscode-descriptionForeground)';
  const headerClasses = 'flex items-center gap-[10px] mb-[10px] break-words';

  const handleMessage = useCallback((event: MessageEvent) => {
    const message: ExtensionMessage = event.data;
    switch (message.type) {
      case 'relinquishControl': {
        setSeeNewChangesDisabled(false);
        break;
      }
    }
  }, []);

  useEvent('message', handleMessage);

  const [icon, title] = useMemo(() => {
    switch (type) {
      case 'error':
        return [
          <span
            className="codicon codicon-warning"
            style={{
              color: warnColor,
              marginBottom: '-1.5px',
            }}
          ></span>,
          <span style={{ color: warnColor, fontWeight: 'bold' }}>错误</span>,
        ];
      case 'mistake_limit_reached':
        return [
          <span
            className="codicon codicon-warning"
            style={{
              color: warnColor,
              marginBottom: '-1.5px',
            }}
          ></span>,
          <span style={{ color: warnColor, fontWeight: 'bold' }}>JoyCode 遇到问题了...</span>,
        ];
      case 'auto_approval_max_req_reached':
        return [
          <span
            className="codicon codicon-warning"
            style={{
              color: warnColor,
              marginBottom: '-1.5px',
            }}
          ></span>,
          <span style={{ color: warnColor, fontWeight: 'bold' }}>已达到最大请求数</span>,
        ];
      case 'command':
        return [
          isCommandExecuting ? (
            <div
              style={{
                margin: '10px 0 0 10px',
              }}
            >
              <img style={{ width: '84px'}} src='https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/thinking.gif' alt='thinking' />
            </div>
          ) : (
            <span
              className="codicon codicon-terminal"
              style={{
                color: normalColor,
                margin: '10px 0 0 10px',
              }}
            ></span>
          ),
          <span style={{ color: normalColor, fontWeight: 'bold' }}>JoyCode 想要执行这个命令:</span>,
        ];
      case 'use_mcp_server':
        const mcpServerUse = JSON.parse(message.text || '{}') as JoyCoderAskUseMcpServer;
        return [
          isMcpServerResponding ? (
            <img style={{ width: '84px'}} src='https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/thinking.gif' alt='thinking' />
          ) : (
            <span
              className="codicon codicon-server"
              style={{
                color: normalColor,
                marginBottom: '-1.5px',
              }}
            ></span>
          ),
          <span style={{ color: normalColor, fontWeight: 'bold' }}>
            JoyCode 想要在 <code>{mcpServerUse.serverName}</code> MCP 服务器上
            {mcpServerUse.type === 'use_mcp_tools' ? '使用一个工具' : '访问一个资源'}：
          </span>,
        ];
      case 'completion_result':
        return [
          <span
            className="codicon codicon-check hidden"
            style={{
              color: infoColor,
              marginBottom: '-1.5px',
              display: 'none',
            }}
          ></span>,
          <span style={{ color: infoColor, fontWeight: 'bold' }} className="hidden">
            任务结束
          </span>,
        ];
      case 'api_req_started':
        const getIconSpan = (iconName: string, color: string) => (
          <div
            style={{
              width: 16,
              height: 16,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <span
              className={`codicon codicon-${iconName} ${iconName === 'check' ? 'hidden' : ''}`}
              style={{
                color,
                fontSize: 16,
                marginBottom: '-1.5px',
                display: iconName === 'check' ? 'none' : 'inline-block',
              }}
            ></span>
          </div>
        );
        return [
          // 请求取消或失败的时候，显示对应的图标入参 
          apiReqCancelReason != null ? (
            apiReqCancelReason === 'user_cancelled' ? (
              getIconSpan('error', cancelledColor)
            ) : (
              getIconSpan('warning', normalColor)
            )
          ) : cost != null ? (
            getIconSpan('check', infoColor)
          ) : apiRequestFailedMessage ? (
            getIconSpan('warning', normalColor)
          ) : (
            // 请求中的时候，显示思考中动画
            <img style={{ width: '84px'}} src='https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/thinking.gif' alt='thinking' />
          ),
          // 请求取消或失败的时候，显示对应的文案入参
          apiReqCancelReason != null ? (
            apiReqCancelReason === 'user_cancelled' ? (
              <span
                style={{
                  color: normalColor,
                  fontWeight: 'bold',
                }}
              >
                API 请求已取消
              </span>
            ) : (
              <span
                style={{
                  color: errorColor,
                  fontWeight: 'bold',
                }}
              >
                API 流式传输失败
              </span>
            )
          ) : cost != null ? (
            <span style={{ color: normalColor, fontWeight: 'bold' }} className="hidden">
              API 请求
            </span>
          ) : apiRequestFailedMessage ? (
            // 请求失败的时候，显示失败文案
            <span style={{ color: errorColor, fontWeight: 'bold' }}>API 请求失败</span>
          ) : (
            // 请求中的时候，显示加载文案
            <span style={{ color: normalColor, fontWeight: 'bold' }} className="hidden">
              API 请求中...
            </span>
          ),
        ];
      case 'followup':
        return [
          <span
            className="codicon codicon-question"
            style={{
              color: normalColor,
              marginBottom: '-1.5px',
            }}
          ></span>,
          <span style={{ color: normalColor, fontWeight: 'bold' }}>JoyCode 有一个问题:</span>,
        ];
      default:
        return [null, null];
    }
  }, [
    type,
    cost,
    apiRequestFailedMessage,
    isCommandExecuting,
    apiReqCancelReason,
    isMcpServerResponding,
    message.text,
  ]);

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
    marginBottom: '12px',
  };

  const pStyle: React.CSSProperties = {
    margin: 0,
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    overflowWrap: 'anywhere',
  };

  const tool: any = useMemo(() => {
    if (message.ask === 'tool' || message.say === 'tool') {
      return JSON.parse(message.text || '{}') as JoyCoderSayTool;
    }
    return null;
  }, [message.ask, message.say, message.text]);

  const followUpData = useMemo(() => {
    if (message.type === 'ask' && message.ask === 'followup' && !message.partial) {
      return safeJsonParse<any>(message.text);
    }
    return null;
  }, [message.type, message.ask, message.partial, message.text]);

  const toolIcon = (name: string) => (
    <span
      className={`codicon codicon-${name}`}
      style={{
        color: 'var(--vscode-foreground)',
        margin: '0 10px -1.5px 0',
      }}
    ></span>
  );
  const [hasChanges, setHasChanges] = useState(false);
  const messageText = useMemo(() => {
    if (message.say !== 'text' && message.say !== 'completion_result') {
      setHasChanges(false);
      return null;
    }
    let jsonaToParse = message.text;
    if (message.say === 'completion_result' && jsonaToParse?.endsWith(COMPLETION_RESULT_CHANGES_FLAG)) {
      setHasChanges(true);
      jsonaToParse = jsonaToParse.slice(0, -COMPLETION_RESULT_CHANGES_FLAG.length);
    } else {
      setHasChanges(false);
    }
    try {
      return JSON.parse(jsonaToParse || '{}') as JoyCoderSayText;
    } catch (e) {
      return { text: jsonaToParse } as JoyCoderSayText;
    }
  }, [message.say, message.text]);

  const reportAction = (data: IActionCustomReportParam) => {
    vscode.postMessage({
      type: 'chatgpt-webview-report',
      reportData: data,
    });
  };
  const handleCopy = useCallback(() => {
    if (!messageText || !messageText.taskId || !messageText.conversationId || !messageText.sessionId) return;
    reportAction({
      actionCate: 'ai',
      accept: 1,
      actionType: ActionType.copy,
      result: window.getSelection()?.toString(),
      conversationId: messageText.conversationId,
      // model: GlobalState.get('openAiModelId'),
      extendMsg: {
        taskId: messageText.taskId,
        sessionId: messageText.sessionId,
      },
    });
  }, [messageText]);

  if (tool) {
    switch (tool.tool) {
      case 'appliedDiff':
      case 'editedExistingFile':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon(tool.tool === 'appliedDiff' ? 'diff' : 'edit')}
              <span style={{ fontWeight: 'bold' }}>JoyCode 想要编辑这个文件 :</span>
            </div>
            <CodeAccordianAdaptor
              progressStatus={message.progressStatus}
              icon={toolIcon(tool.tool === 'appliedDiff' ? 'diff' : 'edit')}
              code={tool.diff || tool.content}
              diff={tool.diff || tool.content}
              path={tool.path!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
            {/* 为工具调用消息添加工具条 */}
            <div style={{ position: 'relative' }}>
              <MessageToolbar
                text={tool.diff || tool.content || ''}
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />
            </div>
          </>
        );
      case 'insertContent':
        return (
          <>
            <div className={headerClasses}>
              {toolIcon('insert')}
              <span className="font-bold">
                {tool.isOutsideWorkspace
                  ? '想要在工作区外进行编辑'
                  : tool.lineNumber === 0
                    ? '想要在末尾插入'
                    : `想要在第 ${tool.lineNumber} 行插入`}
              </span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('insert')}
              isLoading={message.partial}
              diff={tool.diff!}
              code={tool.diff!}
              path={tool.path!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
            {/* 为工具调用消息添加工具条 */}
            <div style={{ position: 'relative' }}>
              <MessageToolbar
                text={tool.diff || ''}
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />
            </div>
          </>
        );
      case 'searchAndReplace':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('replace')}
              <span style={{ fontWeight: 'bold' }}>
                {message.type === 'ask' ? 'JoyCode 想要搜索替换' : 'JoyCode 已完成搜索替换'}
              </span>
            </div>

            <CodeAccordianAdaptor
              progressStatus={message.progressStatus}
              isLoading={message.partial}
              code={tool.diff!}
              diff={tool.diff!}
              path={tool.path!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
            {/* 为工具调用消息添加工具条 */}
            <div style={{ position: 'relative' }}>
              <MessageToolbar
                text={tool.diff || ''}
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />
            </div>
          </>
        );
      case 'newFileCreated':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('new-file')}
              <span style={{ fontWeight: 'bold' }}>JoyCode 想要创建新文件:</span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('new-file')}
              isLoading={message.partial}
              code={tool.content!}
              path={tool.path!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
            {/* 为工具调用消息添加工具条 */}
            <div style={{ position: 'relative' }}>
              <MessageToolbar
                text={tool.content || ''}
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />
            </div>
          </>
        );
      case 'readFile':
        return (
          <>
            <div
              style={{
                borderRadius: 3,
                backgroundColor: CODE_BLOCK_BG_COLOR,
                overflow: 'hidden',
                border: '1px solid var(--vscode-editorGroup-border)',
                margin: '10px 0',
                // paddingBottom: !isStreaming ? '50px' : '0',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '9px 10px',
                  cursor: 'pointer',
                  userSelect: 'none',
                  WebkitUserSelect: 'none',
                  MozUserSelect: 'none',
                  msUserSelect: 'none',
                }}
                onClick={() => {
                  vscode.postMessage({
                    type: 'openFile',
                    text: tool.content,
                  });
                }}
              >
                {toolIcon('file-code')}
                {tool.path?.startsWith('.') && <span>.</span>}
                <span
                  style={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    marginRight: '8px',
                    direction: 'rtl',
                    textAlign: 'left',
                  }}
                >
                  {cleanPathPrefix(tool.path ?? '') + '\u200E'}
                </span>
                <div style={{ flexGrow: 1 }}></div>
                <span
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <div style={{ width: '26px' }}>审查</div>
                  <span
                    className={`codicon codicon-chevron-right`}
                    style={{
                      fontSize: 13.5,
                      margin: '1px 0',
                    }}
                  ></span>
                </span>
              </div>
            </div>
            {/* 为工具调用消息添加工具条 */}
            <div style={{ position: 'relative' }}>
              <MessageToolbar
                text={tool.content || ''}
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />
            </div>
          </>
        );
      case 'fetchInstructions':
        return (
          <>
            <div className={headerClasses}>
              {toolIcon('file-code')}
              <span className="font-bold">想要获取</span>
            </div>
            <CodeAccordianAdaptor
              isLoading={message.partial}
              code={tool.content!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
          </>
        );
      case 'listFilesTopLevel':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('folder-opened')}
              <span style={{ fontWeight: 'bold' }}>
                {message.type === 'ask' ? 'JoyCode 想要查看此目录中的顶层文件:' : 'JoyCode 查看了该目录下的顶层文件:'}
              </span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('folder-opened')}
              code={tool.content!}
              path={tool.path!}
              language="shell-session"
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
          </>
        );
      case 'listFilesRecursive':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('folder-opened')}
              <span style={{ fontWeight: 'bold' }}>
                {message.type === 'ask'
                  ? 'JoyCode 查看了该目录下的顶层文件:'
                  : 'JoyCode 递归地查看了该目录中的所有文件:'}
              </span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('folder-opened')}
              code={tool.content!}
              path={tool.path!}
              language="shell-session"
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
          </>
        );
      case 'listCodeDefinitionNames':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('file-code')}
              <span style={{ fontWeight: 'bold' }}>
                {message.type === 'ask'
                  ? 'JoyCode 想要查看在这个目录中使用的源代码定义名称:'
                  : 'JoyCode 查看了该目录中使用的源代码定义名称:'}
              </span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('file-code')}
              code={tool.content!}
              path={tool.path!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
          </>
        );
      case 'searchFiles':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('search')}
              <span style={{ fontWeight: 'bold' }}>
                JoyCode 尝试在该目录下搜索<code>{tool.regex}</code>:
              </span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('search')}
              code={tool.content!}
              path={tool.path! + (tool.filePattern ? `/(${tool.filePattern})` : '')}
              language="plaintext"
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
          </>
        );
      case 'codebaseSearch':
        return (
          tool.files &&
          tool.files.length > 0 && (
            <div>
              <FileList files={tool.files} />
            </div>
          )
        );
      case 'newTask':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('tasklist')}
              <span style={{ fontWeight: 'bold' }}>
                JoyCoder想在<code>{tool.mode}</code>模式下创建新子任务:
              </span>
            </div>
            <div
              style={{
                marginTop: '4px',
                backgroundColor: 'var(--vscode-badge-background)',
                border: '1px solid var(--vscode-badge-background)',
                borderRadius: '4px 4px 0 0',
                overflow: 'hidden',
                marginBottom: '2px',
              }}
            >
              <div
                style={{
                  padding: '9px 10px 9px 14px',
                  backgroundColor: 'var(--vscode-badge-background)',
                  borderBottom: '1px solid var(--vscode-editorGroup-border)',
                  fontWeight: 'bold',
                  fontSize: 'var(--vscode-font-size)',
                  color: 'var(--vscode-badge-foreground)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                }}
              >
                <span className="codicon codicon-arrow-right"></span>
                子任务内容
              </div>
              <div style={{ padding: '12px 16px', backgroundColor: 'var(--vscode-editor-background)' }}>
                <MarkdownBlock markdown={tool.content} />
              </div>
            </div>
          </>
        );
      case 'switchMode':
        return (
          <>
            <div className={headerClasses}>
              {toolIcon('symbol-enum')}
              <span className="font-bold">
                {tool?.reason ? (
                  <>
                    JoyCode 想要切换到 <code>{tool?.mode}</code> 模式,因为 {tool.reason}
                  </>
                ) : (
                  <>
                    JoyCode 想要切换到 <code>{tool?.mode}</code> 模式
                  </>
                )}
              </span>
            </div>
          </>
        );
      case 'finishTask':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('check-all')}
              <span style={{ fontWeight: 'bold' }}>JoyCoder想完成此子任务</span>
            </div>
            <div
              style={{
                marginTop: '4px',
                backgroundColor: 'var(--vscode-editor-background)',
                border: '1px solid var(--vscode-badge-background)',
                borderRadius: '4px',
                overflow: 'hidden',
                marginBottom: '8px',
              }}
            >
              <div
                style={{
                  padding: '9px 10px 9px 14px',
                  backgroundColor: 'var(--vscode-badge-background)',
                  borderBottom: '1px solid var(--vscode-editorGroup-border)',
                  fontWeight: 'bold',
                  fontSize: 'var(--vscode-font-size)',
                  color: 'var(--vscode-badge-foreground)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                }}
              >
                <span className="codicon codicon-check"></span>
                子任务已完成
              </div>
              <div style={{ padding: '12px 16px', backgroundColor: 'var(--vscode-editor-background)' }}>
                <MarkdownBlock markdown="子任务已完成！您可以查看结果并提出修改或下一步建议。如果一切正常，请确认以将结果返回给主任务。" />
              </div>
            </div>
          </>
        );
      case 'webSearch':
        return (
          tool.query && (
            <div
              style={{
                borderRadius: 3,
                backgroundColor: CODE_BLOCK_BG_COLOR,
                overflow: 'hidden',
                border: '1px solid var(--vscode-editorGroup-border)',
                margin: '10px 0',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '9px 10px',
                  userSelect: 'none',
                  WebkitUserSelect: 'none',
                  MozUserSelect: 'none',
                  msUserSelect: 'none',
                }}
              >
                {toolIcon('compass')}
                <span
                  style={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    marginRight: '8px',
                    direction: 'rtl',
                    textAlign: 'left',
                  }}
                >
                  联网搜索：{tool.query}
                </span>
                <div style={{ flexGrow: 1 }}></div>
              </div>
            </div>
          )
        );
      default:
        return null;
    }
  }

  if (message.ask === 'command' || message.say === 'command') {
    const splitMessage = (text: string) => {
      const outputIndex = text.indexOf(COMMAND_OUTPUT_STRING);
      if (outputIndex === -1) {
        return { command: text, output: '' };
      }
      return {
        command: text.slice(0, outputIndex).trim(),
        output: text
          .slice(outputIndex + COMMAND_OUTPUT_STRING.length)
          .trim()
          .split('')
          .map((char) => {
            switch (char) {
              case '\t':
                return '→   ';
              case '\b':
                return '⌫';
              case '\f':
                return '⏏';
              case '\v':
                return '⇳';
              default:
                return char;
            }
          })
          .join(''),
      };
    };

    const { command: rawCommand, output } = splitMessage(message.text || '');

    const requestsApproval = rawCommand.endsWith(COMMAND_REQ_APP_STRING);
    const command = requestsApproval ? rawCommand.slice(0, -COMMAND_REQ_APP_STRING.length) : rawCommand;

    // setLastOne((message.conversationHistoryIndex ?? 0) === lastModifiedMessage?.conversationHistoryIndex);
    return (
      <>
        {/* <div style={headerStyle}>
          {icon}
          {title}
        </div> */}
        {/* <Terminal
					rawOutput={command + (output ? "\n" + output : "")}
					shouldAllowInput={!!isCommandExecuting && output.length > 0}
				/> */}
        <div
          style={{
            borderRadius: 3,
            border: '1px solid var(--vscode-editorGroup-border)',
            overflow: 'hidden',
            backgroundColor: CODE_BLOCK_BG_COLOR,
            margin: '10px 0',
          }}
        >
          <div
            style={{
              display: 'flex',
            }}
          >
            {icon}
            <CodeBlock source={`${'```'}shell\n${command}\n${'```'}`} forceWrap={true} />
          </div>
          {output.length > 0 && (
            <div style={{ width: '100%' }}>
              <div
                onClick={onToggleExpand}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  width: '100%',
                  justifyContent: 'flex-start',
                  cursor: 'pointer',
                  padding: `2px 8px ${isExpanded ? 0 : 8}px 8px`,
                }}
              >
                <span className={`codicon codicon-chevron-${isExpanded ? 'down' : 'right'}`}></span>
                <span style={{ fontSize: '0.8em' }}>命令输出 </span>
              </div>
              {isExpanded && (
                <>
                  <CodeBlock source={`${'```'}shell\n${output}\n${'```'}`} />
                </>
              )}
            </div>
          )}
        </div>
        {requestsApproval && (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 10,
              padding: 8,
              fontSize: '12px',
              color: 'var(--vscode-editorWarning-foreground)',
            }}
          >
            <i className="codicon codicon-warning"></i>
            <span>模型已确定此命令需要明确批准。</span>
          </div>
        )}
        {/* 为命令消息添加工具条 */}
        {message.text && (
          <div style={{ position: 'relative' }}>
            <MessageToolbar
              text={message.text}
              shouldShow={shouldShowToolbar}
              isGenerating={isMessageGenerating}
              allMessages={allMessages}
              messageIndex={messageIndex}
              currentMessage={message}
              messageTs={message.ts}
            />
          </div>
        )}
      </>
    );
  }

  if (message.ask === 'use_mcp_server' || message.say === 'use_mcp_server') {
    const useMcpServer = JSON.parse(message.text || '{}') as JoyCoderAskUseMcpServer;
    const server = mcpServers.find((server) => server.name === useMcpServer.serverName);
    return (
      <>
        <div style={headerStyle}>
          {icon}
          {title}
        </div>

        <div
          style={{
            background: 'var(--vscode-textCodeBlock-background)',
            borderRadius: '3px',
            padding: '8px 10px',
            marginTop: '8px',
          }}
        >
          {useMcpServer.type === 'get_mcp_resource' && (
            <McpResourceRow
              item={{
                // 使用匹配的资源/模板详细信息，带有回退
                ...(findMatchingResourceOrTemplate(
                  useMcpServer.uri || '',
                  server?.resources,
                  server?.resourceTemplates,
                ) || {
                  name: '',
                  mimeType: '',
                  description: '',
                }),
                // 总是使用请求中的实际URI
                uri: useMcpServer.uri || '',
              }}
            />
          )}

          {useMcpServer.type === 'use_mcp_tools' && (
            <>
              <div onClick={(e) => e.stopPropagation()}>
                <McpToolRow
                  tool={{
                    name: useMcpServer.toolName || '',
                    description: server?.tools?.find((tool) => tool.name === useMcpServer.toolName)?.description || '',
                    autoApprove:
                      server?.tools?.find((tool) => tool.name === useMcpServer.toolName)?.autoApprove || false,
                  }}
                  serverName={useMcpServer.serverName}
                />
              </div>
              {useMcpServer.arguments && useMcpServer.arguments !== '{}' && (
                <div style={{ marginTop: '8px' }}>
                  <div
                    style={{
                      marginBottom: '4px',
                      opacity: 0.8,
                      fontSize: '12px',
                      textTransform: 'uppercase',
                    }}
                  >
                    参数
                  </div>
                  <CodeAccordianAdaptor
                    code={useMcpServer.arguments}
                    language="json"
                    isExpanded={true}
                    onToggleExpand={onToggleExpand}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </>
    );
  }

  switch (message.type) {
    case 'say':
      switch (message.say) {
        case 'api_req_started':
          return (
            <>
              <div
                style={{
                  ...headerStyle,
                  marginBottom: (cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage ? 10 : 0,
                  justifyContent: 'space-between',
                  // cursor: 'pointer',
                  userSelect: 'none',
                  WebkitUserSelect: 'none',
                  MozUserSelect: 'none',
                  msUserSelect: 'none',
                }}
                // onClick={onToggleExpand}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px',
                  }}
                >
                  {icon}
                  {title}
                  <VSCodeBadge
                    style={{
                      opacity: cost != null && cost > 0 ? 1 : 0,
                    }}
                  >
                    ${Number(cost || 0)?.toFixed(4)}
                  </VSCodeBadge>
                </div>
                {/* <span className={`codicon codicon-chevron-${isExpanded ? 'up' : 'down'}`}></span> */}
              </div>
              {((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage) && (
                <>
                  {(apiRequestFailedMessage || apiReqStreamingFailedMessage) && (
                    <p
                      style={{
                        ...pStyle,
                        color: 'var(--vscode-errorForeground)',
                      }}
                      dangerouslySetInnerHTML={{
                        __html: (apiRequestFailedMessage || apiReqStreamingFailedMessage) ?? '',
                      }}
                    ></p>
                  )}
                  {apiRequestFailedMessage?.toLowerCase().includes('powershell') && (
                    <p
                      style={{
                        ...pStyle,
                        color: 'var(--vscode-errorForeground)',
                      }}
                    >
                      {apiRequestFailedMessage?.toLowerCase().includes('powershell') && (
                        <>
                          <br />
                          <br />
                          看起来你遇到了 Windows PowerShell 的问题，请联系我们{' '}
                          <a
                            href="http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/contact"
                            style={{
                              color: 'inherit',
                              textDecoration: 'underline',
                            }}
                          >
                            故障排除指南
                          </a>
                          .
                        </>
                      )}
                    </p>
                  )}
                </>
              )}

              {isExpanded && (
                <div style={{ marginTop: '10px' }}>
                  <CodeAccordianAdaptor
                    code={JSON.parse(message.text || '{}').request}
                    language="markdown"
                    isExpanded={true}
                    onToggleExpand={onToggleExpand}
                  />
                </div>
              )}
            </>
          );
        case 'api_req_finished':
          return null; // 我们永远不应该看到这种消息类型
        case 'subtask_result':
          return (
            <div>
              <div
                style={{
                  marginTop: '0px',
                  backgroundColor: 'var(--vscode-badge-background)',
                  border: '1px solid var(--vscode-badge-background)',
                  borderRadius: '0 0 4px 4px',
                  overflow: 'hidden',
                  marginBottom: '8px',
                }}
              >
                <div
                  style={{
                    padding: '9px 10px 9px 14px',
                    backgroundColor: 'var(--vscode-badge-background)',
                    borderBottom: '1px solid var(--vscode-editorGroup-border)',
                    fontWeight: 'bold',
                    fontSize: 'var(--vscode-font-size)',
                    color: 'var(--vscode-badge-foreground)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                >
                  <span className="codicon codicon-arrow-left"></span>
                  子任务结果内容
                </div>
                <div
                  style={{
                    padding: '12px 16px',
                    backgroundColor: 'var(--vscode-editor-background)',
                  }}
                >
                  <MarkdownBlock markdown={message.text} />
                </div>
              </div>
            </div>
          );

        // markdown文本内容
        case 'text':
          return (
            <div onCopy={handleCopy} style={{ position: 'relative' }}>
              <Markdown markdown={messageText?.text} />
              {messageText?.text && <MessageToolbar
                text={messageText.text}
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />}
            </div>
          );
        // 深度思考
        case 'reasoning':
          return (
            <>
              {message.text && (
                <div
                  onClick={onToggleExpand}
                  style={{
                    // marginBottom: 15,
                    cursor: 'pointer',
                    color: 'var(--vscode-descriptionForeground)',

                    fontStyle: 'italic',
                    overflow: 'hidden',
                  }}
                >
                  {!isExpanded ? (
                    <div style={{ marginTop: -3 }}>
                      <span style={{ fontWeight: 'bold', display: 'block', marginBottom: '4px' }}>
                        思考中···
                        <span
                          className="codicon codicon-chevron-down"
                          style={{
                            display: 'inline-block',
                            transform: 'translateY(3px)',
                            marginLeft: '1.5px',
                          }}
                        />
                      </span>
                      {message.text}
                    </div>
                  ) : (
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span style={{ fontWeight: 'bold', marginRight: '4px' }}>深度思考:</span>
                      <span
                        style={{
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          direction: 'rtl',
                          textAlign: 'left',
                          flex: 1,
                        }}
                      >
                        {message.text + '\u200E'}
                      </span>
                      <span
                        className="codicon codicon-chevron-right"
                        style={{
                          marginLeft: '4px',
                          flexShrink: 0,
                        }}
                      />
                    </div>
                  )}
                </div>
              )}
            </>
          );
        case 'user_feedback':
          return (
            <div style={{ position: 'relative' }}>
              <CollapsibleText
                text={message.text || ''}
                maxLines={15}
                align="right"
              />
              {message.images && message.images.length > 0 && (
                <Thumbnails images={message.images} style={{ marginTop: '8px' }} />
              )}
              {message.text && <MessageToolbar
                text={message.text}
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />}
            </div>
          );
        case 'user_feedback_diff': {
          const tool = JSON.parse(message.text || '{}') as JoyCoderSayTool;
          return (
            <div
              style={{
                marginTop: -10,
                width: '100%',
                position: 'relative',
              }}
            >
              <CodeAccordianAdaptor
                diff={tool.diff!}
                code={tool.diff!}
                isFeedback={true}
                isExpanded={isExpanded}
                onToggleExpand={onToggleExpand}
              />
              {tool.diff && <MessageToolbar
                text={tool.diff}
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />}
            </div>
          );
        }
        case 'error':
          return (
            <>
              {title && (
                <div style={headerStyle}>
                  {icon}
                  {title}
                </div>
              )}
              <p
                style={{
                  ...pStyle,
                  color: 'var(--vscode-foreground)',
                }}
                dangerouslySetInnerHTML={{
                  __html: message.text ?? '',
                }}
              ></p>
            </>
          );
        case 'diff_error':
          return (
            <>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'rgba(255, 191, 0, 0)',
                  padding: 8,
                  borderRadius: 3,
                  fontSize: 12,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: 4,
                  }}
                >
                  <i
                    className="codicon codicon-info"
                    style={{
                      marginRight: 8,
                      fontSize: 18,
                      // color: '#FFA500',
                    }}
                  ></i>
                  <span
                    style={{
                      fontWeight: 500,
                      // color: '#FFA500',
                    }}
                  >
                    差异修改异常，正在重试中...
                  </span>
                </div>
                <div>模型使用的搜索模式与文件中的任何内容都不匹配，重试中...</div>
              </div>
            </>
          );
        case 'joycoderignore_error':
          return (
            <>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'rgba(255, 191, 0, 0.1)',
                  padding: 8,
                  borderRadius: 3,
                  fontSize: 12,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: 4,
                  }}
                >
                  <i
                    className="codicon codicon-warning"
                    style={{
                      marginRight: 8,
                      fontSize: 18,
                      color: '#FFA500',
                    }}
                  ></i>
                  <span
                    style={{
                      fontWeight: 500,
                      color: '#FFA500',
                    }}
                  >
                    拒绝访问
                  </span>
                </div>
                <div>
                  JoyCode 尝试访问 <code>{message.text}</code>，但它被 <code>.joycoderignore</code> 文件所阻止。
                </div>
              </div>
            </>
          );
        case 'checkpoint_created':
          // 只有在shouldShowToolbar为true时才渲染MessageToolbar
          if (shouldShowToolbar) {
            return (
              <>
                <MessageToolbar
                  text=""
                  shouldShow={shouldShowToolbar}
                  isGenerating={isMessageGenerating}
                  allMessages={allMessages}
                  messageIndex={messageIndex}
                  currentMessage={message}
                  messageTs={message.ts}
                />
              </>
            );
          }
          return null;
        case 'get_mcp_instructions':
          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                color: 'var(--vscode-foreground)',
                opacity: 0.7,
                fontSize: 12,
                padding: '4px 0',
              }}
            >
              <i className="codicon codicon-book" style={{ marginRight: 6 }} />
              正在加载 MCP 内容...
            </div>
          );
        case 'completion_result':
          return (
            <>
              <div
                style={{
                  ...headerStyle,
                  marginBottom: '10px',
                }}
              >
                {icon}
                {title}
              </div>
              <div
                style={{
                  color: 'var(--vscode-foreground)',
                  // color: 'var(--vscode-charts-green)',
                  paddingTop: 10,
                  position: 'relative',
                }}
                onCopy={handleCopy}
              >
                <Markdown markdown={messageText?.text} />
                {messageText?.text && <MessageToolbar
                  text={messageText.text}
                  shouldShow={shouldShowToolbar}
                  isGenerating={isMessageGenerating}
                  allMessages={allMessages}
                  messageIndex={messageIndex}
                  currentMessage={message}
                  messageTs={message.ts}
                />}
              </div>
              {message.partial !== true && hasChanges && (
                <div style={{ paddingTop: 17 }}>
                  <SuccessButton
                    disabled={seeNewChangesDisabled}
                    onClick={() => {
                      setSeeNewChangesDisabled(true);
                      vscode.postMessage({
                        type: 'taskCompletionViewChanges',
                        number: message.ts,
                      });
                    }}
                    style={{
                      width: '100%',
                      cursor: seeNewChangesDisabled ? 'wait' : 'pointer',
                      background: 'linear-gradient(320deg, rgb(76, 213, 255) 0%, rgb(54, 87, 255) 50%)',
                    }}
                  >
                    <i className="codicon codicon-new-file" style={{ marginRight: 6 }} />
                    查看最新变更
                  </SuccessButton>
                </div>
              )}
            </>
          );
        case 'shell_integration_warning':
          return (
            <>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'rgba(255, 191, 0, 0)',
                  padding: 8,
                  borderRadius: 3,
                  fontSize: 12,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: 4,
                  }}
                >
                  <i
                    className="codicon codicon-warning"
                    style={{
                      marginRight: 8,
                      fontSize: 18,
                      color: '#FFA500',
                    }}
                  ></i>
                  <span
                    style={{
                      fontWeight: 500,
                      // color: '#FFA500',
                    }}
                  >
                    Shell 集成不可用
                  </span>
                </div>
                <div>
                  JoyCode 将无法查看命令的输出。请更新 VSCode（
                  <code>CMD/CTRL + Shift + P</code> → "更新"）并确保您使用的是受支持的 shell：zsh、bash、 fish 或
                  PowerShell（<code>CMD/CTRL + Shift + P</code> → "终端：选择默认配置文件"）。{' '}
                  <a
                    href="http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/prompts/shell#%E9%92%88%E5%AF%B9-windows-%E7%94%A8%E6%88%B7%E7%9A%84%E5%85%B6%E4%BB%96%E6%95%85%E9%9A%9C%E6%8E%92%E9%99%A4"
                    style={{
                      color: 'inherit',
                      textDecoration: 'underline',
                    }}
                  >
                    仍然遇到问题？
                  </a>
                </div>
              </div>
            </>
          );
        case 'mcp_server_response':
          return (
            <>
              <div style={{ paddingTop: 0 }}>
                <div
                  style={{
                    marginBottom: '4px',
                    opacity: 0.8,
                    fontSize: '12px',
                    textTransform: 'uppercase',
                  }}
                >
                  响应
                </div>
                <CodeAccordianAdaptor
                  code={message.text}
                  language="json"
                  isExpanded={true}
                  onToggleExpand={onToggleExpand}
                />
              </div>
            </>
          );
        // 调用Codebase搜索，但无法生效的提示
        case 'codebase_search_not_support': {
          return (
            <div>
              无法通过Codebase进行搜索，原因：
              {message.text === 'UNINDEXED' ? (
                <div>
                  未开启Codebase索引，
                  <button
                    className="joycoder-link-button" // 添加CSS使其看起来像链接
                    onClick={(e) => {
                      vscode.postMessage({
                        type: 'openSettings',
                      });
                    }}
                  >
                    去开启
                  </button>
                </div>
              ) : message.text === 'INDEXING' ? (
                <div>
                  索引进行中，
                  <button
                    className="joycoder-link-button" // 添加CSS使其看起来像链接
                    onClick={(e) => {
                      vscode.postMessage({
                        type: 'openSettings',
                      });
                    }}
                  >
                    查看进展
                  </button>
                </div>
              ) : (
                '未知原因' + message.text
              )}
            </div>
          );
        }
        case 'deleted_api_reqs':
          // deleted_api_reqs消息不显示任何内容
          return null;
        case 'api_req_retried':
          // api_req_retried消息不显示任何内容
          return null;
        default:
          return (
            <>
              {title && (
                <div style={headerStyle}>
                  {icon}
                  {title}
                </div>
              )}
              <div style={{ paddingTop: 10, position: 'relative' }} onCopy={handleCopy}>
                <Markdown markdown={message.text} />
                {message.text && <MessageToolbar
                  text={message.text}
                  shouldShow={shouldShowToolbar}
                  isGenerating={isMessageGenerating}
                  allMessages={allMessages}
                  messageIndex={messageIndex}
                  currentMessage={message}
                  messageTs={message.ts}
                />}
              </div>
            </>
          );
      }
    case 'ask':
      switch (message.ask) {
        case 'mistake_limit_reached':
          return (
            <>
              <div style={headerStyle}>
                {icon}
                {title}
              </div>
              <p
                style={{
                  ...pStyle,
                  color: 'var(--vscode-warnForeground)',
                }}
              >
                {message.text}
              </p>
            </>
          );
        //@ts-ignore
        case 'command':
          return (
            <>
              <div style={headerStyle}>
                {icon}
                {title}
              </div>
              <CommandExecution executionId={message.ts.toString()} text={message.text} />
              {/* 为命令消息添加工具条 */}
              {message.text && (
                <div style={{ position: 'relative' }}>
                  <MessageToolbar
                    text={message.text}
                    shouldShow={shouldShowToolbar}
                    isGenerating={isMessageGenerating}
                    allMessages={allMessages}
                    messageIndex={messageIndex}
                    currentMessage={message}
                    messageTs={message.ts}
                  />
                </div>
              )}
            </>
          );
        case 'auto_approval_max_req_reached':
          return (
            <>
              <div style={headerStyle}>
                {icon}
                {title}
              </div>
              <p
                style={{
                  ...pStyle,
                  color: 'var(--vscode-foreground)',
                }}
              >
                {message.text}
              </p>
            </>
          );
        case 'new_task_with_condense_context':
          return (
            <>
              <div style={headerStyle}>
                <span
                  className="codicon codicon-new-file"
                  style={{
                    color: normalColor,
                    marginBottom: '-1.5px',
                  }}
                ></span>
                <span style={{ color: normalColor, fontWeight: 'bold' }}>JoyCode 想要创建带当前上下文的新任务：</span>
              </div>
              <NewTaskPreview context={message.text || ''} />
            </>
          );
        case 'completion_result':
          if (message.text) {
            // 修复：这个是否真的被使用过？
            const hasChanges = message.text.endsWith(COMPLETION_RESULT_CHANGES_FLAG) ?? false;
            const text = hasChanges ? message.text.slice(0, -COMPLETION_RESULT_CHANGES_FLAG.length) : message.text;
            return (
              <div>
                <div
                  style={{
                    ...headerStyle,
                    marginBottom: '10px',
                  }}
                >
                  {icon}
                  {title}
                </div>
                <div
                  style={{
                    color: 'var(--vscode-foreground)',
                    // color: 'var(--vscode-charts-green)',
                    paddingTop: 10,
                    position: 'relative',
                  }}
                >
                  <Markdown markdown={text} />
                  {text && <MessageToolbar
                    text={text}
                    shouldShow={shouldShowToolbar}
                    isGenerating={isMessageGenerating}
                    allMessages={allMessages}
                    messageIndex={messageIndex}
                    currentMessage={message}
                    messageTs={message.ts}
                  />}
                  {message.partial !== true && hasChanges && (
                    <div style={{ marginTop: 15 }}>
                      <SuccessButton
                        appearance="secondary"
                        disabled={seeNewChangesDisabled}
                        onClick={() => {
                          setSeeNewChangesDisabled(true);
                          vscode.postMessage({
                            type: 'taskCompletionViewChanges',
                            number: message.ts,
                          });
                        }}
                      >
                        <i
                          className="codicon codicon-new-file"
                          style={{
                            marginRight: 6,
                            cursor: seeNewChangesDisabled ? 'wait' : 'pointer',
                            background: 'linear-gradient(320deg, rgb(76, 213, 255) 0%, rgb(54, 87, 255) 50%)',
                          }}
                        />
                        查看最新变更
                      </SuccessButton>
                    </div>
                  )}
                </div>
              </div>
            );
          } else {
            return null; // 当我们得到没有文本的completion_result询问时，不渲染任何内容
          }
        case 'followup': {
          let question: string | undefined;
          let options: string[] | undefined;
          let selected: string | undefined;
          try {
            const parsedMessage = JSON.parse(message.text || '{}') as JoyCoderAskQuestion;
            question = parsedMessage.question;
            options = parsedMessage.options;
            selected = parsedMessage.selected;
          } catch (e) {
            // 旧版消息会直接传递问题
            question = message.text;
          }

          return (
            <>
              {title && (
                <div style={headerStyle}>
                  {icon}
                  {title}
                </div>
              )}
              <div style={{ paddingTop: 10, position: 'relative' }}>
                <Markdown markdown={question} />
                {question && <MessageToolbar
                  text={question}
                  shouldShow={shouldShowToolbar}
                  isGenerating={isMessageGenerating}
                  allMessages={allMessages}
                  messageIndex={messageIndex}
                  currentMessage={message}
                  messageTs={message.ts}
                />}
                <OptionsButtons
                  options={options}
                  selected={selected}
                  isActive={isLast && lastModifiedMessage?.ask === 'followup'}
                />
              </div>
              {followUpData && followUpData.suggest?.length > 0 && (
                <>
                  <div style={{ paddingTop: 10, paddingBottom: 15 }}>
                    <Markdown markdown={message.partial === true ? message?.text : followUpData?.question} />
                  </div>
                  <FollowUpSuggest suggestions={followUpData?.suggest} ts={message?.ts} />
                </>
              )}
            </>
          );
        }
        case 'condense':
          return (
            <>
              <div style={headerStyle}>
                <span
                  className="codicon codicon-new-file"
                  style={{
                    color: normalColor,
                    marginBottom: '-1.5px',
                  }}
                ></span>
                <span style={{ color: normalColor, fontWeight: 'bold' }}>JoyCoder将要简化并提取当前上下文：</span>
              </div>
              <NewTaskPreview context={message.text || ''} />
            </>
          );
        case 'get_plan_info':
          let response: string | undefined;
          let options: string[] | undefined;
          let selected: string | undefined;
          try {
            const parsedMessage = JSON.parse(message.text || '{}') as JoyCoderPlanModeResponse;
            response = parsedMessage.response;
            options = parsedMessage.options;
            selected = parsedMessage.selected;
          } catch (e) {
            // 旧版消息会直接传递响应
            response = message.text;
          }
          return (
            <>
              <div style={{ position: 'relative' }}>
                <Markdown markdown={response} />
                {response && <MessageToolbar
                  text={response}
                  shouldShow={shouldShowToolbar}
                  isGenerating={isMessageGenerating}
                  allMessages={allMessages}
                  messageIndex={messageIndex}
                  currentMessage={message}
                  messageTs={message.ts}
                />}
                <OptionsButtons
                  options={options}
                  selected={selected}
                  isActive={isLast && lastModifiedMessage?.ask === 'get_plan_info'}
                />
              </div>
            </>
          );
        // return (
        //   <div style={{}}>
        //     <Markdown markdown={message.text} />
        //   </div>
        // );
        default:
          return null;
      }
  }
};

// 已替换为Thinking图片
// export const ProgressIndicator = () => (
//   <div
//     style={{
//       width: '16px',
//       height: '16px',
//       display: 'flex',
//       alignItems: 'center',
//       justifyContent: 'center',
//     }}
//   >
//     <div style={{ transform: 'scale(0.55)', transformOrigin: 'center' }}>
//       <VSCodeProgressRing />
//     </div>
//   </div>
// );

const Markdown = memo(({ markdown }: { markdown?: string }) => {
  return (
    <div
      style={{
        wordBreak: 'break-word',
        overflowWrap: 'anywhere',
        marginBottom: -3,
        marginTop: -10,
      }}
    >
      <MarkdownBlock markdown={markdown} />
    </div>
  );
});

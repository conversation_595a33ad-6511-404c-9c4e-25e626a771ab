import React, { useState, useEffect, useCallback } from 'react';
import { Input, Button, message, Tag } from 'antd';
import { CustomInstructionsProps } from './types';
import { getCustomInstructions, getRoleDefinition, getWhenToUse, ModeConfig, PromptComponent } from '../../utils/modes';
import { vscode } from '../../utils/vscode';

const { TextArea } = Input;

const CustomInstructions: React.FC<CustomInstructionsProps> = ({
  visualMode,
  customModes,
  customModePrompts,
  updateAgentPrompt,
  updateCustomMode,
  handleAgentReset,
  getCurrentMode,
  isToolsEditMode,
}) => {
  const [localRoleDefinition, setLocalRoleDefinition] = useState('');
  const [localCustomInstructions, setLocalCustomInstructions] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setLocalRoleDefinition(agentDefinition);
    setLocalCustomInstructions(customInstructions);
    setHasChanges(false);
  }, [visualMode, customModes, customModePrompts]);
  const currentMode = getCurrentMode();
  const isCustomMode = customModes?.some((m) => m.agentId === currentMode?.agentId);

  const agentDefinition = (() => {
    const customMode = customModes?.find((m) => m.agentId === visualMode);
    const prompt = customModePrompts?.[visualMode];
    return customMode?.agentDefinition ?? prompt?.agentDefinition ?? getRoleDefinition(visualMode);
  })();

  const customInstructions = (() => {
    const customMode = customModes?.find((m) => m.agentId === visualMode);
    const prompt = customModePrompts?.[visualMode];
    return (
      customMode?.customInstructions ?? prompt?.customInstructions ?? getCustomInstructions(visualMode, customModes)
    );
  })();

  const handleRoleDefinitionChange = (value: string) => {
    setLocalRoleDefinition(value);
    setHasChanges(true);
  };

  const handleCustomInstructionsChange = (value: string) => {
    setLocalCustomInstructions(value);
    setHasChanges(true);
  };
  // Helper function to find a mode by agentId
  const findModeBySlug = useCallback(
    (searchSlug: string, modes: readonly ModeConfig[] | undefined): ModeConfig | undefined => {
      if (!modes) return undefined;
      const isModeWithSlug = (mode: ModeConfig): mode is ModeConfig => mode.agentId === searchSlug;
      return modes.find(isModeWithSlug);
    },
    [],
  );

  const handleSave = () => {
    if (isCustomMode && currentMode) {
      updateCustomMode(visualMode, {
        ...currentMode,
        agentDefinition: localRoleDefinition.trim() || '',
        customInstructions: localCustomInstructions.trim() || undefined,
        source: currentMode.source || 'global',
      });
    } else {
      updateAgentPrompt(visualMode, {
        agentDefinition: localRoleDefinition.trim() || undefined,
        customInstructions: localCustomInstructions.trim(),
      });
    }
    setHasChanges(false);
    message.success('Changes saved successfully');
  };

  return (
    <div className="custom-instructions">
      {/* When to Use section */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-1">
          <div className="font-bold">使用场景（可选）</div>
          {!findModeBySlug(visualMode, customModes) && (
            <Button
              icon={<span className="codicon codicon-discard" />}
              onClick={() => {
                const currentMode = getCurrentMode();
                if (currentMode?.agentId) {
                  handleAgentReset(currentMode.agentId, 'whenToUse');
                }
              }}
              title='重置\"使用场景\"描述为默认值'
              data-testid="when-to-use-reset"
            ></Button>
          )}
        </div>
        <div className="text-sm text-vscode-descriptionForeground mb-2">
          描述何时应该使用此模式。这有助于 Orchestrator 为任务选择合适的模式。
        </div>
        <TextArea
          value={(() => {
            const customMode = findModeBySlug(visualMode, customModes);
            const prompt = customModePrompts?.[visualMode] as PromptComponent;
            return customMode?.whenToUse ?? prompt?.whenToUse ?? getWhenToUse(visualMode);
          })()}
          onChange={(e) => {
            const value =
              (e as unknown as CustomEvent)?.detail?.target?.value || ((e as any).target as HTMLTextAreaElement).value;
            const customMode = findModeBySlug(visualMode, customModes);
            if (customMode) {
              // For custom modes, update the JSON file
              updateCustomMode(visualMode, {
                ...customMode,
                whenToUse: value.trim() || undefined,
                source: customMode.source || 'global',
              });
            } else {
              // For built-in modes, update the prompts
              updateAgentPrompt(visualMode, {
                whenToUse: value.trim() || undefined,
              });
            }
          }}
          className="resize-y w-full"
          rows={4}
          data-testid={`${getCurrentMode()?.agentId || 'code'}-when-to-use-textarea`}
        />
      </div>
      <div className="mb-4">
        <div className="flex justify-between items-center mb-1">
          <div className="font-bold">角色定义</div>
          {!isCustomMode && (
            <Button
              icon={<span className="codicon codicon-discard" />}
              onClick={() => handleAgentReset(visualMode, 'agentDefinition')}
              title="重置为默认值"
            />
          )}
        </div>
        {isToolsEditMode && (
          <>
            <div className="text-sm text-vscode-descriptionForeground mb-2">定义此模式的角色和行为</div>
            <TextArea
              value={localRoleDefinition}
              onChange={(e) => handleRoleDefinitionChange(e.target.value)}
              className="resize-y w-full"
              rows={4}
              readOnly={!isToolsEditMode}
            />
          </>
        )}
        <div className="text-sm text-vscode-descriptionForeground mb-2">
          支持从
          <Tag
            style={{ lineHeight: '14px', margin: 0, padding: '0 5px', cursor: 'pointer', textDecoration: 'underline' }}
            onClick={() => {
              const currentMode = getCurrentMode();
              if (!currentMode) return;

              vscode.postMessage({
                type: 'openFile',
                text: `./.joycode/modes/rules-${currentMode.agentId}/agentDefinition.md`,
                values: {
                  create: true,
                  content: '',
                },
              });
            }}
          >
            .joycode/modes
          </Tag>
          目录读取配置，且覆盖内置内容
        </div>
      </div>

      <div className="mb-2">
        <div className="flex justify-between items-center mb-1">
          <div className="font-bold">专属规则（可选）</div>
          {!isCustomMode && (
            <Button
              icon={<span className="codicon codicon-discard" />}
              onClick={() => handleAgentReset(visualMode, 'customInstructions')}
              title="重置为默认值"
            />
          )}
        </div>
        {isToolsEditMode && (
          <>
            <div className="text-[13px] text-vscode-descriptionForeground mb-2">
              为<Tag style={{ lineHeight: '14px', margin: 0 }}>{currentMode?.name || '编码'}</Tag>
              模式提供额外的自定义指令
            </div>
            <TextArea
              value={localCustomInstructions}
              onChange={(e) => handleCustomInstructionsChange(e.target.value)}
              rows={4}
              className="w-full resize-y"
              readOnly={!isToolsEditMode}
            />
          </>
        )}
        <div className="text-sm text-vscode-descriptionForeground mb-2">
          支持从
          <Tag
            style={{ lineHeight: '14px', margin: 0, padding: '0 5px', cursor: 'pointer', textDecoration: 'underline' }}
            onClick={() => {
              const currentMode = getCurrentMode();
              if (!currentMode) return;

              vscode.postMessage({
                type: 'openFile',
                text: `./.joycode/modes/rules-${currentMode.agentId}/customInstructions.md`,
                values: {
                  create: true,
                  content: '',
                },
              });
            }}
          >
            .joycode/modes
          </Tag>
          目录读取配置，且覆盖内置内容
        </div>
      </div>
      {hasChanges && (
        <div className="mt-4">
          <Button type="primary" onClick={handleSave}>
            保存更改
          </Button>
        </div>
      )}
    </div>
  );
};

export default CustomInstructions;

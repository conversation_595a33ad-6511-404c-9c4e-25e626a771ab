@layer theme, base, components, utilities;
@import "tailwindcss/theme.css" layer(theme);
/* Disable Tailwind's CSS reset to preserve existing styles */
/* @import "tailwindcss/preflight.css" layer(base); */
@import "tailwindcss/utilities.css" layer(utilities);

textarea:focus {
	outline: 1.5px solid var(--vscode-focusBorder, #007fd4);
}

vscode-button::part(control):focus {
	outline: none;
}

/*
Use vscode native scrollbar styles
https://github.com/gitkraken/vscode-gitlens/blob/b1d71d4844523e8b2ef16f9e007068e91f46fd88/src/webviews/apps/home/<USER>
*/

html {
	height: 100%;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

body {
	margin: 0;
	line-height: 1.25;
}

body.scrollable,
.scrollable,
body.code-block-scrollable,
.code-block-scrollable {
	border-color: transparent;
	transition: border-color 0.7s linear;
}

body:hover.scrollable,
body:hover .scrollable,
body:focus-within.scrollable,
body:focus-within .scrollable,
body:hover.code-block-scrollable,
body:hover .code-block-scrollable,
body:focus-within.code-block-scrollable,
body:focus-within .code-block-scrollable {
	border-color: var(--vscode-scrollbarSlider-background);
	transition: none;
}

.scrollable::-webkit-scrollbar-corner {
	background-color: transparent !important;
}

.scrollable::-webkit-scrollbar-thumb {
	background-color: transparent;
	border-color: inherit;
	border-right-style: inset;
	border-right-width: calc(100vw + 100vh);
	border-radius: unset !important;
}

.scrollable::-webkit-scrollbar-thumb:hover {
	border-color: var(--vscode-scrollbarSlider-hoverBackground);
}

.scrollable::-webkit-scrollbar-thumb:active {
	border-color: var(--vscode-scrollbarSlider-activeBackground);
}

/*
Fix VSCode ignoring webkit scrollbar modifiers
https://github.com/microsoft/vscode/issues/213045
*/
@supports selector(::-webkit-scrollbar) {
	html {
		scrollbar-color: unset;
	}
}

/*
The above scrollbar styling uses some transparent background color magic to accomplish its animation. However this doesn't play nicely with SyntaxHighlighter, so we need to set a background color for the code blocks' horizontal scrollbar. This actually has the unintended consequence of always showing the scrollbar which I prefer since it makes it more obvious that there is more content to scroll to.
*/

.code-block-scrollable::-webkit-scrollbar-track {
	background: transparent;
}

.code-block-scrollable::-webkit-scrollbar-thumb {
	background-color: var(--vscode-scrollbarSlider-background);
	border-radius: 5px;
	border: 2px solid transparent;
	background-clip: content-box;
}

.code-block-scrollable::-webkit-scrollbar-thumb:hover {
	background-color: var(--vscode-scrollbarSlider-hoverBackground);
}

.code-block-scrollable::-webkit-scrollbar-thumb:active {
	background-color: var(--vscode-scrollbarSlider-activeBackground);
}

.code-block-scrollable::-webkit-scrollbar-corner {
	background-color: transparent;
}

/*
Dropdown label
https://github.com/microsoft/vscode-webview-ui-toolkit/tree/main/src/dropdown#with-label
*/
.dropdown-container {
	box-sizing: border-box;
	display: flex;
	flex-flow: column nowrap;
	align-items: flex-start;
	justify-content: flex-start;
}
.dropdown-container label {
	display: block;
	color: var(--vscode-foreground);
	cursor: pointer;
	font-size: var(--vscode-font-size);
	line-height: normal;
	margin-bottom: 2px;
}

/* Fix scrollbar in dropdown */

vscode-dropdown::part(listbox) {
	border-color: var(--vscode-scrollbarSlider-background);
	transition: none;
	scrollbar-color: var(--vscode-scrollbarSlider-background) transparent;
}

/* Faded icon buttons in textfields */

.input-icon-button {
	cursor: pointer;
	opacity: 0.65;
}
.input-icon-button:hover {
	opacity: 1;
}
.input-icon-button.disabled {
	cursor: not-allowed;
	opacity: 0.4;
}
.input-icon-button.disabled:hover {
	opacity: 0.4;
}

/* Context mentions */

.mention-context-textarea-highlight {
	background-color: color-mix(in srgb, var(--vscode-badge-foreground) 30%, transparent);
	border-radius: 3px;
	box-shadow: 0 0 0 0.5px color-mix(in srgb, var(--vscode-badge-foreground) 30%, transparent);
	color: transparent;
  padding: 0;
}

.mention-context-highlight {
	background-color: color-mix(in srgb, var(--vscode-badge-foreground) 30%, transparent);
	border-radius: 3px;
}

.mention-context-highlight-with-shadow {
	background-color: color-mix(in srgb, var(--vscode-badge-foreground) 30%, transparent);
	border-radius: 3px;
	box-shadow: 0 0 0 0.5px color-mix(in srgb, var(--vscode-badge-foreground) 30%, transparent);
}
.mention-context-textarea-highlight-layer  textarea{
  outline: 1px solid transparent;
  border-radius: 6px;
  max-height: 340px;
}
.mention-context-textarea-highlight-layer textarea:focus{
  outline: 1px solid transparent !important;
}
.vscode-light .joycoder-chart-title{
  color: rgba(0, 0, 0, 0.9);
}
.vscode-light .joycoder-chart-subtitle{
  color: rgba(0, 0, 0, 0.7);
}
.vscode-light .joycoder-chart-item-title{
  color: rgba(0, 0, 0, 0.8);
}
.vscode-light .joycoder-chart-item{
  background: rgba(0,0,0,0.04);
  border: 1px solid rgba(0,0,0,0.04);
  color: rgba(84, 84, 84, 0.8);
}
.vscode-light .input-icon-button.codicon-device-camera{
  background-color: rgba(34,34,34,0.6) !important;
  mask: url(https://storage.jd.com/dist-dev/joycoder/chatDialog/upload-img.svg) no-repeat center center !important;
}
.vscode-light .mention-context-textarea-highlight-layer-bg{
  background-color: rgba(255,255,255,1) !important;
  border-color: rgba(67, 67, 67,.4) rgba(67, 67, 67,.4) transparent !important;
}
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_content_tip,
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_content_header{
  color: rgba(21, 21, 21, 1) !important;
}
.vscode-light .mention-context-textarea-highlight-layer-line{
  border: 1px solid #cacaca !important;
}
.vscode-light .joycoder-chatgpt-model-dropdown .model-icon+span{
  color: rgba(21, 21, 21, 1) !important;
}
.vscode-light .joycoder-chatgpt-model-dropdown{
  background: rgba(0, 0, 0, 0.05) !important;
}
.vscode-light .joycoder-coder-mode-dropdown .mode-icon,.vscode-light .joycoder-coder-mode-dropdown span{
  color: rgba(21, 21, 21, 1) !important;
}
.vscode-light .joycoder-chatgpt-input-icons .codicon-send.codicon[class*=codicon-] {
  height: 20px;
}
.vscode-light .input-icon-button.codicon-send {
  background: rgba(0, 0, 0, 0) !important;
}
.joycoder-chart-logo{
  width: 60px;
  height: 60px;
  margin: 60px auto 0;
  background: url(https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/logo.svg) no-repeat;
  background-size: 60px 60px;
}
.vscode-light .joycoder-chart-logo{
  width: 60px;
  height: 60px;
  margin: 60px auto 0;
  background: url(https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/logo-light.svg) no-repeat;
  background-size: 60px 60px;
}
.joycoder-chart-title{
  font-size: 20px;
  font-weight: 500;
  color: rgba(255,255,255,1);
  margin: 4px 0;
}
.joycoder-chart-subtitle{
  font-size: 12px;
  font-weight: normal;
  color: rgba(255,255,255,0.7);
  text-align: center;
  line-height: 17px;
  height: 17px;
}
.joycoder-chart-item-title{
  margin: 70px 40px 10px;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  font-weight: normal;
  color: rgba(255,255,255,0.8);
  text-align: left;
}
.joycoder-chart-item{
  height: 60px;
  line-height: 60px;
  background: rgba(255,255,255,0.04);
  border: 1px solid rgba(255,255,255,0.04);
  border-radius: 6px;
  /* background: url(https://storage.jd.com/dist-dev/joycoder/chatDialog/list-icon.svg) no-repeat 24px center; */
  font-size: 16px;
  font-weight: 500;
  color: rgba(255,255,255,0.8);
  padding: 0 12px 0 30px;
  overflow: hidden;
  margin: 0 40px 20px;
  cursor: pointer;
}
.joycoder-chart-item::before{
  content: "";
  padding-left: 30px;
  background: url(https://storage.jd.com/dist-dev/joycoder/chatDialog/list-icon.svg) no-repeat center;
}
.ui-restore-box.codicon-bookmark:before {
  content: "→";
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinning-loading {
  animation: spin 2s linear infinite;
}
.joycoder-link-button {
  background: none;
  border: none;
  padding: 0;
  color: var(--vscode-textLink-foreground);
  text-decoration: underline;
  cursor: pointer;
  font: inherit;
  outline: none;
}

.joycoder-link-button:hover {
  color: var(--vscode-textLink-activeForeground);
  text-decoration: underline;
}

.joycoder-link-button:focus {
  outline: 1px solid var(--vscode-focusBorder);
  outline-offset: 2px;
}

.joycoder-link-button:active {
  color: var(--vscode-textLink-activeForeground);
}

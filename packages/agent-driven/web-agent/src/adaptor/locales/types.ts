export interface Locale {
  common: {
    approve: string;
    reject: string;
    save: string;
    cancel: string;
    retry: string;
    proceed: string;
    startNewTask: string;
    runCommand: string;
    resumeTask: string;
    proceedWhileRunning: string;
  };
  welcome: {
    title: string;
    subtitle: string;
    examples: {
      title: string;
      snakeGame: string;
      vueProject: string;
      fixErrors: string;
    };
    hotkey: {
      text: string;
    };
  };
  header: {
    newChat: string;
    history: string;
    tools: string;
  };
  chat: {
    thinking: string;
    placeholder: {
      withTask: string;
      withoutTask: string;
      withTaskPlan: string;
      withoutTaskPlan: string;
      chat: string;
      act: string;
      architect: string;
      orchestrator: string;
      debug: string;
      ask: string;
      default: string;
    };
  };
  buttons: {
    primary: {
      editFile: string;
      default: string;
      approve: string;
      command: string;
      retry: string;
      proceed: string;
      startNew: string;
      resume: string;
    };
    secondary: {
      editFile: string;
      default: string;
      reject: string;
      startNew: string;
    };
  };
}

export interface ChatModelConfig {
  label: string;
  description: string;
  avatar: string;
  chatApiModel: string;
  chatApiUrl?: string;
  maxTotalTokens: number;
  bizId?: string;
  bizToken?: string;
  systemMessage?: string;
  respMaxTokens?: number;
  // 标记类
  hidden?: boolean;
  prefer?: boolean;
  context?: boolean;
  features?: string[];
  temperature?: number;
  stream?: boolean;
}
export const MAX_IMAGES_PER_MESSAGE = 20;
export const DEFAULT_ASSISTANT_AVATAR =
  'https://img30.360buyimg.com/img/jfs/t1/222950/2/30082/4115/6521193cFc19f1365/b0070eb19e1f2566.png';

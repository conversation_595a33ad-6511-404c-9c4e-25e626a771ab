import { JOYCODE_BRAND_NAME } from '@joycoder/shared/src/constants/uiConstants';

export const zhCN = {
  common: {
    approve: '同意',
    reject: '拒绝',
    save: '保存',
    cancel: '取消',
    retry: '重试',
    proceed: '继续执行',
    startNewTask: '开始新的任务',
    runCommand: '运行命令',
    resumeTask: '恢复任务',
    proceedWhileRunning: '保持运行并继续',
  },
  welcome: {
    title: `欢迎使用${JOYCODE_BRAND_NAME}`,
    subtitle: '感知 决策 行动',
    examples: {
      title: '使用示例',
      snakeGame: '生成一个贪吃蛇游戏',
      vueProject: '创建一个前端项目（VUE2+TS）',
      fixErrors: '修复当前编辑器中的代码错误问题',
    },
    hotkey: {
      text: '使用快捷键创建新任务',
    },
  },
  header: {
    newChat: '新的聊天',
    history: '历史会话',
    tools: '工具市场',
  },
  chat: {
    thinking: `${JOYCODE_BRAND_NAME} 思考中`,
    placeholder: {
      // withTask: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\n编码模式下，${JOYCODE_BRAND_NAME}会自主撰写代码`,
      // withoutTask: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\n编码模式下，${JOYCODE_BRAND_NAME}会自主撰写代码`,
      // withTaskPlan: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\n规划模式下，${JOYCODE_BRAND_NAME}不会自主撰写代码，而是聚焦方案讨论`,
      // withoutTaskPlan: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\n规划模式下，${JOYCODE_BRAND_NAME}不会自主撰写代码，而是聚焦方案讨论`,
      chat: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\nCommand(Ctrl)/Shift+Enter可换行\n${JOYCODE_BRAND_NAME}问答智能体将专注于回答问题`,
      architect: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\nCommand(Ctrl)/Shift+Enter可换行\n${JOYCODE_BRAND_NAME}规划智能体${JOYCODE_BRAND_NAME}不会自主撰写代码，而是聚焦方案讨论`,
      act: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\nCommand(Ctrl)/Shift+Enter可换行\n${JOYCODE_BRAND_NAME}编码智能体会自主撰写代码`,
      orchestrator: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\nCommand(Ctrl)/Shift+Enter可换行\n${JOYCODE_BRAND_NAME}智能体团队能够协调复杂工作流，将任务分解并委派给专门的智能体`,
      debug: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\nCommand(Ctrl)/Shift+Enter可换行\n${JOYCODE_BRAND_NAME}问题修复智能体能够系统性地诊断和解决编码问题`,
      ask: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\nCommand(Ctrl)/Shift+Enter可换行\n${JOYCODE_BRAND_NAME}技术问答智能体能够专业解答软件开发问题并提供技术信息`,

      default: `@文件/文件夹可作为上下文，↑↓可切换历史提示词\nCommand(Ctrl)/Shift+Enter可换行`,
    },
  },
  buttons: {
    primary: {
      editFile: '保存',
      default: '同意',
      approve: '同意',
      command: '运行命令',
      retry: '重试',
      proceed: '继续执行',
      startNew: '开始新的任务',
      resume: '恢复任务',
    },
    secondary: {
      editFile: '拒绝',
      default: '拒绝',
      reject: '拒绝',
      startNew: '开始新的任务',
    },
  },
};

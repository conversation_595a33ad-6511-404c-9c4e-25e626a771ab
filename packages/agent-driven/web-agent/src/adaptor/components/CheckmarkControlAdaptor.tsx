import { useCallback, useState, useEffect, useMemo } from 'react';
import { useEvent } from 'react-use';
import styled from 'styled-components';
import { ExtensionMessage, JoyCoderMessage } from '../../../../src/shared/ExtensionMessage';
import { vscode } from '../../utils/vscode';
import { VSCodeButton } from '@vscode/webview-ui-toolkit/react';
import { createPortal } from 'react-dom';
import { useFloating, offset, flip, shift } from '@floating-ui/react';

interface CheckmarkControlAdaptorProps {
  messageTs?: number;
  previousMessage?: JoyCoderMessage | null;
}

export const CheckmarkControlAdaptor = ({ messageTs, previousMessage }: CheckmarkControlAdaptorProps) => {
  const [restoreBothDisabled, setRestoreBothDisabled] = useState(false);
  const [showRestoreConfirm, setShowRestoreConfirm] = useState(false);

  // 判断上一条消息是否为AI消息
  const isPreviousMessageAI = useCallback(() => {
    if (!previousMessage) return false;

    // 用户消息的判断逻辑
    const isUser = previousMessage.say === 'user_feedback' ||
                   previousMessage.say === 'user_feedback_diff' ||
                   // 特殊情况：第一条消息可能是say: "text"但实际是用户输入
                   // 通过检查conversationHistoryIndex来判断是否是用户的初始输入
                   (previousMessage.say === 'text' && previousMessage.conversationHistoryIndex === -1);

    const isCheckpointOrTransition =
      previousMessage.say === 'checkpoint_created' ||
      previousMessage.ask === 'resume_completed_task' ||
      previousMessage.ask === 'resume_task' ||
      previousMessage.say === 'api_req_started' ||
      previousMessage.say === 'api_req_finished' ||
      previousMessage.say === 'api_req_retried' ||
      previousMessage.say === 'deleted_api_reqs';
    return !isUser && !isCheckpointOrTransition;
  }, [previousMessage]);

  // 根据上一条消息类型决定显示样式
  // 只有当上一条消息是AI消息时才显示文字按钮，用户消息时只显示图标
  const shouldShowTextButton = isPreviousMessageAI();

  const { refs, floatingStyles, update, placement } = useFloating({
    placement: 'bottom-end',
    middleware: [
      offset({
        mainAxis: 8,
        crossAxis: 10,
      }),
      flip(),
      shift(),
    ],
  });

  useEffect(() => {
    const handleScroll = () => {
      update();
    };
    window.addEventListener('scroll', handleScroll, true);
    return () => window.removeEventListener('scroll', handleScroll, true);
  }, [update]);

  useEffect(() => {
    if (showRestoreConfirm) {
      update();
    }
  }, [showRestoreConfirm, update]);

  // 点击外部区域关闭弹框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showRestoreConfirm) {
        const dialogElement = refs.floating.current;
        const buttonElement = refs.reference.current;

        if (
          dialogElement &&
          buttonElement &&
          buttonElement instanceof Element &&
          !dialogElement.contains(event.target as Node) &&
          !buttonElement.contains(event.target as Node)
        ) {
          setShowRestoreConfirm(false);
        }
      }
    };

    if (showRestoreConfirm) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showRestoreConfirm, refs.floating, refs.reference]);

  const handleMessage = useCallback((event: MessageEvent<ExtensionMessage>) => {
    if (event.data.type === 'relinquishControl') {
      setRestoreBothDisabled(false);
      setShowRestoreConfirm(false);
    }
  }, []);

  const handleRestoreBoth = () => {
    setRestoreBothDisabled(true);
    setShowRestoreConfirm(false);
    vscode.postMessage({
      type: 'checkpointRestore',
      number: messageTs,
      text: 'taskAndWorkspace',
    });
  };

  const handleCancel = () => {
    setShowRestoreConfirm(false);
  };

  const handleButtonClick = () => {
    setShowRestoreConfirm(!showRestoreConfirm);
  };

  useEvent('message', handleMessage);

  return (
    <Container>
      <div ref={refs.setReference} style={{ position: 'relative' }}>
        {shouldShowTextButton ? (
          // 当上一条消息为AI消息时，显示带文字的按钮
          <TextRollbackButton
            title="回滚代码文件及消息至此处"
            onClick={handleButtonClick}
            disabled={restoreBothDisabled}
          >
            <i
              className="icon iconfont icon-fanhuishangyibu"
              style={{
                fontSize: '14px',
                marginRight: '6px',
                color: 'var(--vscode-descriptionForeground)'
              }}
            />
            回滚到当前检查点
          </TextRollbackButton>
        ) : (
          // 当上一条消息为用户消息时，显示简单的图标按钮
          <RollbackButton
            title="回滚代码文件及消息至此处"
            onClick={handleButtonClick}
            disabled={restoreBothDisabled}
          >
            <i
              className="icon iconfont icon-fanhuishangyibu"
              style={{
                fontSize: '14px',
                color: 'var(--vscode-descriptionForeground)'
              }}
            />
          </RollbackButton>
        )}
        {showRestoreConfirm &&
          createPortal(
            <RestoreConfirmDialog
              ref={refs.setFloating}
              style={floatingStyles}
              data-placement={placement}
            >
              <DialogContainer>
                <div>
                  <DialogTitle>回滚对话及代码变更至此消息之前？</DialogTitle>
                  <WaitingText>
                    <img style={{width: '12px'}} src="https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/waiting.gif" alt="加载中..." />
                    等待回复<DynamicDots />
                  </WaitingText>
                </div>
                <ButtonGroup>
                  <CancelButton onClick={handleCancel}>
                    取消
                  </CancelButton>
                  <ConfirmButton
                    onClick={handleRestoreBoth}
                    disabled={restoreBothDisabled}
                  >
                    确定
                  </ConfirmButton>
                </ButtonGroup>
              </DialogContainer>
            </RestoreConfirmDialog>,
            document.body,
          )}
      </div>
    </Container>
  );
};

const Container = styled.div`
  display: inline-flex;
  align-items: center;
  position: relative;
`;

const RollbackButton = styled.button`
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background: var(--vscode-toolbar-hoverBackground);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const TextRollbackButton = styled.button`
  background: var(--vscode-button-secondaryBackground);
  border: 1px solid var(--vscode-button-border);
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--vscode-button-secondaryForeground);
  font-size: 12px;
  font-family: var(--vscode-font-family);
  white-space: nowrap;

  &:hover:not(:disabled) {
    background: var(--vscode-button-secondaryHoverBackground);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const RestoreConfirmDialog = styled.div`
  position: fixed;
  background: var(--vscode-quickInput-background);
  border: 1px solid var(--vscode-quickInput-border);
  border-radius: 6px;
  padding: 10px 16px;
  min-width: 320px;
  max-width: 400px;
  z-index: 1000;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(48,48,53,1);

  // Add invisible padding to create a safe hover zone
  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 0;
    right: 0;
    height: 8px;
  }

  // Adjust arrow to be above the padding
  &::after {
    content: '';
    position: absolute;
    top: -6px;
    right: 24px;
    width: 10px;
    height: 10px;
    background: var(--vscode-quickInput-background);
    border-left: 1px solid var(--vscode-quickInput-border);
    border-top: 1px solid var(--vscode-quickInput-border);
    transform: rotate(45deg);
    z-index: 1;
  }

  // When menu appears above the button
  &[data-placement^='top'] {
    &::before {
      top: auto;
      bottom: -8px;
    }

    &::after {
      top: auto;
      bottom: -6px;
      right: 24px;
      transform: rotate(225deg);
    }
  }
`;

const DialogContainer = styled.div`
  display: flex;
  align-items: center;
`;

const DialogTitle = styled.div`
  // color: var(--vscode-foreground);
  color: rgba(255,255,255,0.8);
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 5px;
  line-height: 18px;
  font-family: PingFang SC;
  font-weight: normal;
`;

const DynamicDots = () => {
  const [dots, setDots] = useState('');
  
  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 400);
    
    return () => clearInterval(interval);
  }, []);

  return <span>{dots}</span>;
};

const WaitingText = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--vscode-descriptionForeground);
  font-size: 12px;
  color: rgba(255,255,255,0.3)
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  height: 28px;
`;

const CancelButton = styled(VSCodeButton)`
  min-width: 48px;
  background: rgba(48,48,53,1);
  color: rgba(255,255,255,0.8);
`;

const ConfirmButton = styled(VSCodeButton)`
  min-width: 48px;
  // background: var(--vscode-button-background);
  // color: var(--vscode-button-foreground);
  background: rgba(240,247,255,1);
  color: rgba(24,24,27,1);
  
  &:hover:not(:disabled) {
    // background: var(--vscode-button-hoverBackground);
    background: rgba(48,48,53,1);
    color: rgba(255,255,255,0.8);
  }
`;

.joycoder-coder-mode-dropdown{
  font-size: 10px;
  min-width: 63px;
  height: 20px;
  overflow: hidden;
  display: block;
  margin: 0 4px 0 0;
  min-width: auto !important;
  &-option{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 10px;
    padding: 5px 12px 5px 10px;
    i{
      display: inline-flex !important;
      font-size: 10px !important;
      vertical-align: text-bottom;
    }
    > span{
      margin-right: 4px;
    }
    &-title{
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .ant-dropdown-menu{
    max-height: 400px;
    overflow: auto !important;
  }
  &-button{
    border: 1px solid rgba(67, 67, 67, 0);
    box-shadow: rgba(0, 0, 0, 0.016) 0px 2px 0px;
    transition: 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    user-select: none;
    touch-action: manipulation;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    i{
      display: inline-flex !important;
      font-size: 10px !important;
    }
    > span{
      margin: 0 2px;
    }
    .auto-text::after{
      content: '自动执行';
      font-size: 8px;
      margin-left: 2px;
      opacity: 0.6;
      // color: rgba(255, 255, 255, 0.6);
    }
  }
  .ant-dropdown-menu-item-group{
    &-title{
      padding: 5px 12px;
      color: var(--input-placeholder-foreground);
      transition: all 0.2s;
      font-size: 10px;
    }
    &-list{
      padding-inline-start: 5px;
      padding-inline-end: 5px;
    }
  }
  &.joycoder-chatgpt-model-dropdown{
    .ant-dropdown-placement-top{
      // width: 93px !important;
      min-width: 93px !important;
      .ant-dropdown-menu{
        min-width: 93px !important;
        .ant-dropdown-menu-item{
          min-width: 93px !important;
        }
      }
    }
  }
}
.joycoder-chatgpt-input-icons{
  background: rgb(33, 33, 34) !important;
  z-index: 20 !important;
  margin-bottom: 10px;
}
.vscode-light {
  .joycoder-chatgpt-input-icons {
    background: var(--background) !important;
  }
}

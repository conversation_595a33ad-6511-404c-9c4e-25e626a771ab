import React, { useState } from 'react';
import { JOYCODE_BRAND_NAME } from '@joycoder/shared/src/constants/uiConstants';
import { vscode } from '../../utils/vscode';
import './index.scss';

export default function Login(props?: any) {
  const [logoImg] = useState('https://storage.jd.com/dist-dev/joycoder/chatDialog/logo.svg');
  const login = () => {
    vscode.postMessage({
      type: 'JUMP_LOGIN',
    });
  };

  return (
    <>
      {!props?.isLogined && (
        <div className="joycoder-chart-login">
          <div className="joycoder-chart-login-logo">
            <img src={logoImg} alt="" />
            {/* <span>JoyCode</span> */}
          </div>
          <div className="joycoder-chart-login-title">登录 {JOYCODE_BRAND_NAME} 协同开发</div>
          <div className="joycoder-chart-login-btn" onClick={login}>
            登录
          </div>
        </div>
      )}
    </>
  );
}

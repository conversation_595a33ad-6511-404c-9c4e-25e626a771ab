.joycoder-chart-login{
  font-family: 'PingFang SC';
  padding: 0;
  min-height: 100vh;
  box-sizing: border-box;
  margin: 0;
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background);
  &-logo{
    margin: 20vh auto 0;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 56px;
    color: #2695F1;
    height: 70px;
    font-weight: 600;
    img{
      height: 64px;
      width: 64px;
      display: block;
    }
  }
  &-title{
    height: 45px;
    line-height: 45px;
    font-size: 16px;
    text-align: center;
    margin: 16px 0 0;
    font-weight: 600;
    color: var(--vscode-editor-foreground);
  }
  &-btn{
    width: 160px;
    height: 32px;
    line-height: 32px;
    border-radius: 26px;
    background: rgba(36,123,255,1);
    color: #ffffff;
    font-size: 13px;
    text-align: center;
    margin: 38vh auto 0;
    cursor: pointer;
  }
}
.joycoder-light {
  .joycoder-chart-desc,.joycoder-chart-question{
    h4{
      color: #333 !important;
    }
  }
}

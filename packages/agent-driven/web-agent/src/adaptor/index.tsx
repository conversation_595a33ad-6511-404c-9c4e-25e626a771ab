import React, { useCallback, useEffect, useState } from 'react';
import { vscode } from '../utils/vscode';
import WelcomeView from './components/WelcomeView';
import HistoryPreview from '../components/history/HistoryPreview';
import McpView from '../components/mcp/McpView';
import { ExtensionMessage } from '../../../src/shared/ExtensionMessage';
import { useEvent } from 'react-use';

interface IndexProps {
  showIndex: boolean;
  showHistoryView?: () => void;
  toggleMCP?: () => void;
  setShowMcpView: () => void;
  showChatView: () => void;
  isIDE: boolean;
  isMacOs: boolean;
}

const Index: React.FC<IndexProps> = ({ showIndex, showHistoryView, isIDE, isMacOs, setShowMcpView, showChatView }) => {
  const [showHistoryPreview, setShowHistoryPreview] = useState(false);
  const [showMCP, setShowMCP] = useState(false);
  const [isRemoteEnvironment, setIsRemoteEnvironment] = useState(false);

  const handleMessage = useCallback(
    (e: MessageEvent) => {
      const message: ExtensionMessage = e.data;
      switch (message.type) {
        case 'action':
          switch (message.action!) {
            case 'mcpButtonClicked':
              setShowMcpView();
              setShowHistoryPreview(false);
              setShowMCP(true);
              setIsRemoteEnvironment(message.data?.isRemoteEnvironment);
              break;
            case 'historyButtonClicked':
              showHistoryView && showHistoryView();
              setShowHistoryPreview(false);
              setShowMCP(false);
              break;
            case 'openInNewChat':
              setShowMCP(false);
              showChatView();
              setShowHistoryPreview(false);
              break;
            case 'chatButtonClicked':
              setShowMCP(false);
              showChatView();
              setShowHistoryPreview(false);
              break;
          }
          break;
      }
    },
    [setShowMcpView, showHistoryView, showChatView],
  );

  useEvent('message', handleMessage);

  const sendExample = (text: string) => {
    vscode.postMessage({ type: 'newTask', text, images: [] });
  };

  if (!showIndex) {
    return null;
  }

  if (showMCP) {
    return (
      <McpView
        onDone={() => {
          showChatView();
          setShowMCP(false);
          setShowHistoryPreview(false);
        }}
        isRemoteEnvironment={isRemoteEnvironment}
      />
    );
  }

  return (
    <div
      style={{
        flexGrow: 1,
        overflowY: 'auto',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* {!isIDE && <HeaderTools newChat={newChat} toggleHistoryPreview={toggleHistoryPreview} toggleMCP={toggleMCP} />} */}
      {!showHistoryPreview && <WelcomeView isMacOs={isMacOs} sendExample={sendExample} />}
      {showHistoryPreview && showHistoryView && <HistoryPreview showHistoryView={showHistoryView} />}
    </div>
  );
};

export default Index;

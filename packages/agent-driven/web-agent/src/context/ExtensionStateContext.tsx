import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';
import { useEvent } from 'react-use';
import { DEFAULT_AUTO_APPROVAL_SETTINGS } from '../../../src/shared/AutoApprovalSettings';
import { ExtensionMessage, ExtensionState, DEFAULT_PLATFORM } from '../../../src/shared/ExtensionMessage';
import {
  ApiConfiguration,
  ModelInfo,
  openRouterDefaultModelId,
  openRouterDefaultModelInfo,
} from '../../../src/shared/api';
import { findLastIndex } from '../../../src/shared/array';
import { McpServer } from '../../../src/shared/mcp';
import { convertTextMateToHljs } from '../utils/textMateToHljs';
import { vscode } from '../utils/vscode';
import { DEFAULT_BROWSER_SETTINGS } from '../../../src/shared/BrowserSettings';
import { DEFAULT_CHAT_SETTINGS } from '../../../src/shared/ChatSettings';
import { defaultModeSlug, defaultPrompts } from '../utils/modes';
import { CustomSupportPrompts } from '../utils/support-prompt';

interface ExtensionStateContextType extends ExtensionState {
  didHydrateState: boolean;
  showWelcome: boolean;
  theme: any;
  promptList: any;
  openRouterModels: Record<string, ModelInfo>;
  openAiModels: string[];
  mcpServers: McpServer[];
  filePaths: string[];
  ruleFiles: string[]; // 添加 ruleFiles 属性
  totalTasksSize: number | null;
  setApiConfiguration: (config: ApiConfiguration) => void;
  setCustomInstructions: (value?: string) => void;
  setShowAnnouncement: (value: boolean) => void;
  customModes: any[];
  setCustomModes: (value: any[]) => void;
  mode: any;
  setMode: (value: any) => void;
  setCustomModePrompts: (value: any) => void;
  setCustomSupportPrompts: (value: CustomSupportPrompts) => void;
}

const ExtensionStateContext = createContext<ExtensionStateContextType | undefined>(undefined);

export const ExtensionStateContextProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [state, setState] = useState<ExtensionState>({
    version: '',
    joycoderMessages: [],
    taskHistory: [],
    shouldShowAnnouncement: false,
    autoApprovalSettings: DEFAULT_AUTO_APPROVAL_SETTINGS,
    browserSettings: DEFAULT_BROWSER_SETTINGS,
    chatSettings:
      defaultModeSlug === 'code'
        ? DEFAULT_CHAT_SETTINGS
        : {
            mode: defaultModeSlug,
          },
    isLoggedIn: false,
    platform: DEFAULT_PLATFORM,
    isRemoteEnvironment: false,
    updateStatus: 'done',
    mode: defaultModeSlug,
    customSupportPrompts: {},
    customModePrompts: defaultPrompts,
    customModes: [],
  });
  const [didHydrateState, setDidHydrateState] = useState(false);
  const [showWelcome, setShowWelcome] = useState(false);
  const [theme, setTheme] = useState<any>(undefined);
  const [promptList, setPromptList] = useState<any[]>([]);
  const [updateStatus, setUpdateStatus] = useState<string>('done');
  const [filePaths, setFilePaths] = useState<string[]>([]);
  const [ruleFiles, setRuleFiles] = useState<string[]>([]); // 添加 ruleFiles 状态
  const [openRouterModels, setOpenRouterModels] = useState<Record<string, ModelInfo>>({
    [openRouterDefaultModelId]: openRouterDefaultModelInfo,
  });
  const [totalTasksSize, setTotalTasksSize] = useState<number | null>(null);
  const [openAiModels, setOpenAiModels] = useState<string[]>([]);
  const [mcpServers, setMcpServers] = useState<McpServer[]>([]);

  const handleMessage = useCallback((event: MessageEvent) => {
    const message: ExtensionMessage = event.data;
    switch (message.type) {
      case 'state': {
        setState(message.state!);
        setShowWelcome(false);
        setDidHydrateState(true);
        break;
      }
      case 'modeUpdate': {
        // Only update mode-related state to prevent unnecessary re-renders
        setState(prevState => ({
          ...prevState,
          mode: message.mode!,
          chatSettings: message.chatSettings!,
        }));
        break;
      }
      case 'theme': {
        if (message.text) {
          setTheme(convertTextMateToHljs(JSON.parse(message.text)));
        }
        break;
      }
      case 'updateUserPrompt': {
        const promptList = message?.promptList || [];
        setPromptList(promptList);
        break;
      }
      case 'workspaceUpdated': {
        setFilePaths(message.filePaths ?? []);
        setRuleFiles(message.ruleFiles ?? []); // 设置 ruleFiles 状态
        setUpdateStatus(message.updateStatus ?? 'done');
        break;
      }
      case 'workspaceUpdating': {
        setUpdateStatus(message.updateStatus ?? 'done');
        break;
      }
      case 'partialMessage': {
        const partialMessage = message.partialMessage!;
        setState((prevState) => {
          // worth noting it will never be possible for a more up-to-date message to be sent here or in normal messages post since the presentAssistantContent function uses lock
          const lastIndex = findLastIndex(prevState.joycoderMessages, (msg) => msg.ts === partialMessage.ts);
          if (lastIndex !== -1) {
            const newJoyCoderMessages = [...prevState.joycoderMessages];
            newJoyCoderMessages[lastIndex] = partialMessage;
            return { ...prevState, joycoderMessages: newJoyCoderMessages };
          }
          return prevState;
        });
        break;
      }
      case 'openRouterModels': {
        const updatedModels = message.openRouterModels ?? {};
        setOpenRouterModels({
          [openRouterDefaultModelId]: openRouterDefaultModelInfo, // in case the extension sent a model list without the default model
          ...updatedModels,
        });
        break;
      }
      case 'openAiModels': {
        const updatedModels = message.openAiModels ?? [];
        setOpenAiModels(updatedModels);
        break;
      }
      case 'mcpServers': {
        setMcpServers(message.mcpServers ?? []);
        break;
      }
      case 'totalTasksSize': {
        setTotalTasksSize(message.totalTasksSize ?? null);
        break;
      }
    }
  }, []);

  useEvent('message', handleMessage);

  useEffect(() => {
    vscode.postMessage({ type: 'webviewDidLaunch' });
  }, []);

  const contextValue: ExtensionStateContextType = {
    ...state,
    didHydrateState,
    showWelcome,
    theme,
    promptList,
    openRouterModels,
    openAiModels,
    mcpServers,
    filePaths,
    ruleFiles, // 在 contextValue 中添加 ruleFiles
    totalTasksSize,
    setApiConfiguration: (value) =>
      setState((prevState) => ({
        ...prevState,
        apiConfiguration: value,
      })),
    setCustomInstructions: (value) =>
      setState((prevState) => ({
        ...prevState,
        customInstructions: value,
      })),
    setShowAnnouncement: (value) =>
      setState((prevState) => ({
        ...prevState,
        shouldShowAnnouncement: value,
      })),
    updateStatus,
    setCustomModes: function (value: any[]): void {
      setState((prevState) => ({ ...prevState, customModes: value }));
    },
    setMode: function (value: string): void {
      setState((prevState) => ({ ...prevState, mode: value }));
    },
    setCustomModePrompts: function (value: any): void {
      setState((prevState) => ({ ...prevState, customModePrompts: value }));
    },
    setCustomSupportPrompts: function (value: CustomSupportPrompts): void {
      setState((prevState) => ({
        ...prevState,
        customSupportPrompts: value,
      }));
    },
  };

  return <ExtensionStateContext.Provider value={contextValue}>{children}</ExtensionStateContext.Provider>;
};

export const useExtensionState = () => {
  const context = useContext(ExtensionStateContext);
  if (context === undefined) {
    throw new Error('useExtensionState must be used within an ExtensionStateContextProvider');
  }
  return context;
};

import { AutoApprovalSettings } from '../../../src/shared/AutoApprovalSettings';

export const ACTION_METADATA: {
  id: keyof AutoApprovalSettings['actions'];
  label: string;
  shortName: string;
  description: string;
}[] = [
  {
    id: 'readFiles',
    label: '读取文件和目录',
    // label: 'Read files and directories',
    shortName: '读取权限',
    description: '允许读取工作空间的文件。',
  },
  {
    id: 'editFiles',
    label: '编辑文件、切换模式、执行子任务',
    shortName: '写入权限',
    description: '允许模型自动修改工作空间的文件、切换模式、执行子任务，请慎重勾选。',
  },
  {
    id: 'executeCommands',
    label: '允许JoyCoder自动执行安全的终端指令',
    shortName: '命令行操作',
    description:
      '允许JoyCoder自动执行安全的终端指令。如果开启该开关，当模型检测到命令存在潜在风险，依然会将向您请求审核执行。',
  },
  {
    id: 'useBrowser',
    label: '使用无头浏览器搜索信息',
    shortName: '使用浏览器',
    description: '允许使用无头浏览器搜索任何网络信息并与之交互。',
  },
  {
    id: 'useMcp',
    label: '使用MCP服务',
    shortName: 'MCP',
    description: '允许使用已配置的MCP服务器,这些服务器可能会修改文件系统或与API进行交互。',
  },
];

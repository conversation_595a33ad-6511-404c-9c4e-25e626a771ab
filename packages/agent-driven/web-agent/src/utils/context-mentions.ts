import { mentionRegex } from '../../../src/shared/context-mentions';

export function insertMention(
  text: string,
  position: number,
  value: string
): { newValue: string; mentionIndex: number } {
  const beforeCursor = text.slice(0, position);
  const afterCursor = text.slice(position);

  // Find the position of the last '@' symbol before the cursor
  const lastAtIndex = beforeCursor.lastIndexOf('@');

  let newValue: string;
  let mentionIndex: number;

  // Check if the value already starts with '@'
  const prefix = value.startsWith('@') ? '' : '@';

  if (lastAtIndex !== -1) {
    // If there's an '@' symbol, replace everything after it with the new mention
    const beforeMention = text.slice(0, lastAtIndex);
    newValue = beforeMention + prefix + value + ' ' + afterCursor; //.replace(/^[^\s]*/, '');
    mentionIndex = lastAtIndex;
  } else {
    // If there's no '@' symbol, insert the mention at the cursor position
    newValue = beforeCursor + prefix + value + ' ' + afterCursor;
    mentionIndex = position;
  }

  return { newValue, mentionIndex };
}

export function insertMentionDirectly(
  text: string,
  position: number,
  value: string
): { newValue: string; mentionIndex: number } {
  const beforeCursor = text.slice(0, position);
  const afterCursor = text.slice(position);
  const newValue = beforeCursor + value + ' ' + afterCursor;
  const mentionIndex = position;
  return { newValue, mentionIndex };
}
export function removeMention(text: string, position: number): { newText: string; newPosition: number } {
  const beforeCursor = text.slice(0, position);
  const afterCursor = text.slice(position);

  // Check if we're at the end of a mention
  const matchEnd = beforeCursor.match(new RegExp(mentionRegex.source + '$'));

  if (matchEnd) {
    // If we're at the end of a mention, remove it
    const newText = text.slice(0, position - matchEnd[0].length) + afterCursor.replace(' ', ''); // removes the first space after the mention
    const newPosition = position - matchEnd[0].length;
    return { newText, newPosition };
  }

  // If we're not at the end of a mention, just return the original text and position
  return { newText: text, newPosition: position };
}

export enum ContextMenuOptionType {
  File = 'file',
  Folder = 'folder',
  Problems = 'problems',
  URL = 'url',
  // Terminal = 'terminal',
  // Git = 'git',
  Web = 'web',
  UserRules = 'userRules',
  NoResults = 'noResults',
  Codebase = 'codebase',
}

export interface ContextMenuQueryItem {
  type: ContextMenuOptionType;
  value?: string;
}

export function getContextMenuOptions(
  query: string,
  selectedType: ContextMenuOptionType | null = null,
  queryItems: ContextMenuQueryItem[]
): ContextMenuQueryItem[] {
  if (query === '') {
    if (selectedType === ContextMenuOptionType.File) {
      const files = queryItems
        .filter((item) => item.type === ContextMenuOptionType.File)
        .map((item) => ({
          type: ContextMenuOptionType.File,
          value: item.value,
        }));
      return files.length > 0 ? files : [{ type: ContextMenuOptionType.NoResults }];
    }

    if (selectedType === ContextMenuOptionType.Folder) {
      const folders = queryItems
        .filter((item) => item.type === ContextMenuOptionType.Folder)
        .map((item) => ({
          type: ContextMenuOptionType.Folder,
          value: item.value,
        }));
      return folders.length > 0 ? folders : [{ type: ContextMenuOptionType.NoResults }];
    }

    if (selectedType === ContextMenuOptionType.UserRules) {
      const rules = queryItems
        .filter((item) => item.type === ContextMenuOptionType.UserRules)
        .map((item) => ({
          type: ContextMenuOptionType.UserRules,
          value: item.value,
        }));
      return rules.length > 0 ? rules : [{ type: ContextMenuOptionType.NoResults }];
    }

    return [
      { type: ContextMenuOptionType.URL },
      { type: ContextMenuOptionType.Problems },
      { type: ContextMenuOptionType.Web },
      { type: ContextMenuOptionType.UserRules },
      { type: ContextMenuOptionType.Folder },
      { type: ContextMenuOptionType.File },
      { type: ContextMenuOptionType.Codebase },
    ];
  }

  const lowerQuery = query.toLowerCase();

  if (query.startsWith('http')) {
    return [{ type: ContextMenuOptionType.URL, value: query }];
  } else {
    const matchingItems = queryItems.filter((item) => item.value?.toLowerCase().includes(lowerQuery));

    if (matchingItems.length > 0) {
      return matchingItems.map((item) => ({
        type: item.type,
        value: item.value,
      }));
    } else {
      return [{ type: ContextMenuOptionType.NoResults }];
    }
  }
}

export function shouldShowContextMenu(text: string, position: number): boolean {
  const beforeCursor = text.slice(0, position);
  const atIndex = beforeCursor.lastIndexOf('@');

  if (atIndex === -1) return false;

  const textAfterAt = beforeCursor.slice(atIndex + 1);

  // Check if there's any whitespace after the '@'
  if (/\s/.test(textAfterAt)) return false;

  // Don't show the menu if it's a URL
  if (textAfterAt.toLowerCase().startsWith('http')) return false;

  // Don't show the menu if it's a problems or web
  if (textAfterAt.toLowerCase().startsWith('problems')) return false;
  if (textAfterAt.toLowerCase().startsWith('web')) return false;
  if (textAfterAt.toLowerCase().startsWith('codebase')) return false;

  // NOTE: it's okay that menu shows when there's trailing punctuation since user could be inputting a path with marks

  // Show the menu if there's just '@' or '@' followed by some text (but not a URL)
  return true;
}

export function getContextItem(fileInfo: any, isSelection?: boolean, selectText?: string): string {
  const contextItem = `<div class="pulse-chip-container ${isSelection ? 'active' : ''}" file-ext-name="${
    fileInfo.selectionFileText
  }" file-path="${fileInfo.filePath}">
    <div style="display:none;" class="select-text"><selection_content path="${fileInfo.filePath}: L${
    fileInfo?.startLine
  }#${fileInfo?.startCharacter}-L${fileInfo?.endLine}#${fileInfo?.endCharacter}">${selectText}</selection_content></div>
    <div class="pulse-chip">
      <div style="flex: 0 0 auto;">
        <div class="pulse-chip-icon codicon codicon-${isSelection ? 'code' : 'file'}"></div>
      </div>
      <div style="flex: 1 1 100%; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
        <div>${fileInfo.fileName}: L${fileInfo?.startLine}#${fileInfo?.startCharacter}-L${fileInfo?.endLine}#${
    fileInfo?.endCharacter
  }</div>
      </div>
      <div style="flex: 0 0 auto;">
        <div class="close-pulse-chip codicon codicon-close"></div>
      </div>
    </div>
  </div>`;
  return contextItem;
}

// 添加一个新的函数来处理关闭事件
export function setupContextItemCloseHandlers() {
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    if (target.classList.contains('close-pulse-chip')) {
      const container = target.closest('.pulse-chip-container');
      if (container) {
        container.remove();
      }
    }
  });
}

export function getContextItemContents(): string {
  const contextItems = document.querySelectorAll('.pulse-chip-container');
  const contextBox = document.querySelector('.joycoder-context-box');
  const contextItemContents = [];
  try {
    if (contextItems.length > 0) {
      for (let i = 0; i < contextItems.length; i++) {
        const contextItem = contextItems[i];
        const contextItemSelection = contextItem.querySelectorAll('.select-text')[0];
        console.log(contextItem, contextItemSelection);

        contextItemContents.push(contextItemSelection.innerHTML);
        // contextItemPaths.push(contextItem.getAttribute('file-ext-name'));
        // contextItemPaths.push(contextItem.getAttribute('file-path'));
        contextBox?.removeChild(contextItem);
      }
    } else {
      return '';
    }
  } catch (error) {
    console.log('%c [ 获取添加的上下文UI异常-error ]-196', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
  return `\n${contextItemContents.join('\n')}`;
}
export function delContextItem() {
  const contextBox = document.querySelector('.joycoder-context-box');
  try {
    contextBox && (contextBox.innerHTML = '');
  } catch (error) {
    console.log('%c [ 删除的上下文UI异常-error ]-196', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}

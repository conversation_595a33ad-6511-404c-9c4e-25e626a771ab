import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import tailwindcss from '@tailwindcss/postcss';
import { splitVendorChunkPlugin } from 'vite';
const pluginVer = process.env.PLUGIN_VER;

export default defineConfig(({ mode }) => ({
  plugins: [react(), splitVendorChunkPlugin()],
  css: {
    postcss: {
      plugins: [tailwindcss],
    },
  },
  base: './',
  build: {
    outDir: path.resolve(__dirname, pluginVer === 'ide' ? '../../vscode-IDE/dist/webview' : '../../../dist/webview'),
    emptyOutDir: true,
    assetsInlineLimit: 2048,
    modulePreload: true,
    cssCodeSplit: true,
    minify: mode === 'production' ? 'terser' : false,
    // 不使用 esbuild 压缩 CSS
    cssMinify: false, // 关闭内置的 CSS 压缩，将由 terser 处理
    reportCompressedSize: false,
    // 启用并行处理
    workers: true,
    sourcemap: mode !== 'production',
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'src/index.tsx'),
      },
      output: {
        inlineDynamicImports: false,
        entryFileNames: '[name].js',
        chunkFileNames: '[name]-[hash].js',
        assetFileNames: '[name].[ext]',
      },
    },
    chunkSizeWarningLimit: 5000,
    terserOptions:
      mode === 'production'
        ? {
            compress: {
              // drop_console: true,
              drop_debugger: true,
            },
          }
        : undefined,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  // 启用缓存以加速构建
  cacheDir: 'node_modules/.vite',
  server: {
    port: 3000,
    open: true,
  },
}));

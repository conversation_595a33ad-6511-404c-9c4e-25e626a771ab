import delay from 'delay';
import * as vscode from 'vscode';
import { DIFF_VIEW_URI_SCHEME } from './src/integrations/editor/DiffViewProvider';
import { createJoyCoderAPI } from './src/exports';
import { JoyCoderProvider } from './src/core/webview/JoycoderProvider';
import { checkLogin, getVscodeConfig, isIDE, Logger, setVscodeConfig } from '@joycoder/shared';
import './src/utils/path'; // necessary to have access to String.prototype.toPosix
import { getJoyCoderContextMenus } from '@joycoder/plugin-base-ai/src/contextMenu';
import { decorationType } from '@joycoder/plugin-base-ai/src/dialog';
import { isRemoteEnvironment } from './src/utils/fs';

export { createJoyCoderAPI, JoyCoderProvider };

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export default async function (context: vscode.ExtensionContext) {
  Logger.log('JoyCoder extension activated');

  const sidebarProvider = new JoyCoderProvider(context);
  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(JoyCoderProvider.sideBarId, sidebarProvider, {
      webviewOptions: { retainContextWhenHidden: true },
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCoder.Coder.Logout', async () => {
      await sidebarProvider.postMessageToWebview({ type: 'updateLoginStatus', data: { isLogin: false } });
    })
  );
  context.subscriptions.push(
    vscode.commands.registerCommand('joycoder.Coder.plusButtonClicked', async () => {
      Logger.log('Plus button Clicked');
      await sidebarProvider.clearTask();
      await sidebarProvider.postStateToWebview();
      await sidebarProvider.postMessageToWebview({
        type: 'action',
        action: 'chatButtonClicked',
      });
    })
  );
  context.subscriptions.push(
    vscode.commands.registerCommand('joycoder.Coder.mcpButtonClicked', () => {
      sidebarProvider.postMessageToWebview({
        type: 'action',
        action: 'mcpButtonClicked',
        data: {
          isRemoteEnvironment: isRemoteEnvironment() ?? false,
        },
      });
    })
  );
  context.subscriptions.push(
    vscode.commands.registerCommand('joycoder.autoCode.syncState', () => synchronizationStateToWebview(sidebarProvider))
  );
  context.subscriptions.push(
    // 切换为Chat
    vscode.commands.registerCommand('joycoder.switch.chat', async () => {
      try {
        await vscode.workspace.getConfiguration().update('JoyCoder.switch.AgentView', false, false);
      } catch (e) {
        setVscodeConfig('JoyCoder.switch.AgentView', false);
      }
      vscode.commands.executeCommand('JoyCoder-left-view.focus');
    }),
    // 切换为Coder
    vscode.commands.registerCommand('joycoder.switch.Coder', async () => {
      try {
        await vscode.workspace.getConfiguration().update('JoyCoder.switch.AgentView', true, false);
      } catch (e) {
        setVscodeConfig('JoyCoder.switch.AgentView', true);
      }
      vscode.commands.executeCommand('joycoder.joycoder.SidebarProvider.focus');
    })
  );
  // 让每次开启新窗口时，joycoder都激活
  const knownWindows: Number[] = [];
  const focusJoyCoderSidebar = (windowState: vscode.WindowState) => {
    const pid = process.pid;
    if (windowState.focused && !knownWindows.includes(pid)) {
      knownWindows.push(pid);
      if (getVscodeConfig('JoyCoder.config.autoActivateOnNewWindow')) {
        vscode.commands.executeCommand('joycoder.joycoder.SidebarProvider.focus');
        vscode.commands.executeCommand('JoyCoder-left-view.focus');
      }
    }
  };
  context.subscriptions.push(vscode.window.onDidChangeWindowState(focusJoyCoderSidebar));
  // 如果插件激活时已有窗口打开，也执行一次
  focusJoyCoderSidebar(vscode.window.state);
  getJoyCoderContextMenus(context, decorationType, sidebarProvider);
  if (isIDE()) {
    context.subscriptions.push(
      vscode.commands.registerCommand('joycoder.joycoder.openInNewChat', () => {
        sidebarProvider.postMessageToWebview({
          type: 'action',
          action: 'openInNewChat',
        });
      })
    );
    context.subscriptions.push(
      vscode.commands.registerCommand('joycoder.joycoder.showAuxiliaryBar', () => {
        setAuxiliaryBar();
      })
    );
    setAuxiliaryBar();
    recursiveLoginInfo(sidebarProvider);
  }
  // setTimeout(async () => {
  //   vscode.commands.executeCommand('workbench.action.moveView', {
  //     viewId: JoyCoderProvider.sideBarId,
  //     to: 'right',
  //   });
  // }, 3000);
  const openJoyCoderInNewTab = async () => {
    Logger.log('Opening JoyCoder in new tab');
    // (this example uses webviewProvider activation event which is necessary to deserialize cached webview, but since we use retainContextWhenHidden, we don't need to use that event)
    // https://github.com/microsoft/vscode-extension-samples/blob/main/webview-sample/src/extension.ts
    const tabProvider = new JoyCoderProvider(context);
    //const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined
    const lastCol = Math.max(...vscode.window.visibleTextEditors.map((editor) => editor.viewColumn || 0));
    const hasSmileTab = vscode.window.tabGroups.all.filter((tab) => tab.activeTab?.label === 'Coder');
    // Check if there are any visible text editors, otherwise open a new group to the right
    const hasVisibleEditors = vscode.window.visibleTextEditors.length > 0;
    if (!hasVisibleEditors && hasSmileTab.length === 0) {
      await vscode.commands.executeCommand('workbench.action.newGroupRight');
    }

    const targetCol = hasVisibleEditors ? Math.max(lastCol + 1, 1) : vscode.ViewColumn.Two;
    const panel = vscode.window.createWebviewPanel(
      JoyCoderProvider.tabPanelId,
      'Coder',
      { viewColumn: targetCol, preserveFocus: true },
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [context.extensionUri],
      }
    );
    // TODO: use better svg icon with light and dark variants (see https://stackoverflow.com/questions/58365687/vscode-extension-iconpath)

    panel.iconPath = {
      light: vscode.Uri.joinPath(context.extensionUri, 'assets', 'icons', 'robot_panel_light.png'),
      dark: vscode.Uri.joinPath(context.extensionUri, 'assets', 'icons', 'robot_panel_dark.png'),
    };
    tabProvider.resolveWebviewView(panel);

    // Lock the editor group so clicking on files doesn't open them over the panel
    await delay(2900);
    await vscode.commands.executeCommand('workbench.action.lockEditorGroup');
  };

  context.subscriptions.push(
    vscode.commands.registerCommand('joycoder.joycoder.popoutButtonClicked', openJoyCoderInNewTab)
  );
  context.subscriptions.push(vscode.commands.registerCommand('joycoder.joycoder.openInNewTab', openJoyCoderInNewTab));

  context.subscriptions.push(
    vscode.commands.registerCommand('joycoder.joycoder.settingsButtonClicked', () => {
      //vscode.window.showInformationMessage(message)
      sidebarProvider.postMessageToWebview({
        type: 'action',
        action: 'settingsButtonClicked',
      });
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('joycoder.joycoder.historyButtonClicked', () => {
      sidebarProvider.postMessageToWebview({
        type: 'action',
        action: 'historyButtonClicked',
      });
    })
  );

  /*
	We use the text document content provider API to show the left side for diff view by creating a virtual document for the original content. This makes it readonly so users know to edit the right side if they want to keep their changes.

	- This API allows you to create readonly documents in VSCode from arbitrary sources, and works by claiming an uri-scheme for which your provider then returns text contents. The scheme must be provided when registering a provider and cannot change afterwards.
	- Note how the provider doesn't create uris for virtual documents - its role is to provide contents given such an uri. In return, content providers are wired into the open document logic so that providers are always considered.
	https://code.visualstudio.com/api/extension-guides/virtual-documents
	*/
  const diffContentProvider = new (class implements vscode.TextDocumentContentProvider {
    provideTextDocumentContent(uri: vscode.Uri): string {
      return Buffer.from(uri.query, 'base64').toString('utf-8');
    }
  })();
  context.subscriptions.push(
    vscode.workspace.registerTextDocumentContentProvider(DIFF_VIEW_URI_SCHEME, diffContentProvider)
  );

  // URI Handler
  const handleUri = async (uri: vscode.Uri) => {
    const path = uri.path;
    const query = new URLSearchParams(uri.query.replace(/\+/g, '%2B'));
    const visibleProvider = JoyCoderProvider.getVisibleInstance();
    if (!visibleProvider) {
      return;
    }
    switch (path) {
      case '/auth': {
        const token = query.get('token');
        const state = query.get('state');

        console.log('Auth callback received:', {
          token: token,
          state: state,
        });

        // Validate state parameter
        // if (!(await visibleProvider.validateAuthState(state))) {
        //   vscode.window.showErrorMessage('Invalid auth state');
        //   return;
        // }
        break;
      }
      default:
        break;
    }
  };
  try {
    const uriHandler = vscode.window.registerUriHandler({ handleUri } as vscode.UriHandler);
    context.subscriptions.push(uriHandler);
  } catch (error) {
    console.warn(error.message);
  }

  // 注册命令，允许外部调用JoyCoder功能
  context.subscriptions.push(
    vscode.commands.registerCommand(
      'joycoder.joycoderEditor.initModes',
      async (params: { mode?: string; text?: string }) => {
        // 先检测并加载mode.json文件(自定义模式配置文件)
        const customModes = await sidebarProvider.customModesManager.checkAndLoadRoomodes();

        if (customModes) {
          console.log(`[initModes] 成功加载${customModes.length}个自定义模式`);

          // 如果指定了模式且该模式存在于自定义模式中，则切换到该模式
          if (params.mode && customModes.some((mode) => mode.agentId === params.mode)) {
            console.log(`[initModes] 切换到指定模式: ${params.mode}`);
            await sidebarProvider.handleModeSwitch(params.mode);
          }
        }

        if (!sidebarProvider.getCurrentJoyCoder()) {
          // 如果JoyCoder实例不存在，先初始化
          await sidebarProvider.clearTask();
          await sidebarProvider.postStateToWebview();
          await sidebarProvider.initJoyCoderWithTask(params.text);
        }

        // 调用Provider的方法处理命令
        await sidebarProvider.handleExternalCommand(params);
      }
    )
  );

  return createJoyCoderAPI(sidebarProvider);
}

function setAuxiliaryBar() {
  const isAuxiliaryBarVisible = vscode.workspace.getConfiguration('workbench').get('auxiliaryBar.visible');
  if (!isAuxiliaryBarVisible) {
    vscode.commands.executeCommand('workbench.action.focusAuxiliaryBar');
  }
}
async function synchronizationStateToWebview(sidebarProvider: JoyCoderProvider) {
  // 调用传入的sidebarProvider对象的postStateToWebview方法，将状态同步到webview
  // 该函数作为一个包装器，简化了状态同步操作，直接返回postStateToWebview方法的执行结果
  const { autoApprovalSettings } = await sidebarProvider.getStateToPostToWebview();
  sidebarProvider?.getCurrentJoyCoder()?.updateAutoApprovalSettings(autoApprovalSettings);
  return sidebarProvider.postStateToWebview();
}
/**
 * 递归检查登录状态并更新侧边栏
 * @param sidebarProvider - 侧边栏提供者
 */
function recursiveLoginInfo(sidebarProvider: JoyCoderProvider) {
  if (!isIDE()) return;
  setTimeout(async () => {
    const isLoginStat = await checkLogin();
    await sidebarProvider.postMessageToWebview({ type: 'updateLoginStatus', data: { isLogin: isLoginStat } });
    recursiveLoginInfo(sidebarProvider);
  }, 5000);
}

import * as vscode from 'vscode';
import * as fs from 'fs/promises';
import * as os from 'os';
import * as path from 'path';
import * as playwright from 'playwright-core';
import pWaitFor from 'p-wait-for';
import delay from 'delay';
import * as cheerio from 'cheerio';
import TurndownService from 'turndown';
import BrowserClient from '@joycoder/plugin-base-browser/src/browser/BrowserClient';
import { BrowserActionResult } from '../../shared/ExtensionMessage';
import { BrowserSettings, DEFAULT_BROWSER_SETTINGS } from '../../shared/BrowserSettings';

export class BrowserSession {
  private context: vscode.ExtensionContext;
  private browser?: playwright.BrowserContext;
  private page?: playwright.Page;
  private currentMousePosition?: string;
  browserSettings: BrowserSettings;
  constructor(context: vscode.ExtensionContext, browserSettings: BrowserSettings = DEFAULT_BROWSER_SETTINGS) {
    this.context = context;
    this.browserSettings = browserSettings;
  }
  async launchBrowser() {
    if (this.browser) {
      await this.closeBrowser();
    }
    const chromeArgs: string[] = [];
    chromeArgs.push('--allow-file-access-from-files');
    chromeArgs.push('--remote-allow-origins=*');
    chromeArgs.push('--disable-gpu');
    chromeArgs.push(
      '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
    );
    // 设置远程调试端口
    // chromeArgs.push(`--remote-debugging-port=9222`);
    // 设置默认下载路径
    // chromeArgs.push(`--default-download-directory=${__dirname}`);
    // 设置代理
    // const { proxy } = GlobalState.get('JoyCoderBrowseLiteConfig') || {};
    // if (proxy) chromeArgs.push(`--proxy-server=${proxy}`);
    // 获取无核浏览器的本地路径
    const chromePath = BrowserClient.getChromiumPath();
    // 没有找到可用的浏览器执行路径
    if (!chromePath) {
      vscode.window.showErrorMessage('未安装chrome浏览器，或者未设置自定义浏览器路径');
    }
    // 如果是linux浏览器则追加额外参数
    if (os.platform() === 'linux') {
      chromeArgs.push('--no-sandbox');
    }
    // 获取插件上下文对象
    // https://chromium.googlesource.com/chromium/src/+/refs/heads/main/docs/user_data_dir.md
    const userDataDir = path.join(this.context.globalStorageUri.fsPath, 'playwrightUserData');
    // 直接删除加锁文件，避免浏览器唤起失败
    try {
      // TODO: 这里是替身文件，可以再看下怎么判断替身文件是否存在
      await fs.unlink(path.join(userDataDir, 'SingletonLock'));
    } catch {
      // do nothing
    }
    // 启动无核浏览器
    this.browser = await playwright.chromium.launchPersistentContext(userDataDir, {
      args: [...chromeArgs, '--lang=zh-CN,zh'],
      // TODO: 新的无头模式存在问题（动态更新viewport后screencast会出现尺寸异常问题），因此先屏蔽
      // headless: 'new', // 这个是官方新推荐增加的参数（https://developer.chrome.com/articles/new-headless/）
      // 这个设置项有可能导致page.goto变慢至4-5秒
      executablePath: chromePath,
      ignoreDefaultArgs: ['--mute-audio'],
      viewport: this.browserSettings.viewport,
      headless: this.browserSettings.headless,
    });

    this.page = await this.browser.newPage();
    //
  }
  async closeBrowser(): Promise<BrowserActionResult> {
    if (this.browser || this.page) {
      await this.browser?.close().catch(() => {});
      this.browser = undefined;
      this.page = undefined;
    }
    return {};
  }
  async doAction(action: (page: playwright.Page) => Promise<void>): Promise<BrowserActionResult> {
    if (!this.page) {
      throw new Error('浏览器未启动，可能是浏览器未使用use_browser工具关闭');
    }

    const logs: string[] = [];
    let lastLogTs = Date.now();

    const consoleListener = (msg: playwright.ConsoleMessage) => {
      if (msg.type() === 'log') {
        logs.push(msg.text());
      } else {
        logs.push(`[${msg.type()}] ${msg.text()}`);
      }
      lastLogTs = Date.now();
    };

    const errorListener = (err: Error) => {
      logs.push(`[Page Error] ${err.toString()}`);
      lastLogTs = Date.now();
    };

    // Add the listeners
    this.page.on('console', consoleListener);
    this.page.on('pageerror', errorListener);

    try {
      await action(this.page);
    } catch (err) {
      if (!(err instanceof playwright.errors.TimeoutError)) {
        logs.push(`[Error] ${err.toString()}`);
      }
    }

    // Wait for console inactivity, with a timeout
    await pWaitFor(() => Date.now() - lastLogTs >= 500, {
      timeout: 3_000,
      interval: 100,
    }).catch(() => {});

    const screenshotPath = path.join(this.context.globalStorageUri.fsPath, 'screenshot.png');

    await this.page.screenshot({
      path: screenshotPath,
      type: 'png',
    });

    const screenshotBase64 = (await fs.readFile(screenshotPath)).toString('base64');
    const screenshot = `data:image/png;base64,${screenshotBase64}`;

    if (!screenshotBase64) {
      throw new Error('Failed to take screenshot.');
    }

    // this.page.removeAllListeners() <- causes the page to crash!
    this.page.off('console', consoleListener);
    this.page.off('pageerror', errorListener);

    return {
      screenshot,
      logs: logs.join('\n'),
      currentUrl: this.page.url(),
      currentMousePosition: this.currentMousePosition,
    };
  }
  /**
   * Navigate to a URL with standard loading options
   */
  private async navigatePageToUrl(page: playwright.Page, url: string): Promise<void> {
    await page.goto(url, { timeout: 7_000, waitUntil: 'networkidle' });
    await this.waitTillHTMLStable(page);
  }

  /**
   * Creates a new tab and navigates to the specified URL
   */
  private async createNewTab(url: string): Promise<BrowserActionResult> {
    if (!this.browser) {
      throw new Error('Browser is not launched');
    }

    // Create a new page
    const newPage = await this.browser.newPage();

    // Set the new page as the active page
    this.page = newPage;

    // Navigate to the URL
    const result = await this.doAction(async (page) => {
      await this.navigatePageToUrl(page, url);
    });

    return result;
  }
  /**
   * Extract the root domain from a URL
   * e.g., http://localhost:3000/path -> localhost:3000
   * e.g., https://example.com/path -> example.com
   */
  private getRootDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      // Remove www. prefix if present
      return urlObj.host.replace(/^www\./, '');
    } catch (error) {
      // If URL parsing fails, return the original URL
      return url;
    }
  }
  async navigateToUrl(url: string): Promise<BrowserActionResult> {
    if (!this.browser) {
      throw new Error('Browser is not launched');
    }
    // Remove trailing slash for comparison
    const normalizedNewUrl = url.replace(/\/$/, '');

    // Extract the root domain from the URL
    const rootDomain = this.getRootDomain(normalizedNewUrl);

    // Get all current pages
    const pages = await this.browser.pages();

    // Try to find a page with the same root domain
    let existingPage: playwright.Page | undefined;

    for (const page of pages) {
      try {
        const pageUrl = page.url();
        if (pageUrl && this.getRootDomain(pageUrl) === rootDomain) {
          existingPage = page;
          break;
        }
      } catch (error) {
        // Skip pages that might have been closed or have errors
        console.log(`Error checking page URL: ${error}`);
        continue;
      }
    }

    if (existingPage) {
      // Tab with the same root domain exists, switch to it
      console.log(`Tab with domain ${rootDomain} already exists, switching to it`);

      // Update the active page
      this.page = existingPage;
      existingPage.bringToFront();

      // Navigate to the new URL if it's different]
      const currentUrl = existingPage.url().replace(/\/$/, ''); // Remove trailing / if present
      if (this.getRootDomain(currentUrl) === rootDomain && currentUrl !== normalizedNewUrl) {
        console.log(`Navigating to new URL: ${normalizedNewUrl}`);
        console.log(`Current URL: ${currentUrl}`);
        console.log(`Root domain: ${this.getRootDomain(currentUrl)}`);
        console.log(`New URL: ${normalizedNewUrl}`);
        // Navigate to the new URL
        return this.doAction(async (page) => {
          await this.navigatePageToUrl(page, normalizedNewUrl);
        });
      } else {
        console.log(`Tab with domain ${rootDomain} already exists, and URL is the same: ${normalizedNewUrl}`);
        // URL is the same, just reload the page to ensure it's up to date
        console.log(`Reloading page: ${normalizedNewUrl}`);
        console.log(`Current URL: ${currentUrl}`);
        console.log(`Root domain: ${this.getRootDomain(currentUrl)}`);
        console.log(`New URL: ${normalizedNewUrl}`);
        return this.doAction(async (page) => {
          await page.reload({ timeout: 7_000, waitUntil: 'networkidle' });
          await this.waitTillHTMLStable(page);
        });
      }
    } else {
      // No tab with this root domain exists, create a new one
      console.log(`No tab with domain ${rootDomain} exists, creating a new one`);
      return this.createNewTab(normalizedNewUrl);
    }
  }

  private async waitTillHTMLStable(page: playwright.Page, timeout = 5_000) {
    const checkDurationMsecs = 500; // 1000
    const maxChecks = timeout / checkDurationMsecs;
    let lastHTMLSize = 0;
    let checkCounts = 1;
    let countStableSizeIterations = 0;
    const minStableSizeIterations = 3;

    while (checkCounts++ <= maxChecks) {
      let html = await page.content();
      let currentHTMLSize = html.length;

      // let bodyHTMLSize = await page.evaluate(() => document.body.innerHTML.length)
      console.log('last: ', lastHTMLSize, ' <> curr: ', currentHTMLSize);

      if (lastHTMLSize !== 0 && currentHTMLSize === lastHTMLSize) {
        countStableSizeIterations++;
      } else {
        countStableSizeIterations = 0; //reset the counter
      }

      if (countStableSizeIterations >= minStableSizeIterations) {
        console.log('Page rendered fully...');
        break;
      }

      lastHTMLSize = currentHTMLSize;
      await delay(checkDurationMsecs);
    }
  }

  /**
   * Handles mouse interaction with network activity monitoring
   */
  private async handleMouseInteraction(
    page: playwright.Page,
    coordinate: string,
    action: (x: number, y: number) => Promise<void>
  ): Promise<void> {
    const [x, y] = coordinate.split(',').map(Number);

    // Set up network request monitoring
    let hasNetworkActivity = false;
    const requestListener = () => {
      hasNetworkActivity = true;
    };
    page.on('request', requestListener);

    // Perform the mouse action
    await action(x, y);
    this.currentMousePosition = coordinate;

    // Small delay to check if action triggered any network activity
    await delay(100);

    if (hasNetworkActivity) {
      // If we detected network activity, wait for navigation/loading
      await page
        .waitForNavigation({
          waitUntil: 'networkidle',
          timeout: 7000,
        })
        .catch(() => {});
      await this.waitTillHTMLStable(page);
    }

    // Clean up listener
    page.off('request', requestListener);
  }

  async click(coordinate: string): Promise<BrowserActionResult> {
    const [x, y] = coordinate.split(',').map(Number);
    return this.doAction(async (page) => {
      // Set up network request monitoring
      let hasNetworkActivity = false;
      const requestListener = () => {
        hasNetworkActivity = true;
      };
      page.on('request', requestListener);

      // Perform the click
      await page.mouse.click(x, y);
      this.currentMousePosition = coordinate;

      // Small delay to check if click triggered any network activity
      await delay(100);

      if (hasNetworkActivity) {
        // If we detected network activity, wait for navigation/loading
        await page
          .waitForNavigation({
            waitUntil: 'commit',
            timeout: 7000,
          })
          .catch(() => {});
        await this.waitTillHTMLStable(page);
      }

      // Clean up listener
      page.off('request', requestListener);
    });
  }

  async type(text: string): Promise<BrowserActionResult> {
    return this.doAction(async (page) => {
      await page.keyboard.type(text);
    });
  }

  async press(key: string): Promise<BrowserActionResult> {
    return this.doAction(async (page) => {
      await page.keyboard.press(key);
    });
  }

  async scrollDown(): Promise<BrowserActionResult> {
    return this.doAction(async (page) => {
      await page.evaluate(() => {
        window.scrollBy({
          top: 600,
          behavior: 'auto',
        });
      });
      await delay(300);
    });
  }

  async scrollUp(): Promise<BrowserActionResult> {
    return this.doAction(async (page) => {
      await page.evaluate(() => {
        window.scrollBy({
          top: -600,
          behavior: 'auto',
        });
      });
      await delay(300);
    });
  }

  /**
   * 将指定URL的网页内容转换为Markdown格式
   * @param url 要转换的网页URL
   * @returns 转换后的Markdown字符串
   * @throws 如果浏览器未初始化
   */
  async urlToMarkdown(url: string): Promise<string> {
    if (!this.browser || !this.page) {
      throw new Error('Browser not initialized');
    }
    /*
      - networkidle2 is equivalent to playwright's networkidle where it waits until there are no more than 2 network connections for at least 500 ms.
      - domcontentloaded is when the basic DOM is loaded
      this should be sufficient for most doc sites
      */
    await this.page.goto(url, {
      timeout: 10_000,
      waitUntil: 'commit',
    });
    const content = await this.page.content();

    // use cheerio to parse and clean up the HTML
    const $ = cheerio.load(content);
    $('script, style, nav, footer, header').remove();

    // convert cleaned HTML to markdown
    const turndownService = new TurndownService();
    const markdown = turndownService.turndown($.html());

    return markdown;
  }
  async hover(coordinate: string): Promise<BrowserActionResult> {
    return this.doAction(async (page) => {
      await this.handleMouseInteraction(page, coordinate, async (x, y) => {
        await page.mouse.move(x, y);
        // Small delay to allow any hover effects to appear
        await delay(300);
      });
    });
  }

  async resize(size: string): Promise<BrowserActionResult> {
    return this.doAction(async (page) => {
      const [width, height] = size.split(',').map(Number);
      const session = await page.context().newCDPSession(page);
      await page.setViewportSize({ width, height });
      const { windowId } = await session.send('Browser.getWindowForTarget');
      await session.send('Browser.setWindowBounds', {
        bounds: { width, height },
        windowId,
      });
    });
  }
}

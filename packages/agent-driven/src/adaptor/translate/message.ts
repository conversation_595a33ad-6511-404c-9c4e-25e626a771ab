import { ToolUseName } from '../../core/assistant-message';

export const JoyCoderRestoreMessageMap = {
  'Task messages have been restored to the checkpoint': '任务消息已回滚',
  'Workspace files have been restored to the checkpoint': '项目文件已回滚',
  'Task and workspace have been restored to the checkpoint': '代码文件及会话消息已回滚',
  'No changes found': '暂无更改',
  'Failed to retrieve diff set: ': '获取差异集失败：',
  'New changes': '新变更',
  'Changes since snapshot': '自快照以来的变更',
  'JoyCoder instance aborted': 'JoyCoder请求已中止',
  'Current ask promise was ignored 1': '当前请求已被忽略',
  'Current ask promise was ignored 2': '当前请求已被忽略',
  'Current ask promise was ignored': '当前请求已被忽略',
  'Task was interrupted before this tool call could be completed.': '任务在这个工具调用完成之前被中断了。',
  'Unexpected: Last message is not a user or assistant message': '异常：最后一条消息既不是用户消息也不是助手消息',
  'Unexpected: No existing API conversation history': '异常：没有现有的API对话历史',
  Error: '错误',
  'Unknown error': '未知错误：',
  Loading: '加载中',
  'writing file': '写入文件',
  'reading file': '读取文件',
  'listing files': '列出文件',
  'parsing source code definitions': '解析源代码定义',
  'searching files': '查询文件',
  'executing browser action': '执行浏览器操作',
  'executing command': '执行命令',
  'executing MCP tool': '执行MCP工具',
  'accessing MCP resource': '访问MCP资源',
  'asking question': '询问问题',
  'responding to inquiry': '回复询问',
  'attempting completion': '尝试执行完成操作',
  'This may indicate a failure in his thought process or inability to use a tool properly, which can be mitigated with some user guidance (e.g. "Try breaking down the task into smaller steps").': `这可能表明他的思维过程存在失败或无法正确使用工具，可以通过一些用户指导（例如“尝试将任务分解为更小的步骤”）来缓解。`,
  "JoyCoder uses complex prompts and iterative task execution that may be challenging for less capable models. For best results, it's recommended to use Claude 3.5 Sonnet for its advanced agentic coding capabilities.":
    'JoyCoder使用复杂的提示和迭代式任务执行，这对能力较弱的模型来说可能具有挑战性。为了获得最佳结果，建议使用Claude 4 Sonnet，因为它具有先进的代理编码能力。',
  "Unexpected API Response: The language model did not provide any assistant messages. This may indicate an issue with the API or the model's output.":
    '意外的API响应：语言模型没有提供任何助手消息。这可能表明API或模型输出存在问题。',
};
export const sayMissingParamError = (toolName: ToolUseName, paramName: string, relPath?: string) => {
  return `JoyCoder 尝试使用 ${toolName}${
    relPath ? ` 处理 '${relPath.toPosix()}'` : ''
  }，但缺少必需参数 '${paramName}' 的值。正在重试...`;
};

export const JoyCoderCheckpointsMessageMap = {
  'Checkpoints can only be used in the original workspace: ': '仅在原始工作区中可以使用检查点：',
  'Global storage uri is invalid': '全局存储URI无效',
  'Failed to delete task branch:': '删除任务分支失败：',
  'Failed to create checkpoint:': '创建检查点失败：',
  'Global storage path is required to create a checkpoint tracker': '创建检查点跟踪器需要全局存储路径错误',
  'Git must be installed to use checkpoints.': '要使用检查点，必须安装Git。',
  'No workspace detected. Please open JoyCoder in a workspace to use checkpoints.':
    '未检测到工作区。请在打开JoyCoder的工作区中使用检查点。',
  'Cannot use checkpoints in home directory': '无法在主目录中使用检查点',
  'Cannot use checkpoints in Desktop directory': '无法在桌面目录中使用检查点',
  'Cannot use checkpoints in Documents directory': '无法在文档目录中使用检查点',
  'Cannot use checkpoints in Downloads directory': '无法在下载目录中使用检查点',
  'Working directory path cannot be empty': '工作目录路径不能为空',
};
export const JoyCoderEditorMessageMap = {
  'Required values not set': '缺少必需值',
  'User closed text editor, unable to edit file...': '用户关闭了文本编辑器，无法编辑文件...',
  'No file path set': '未设置文件路径',
  'Failed to open diff editor, please try again...': '打开差异编辑器失败，请重试...',
  'Invalid data URI format': '无效的数据URI格式',
  'Could not open file!': `无法打开文件！`,
  'Message is required': '通知消息不可为空',
};
export const JoyCoderMCPMessageMap = {
  'Provider not available': 'Provider不可用',
  'Invalid MCP settings format. Please ensure your settings follow the correct JSON format.':
    'MCP 设置格式无效。请确保您的设置遵循正确的 JSON 格式。',
  'Updating MCP servers...': '正在更新 MCP 服务器...',
  'MCP servers updated': 'MCP 服务器已更新',
  'Failed to update autoApprove settings': '更新自动批准设置失败',
  'No results found': '未找到结果',
  'Could not find ripgrep binary': '找不到 ripgrep 二进制文件',
  'ripgrep process error': 'ripgrep 进程错误',
  'Error parsing ripgrep output': '解析 ripgrep 输出时出错',
  'No workspace folder open': '未打开工作区文件夹',
};

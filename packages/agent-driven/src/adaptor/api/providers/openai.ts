import { sm2 } from 'sm-crypto';
import { Anthropic } from '@anthropic-ai/sdk';
import OpenAI from 'openai';
import { ApiHandlerOptions, ModelInfo, openAiModelInfoSaneDefaults } from '../../../shared/api';
import { ApiHandler } from '../index';
import { convertToOpenAiMessages } from '../transform/openai-format';
import { ApiStream } from '../transform/stream';
import { getExtParams, checkLogin, getExtHeaders } from '@joycoder/agent-common/src/vscode/index';
import { getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { getJdhLoginInfo, GlobalState, isIDE, getBaseUrl } from '@joycoder/shared';

const errorMessage = (errorJson: any) => {
  let modifiedError = '';
  if (
    errorJson.error?.message.includes(`This model's maximum context length`) ||
    errorJson.error?.message.includes(`is longer than the model's context length`) || //DeepSeekR1
    errorJson.error?.message.includes(`Input is too long for requested model`) ||
    errorJson.error?.message.includes("Requested token count exceeds the model's maximum context length") || //DeepSeekV3
    errorJson.error?.code === 'context_length_exceeded'
  ) {
    modifiedError = '上下文输出超长，请减少输入长度后重试！';
    // modifiedError = '上下文输出超长。点击重试以截断对话并再次尝试。';
  } else if (errorJson.error?.message.includes('Model is getting throttled.')) {
    modifiedError = '模型正在被限流，请稍后重试！';
  } else if (errorJson.error?.code == 1033 || errorJson.error?.message.includes(`敏感词`)) {
    modifiedError = `当前输入中包含邮箱、手机号、密码等敏感信息，请检查后重试！${errorJson.error?.message ?? ''}`;
  } else if (errorJson.error?.code == 1050) {
    modifiedError = `${errorJson.error?.message ?? ''}`;
  } else if (errorJson.error?.code == 13 || errorJson.error?.message.includes(`未登录`)) {
    modifiedError = '未登录，请点击<a class="joycoder-login-btn">去浏览器授权</a>登录，登录后重试！';
  } else if (isIDE() && errorJson?.code == 401) {
    modifiedError = '未登录，请点击<a class="joycoder-login-btn">去浏览器授权</a>登录，登录后重试！';
  } else {
    modifiedError = '异常: 模型请求失败，请切换模型重试！';
  }
  return modifiedError;
};
// 自定义 fetch 函数
const customFetch: typeof fetch = async (input, init) => {
  let response;
  if (isIDE()) {
    const extHeaders = getExtHeaders().headers;
    const headers: Record<string, string> = {
      'Content-Type': extHeaders['Content-Type'],
    };

    if ('ptKey' in extHeaders && extHeaders.ptKey) {
      headers.ptKey = extHeaders.ptKey;
    }

    const finalHeaders = {
      ...headers,
    };

    response = await fetch(input, {
      method: 'POST',
      headers: finalHeaders,
      body: init?.body,
    });
  } else {
    response = await fetch(input, init);
  }

  // 检查是否为流式响应
  if (response.headers.get('content-type')?.includes('text/event-stream')) {
    const reader = response.body!.getReader();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const text = new TextDecoder().decode(value);

            if (text.includes('error')) {
              try {
                if (text.includes('context_length_exceeded')) {
                  const errorJson = {
                    error: {
                      message: '错误: 上下文输出超长，请减少输入长度后重试！',
                    },
                  };
                  const modifiedData = JSON.stringify(errorJson);
                  controller.enqueue(new TextEncoder().encode(`data: ${modifiedData}\n\n`));
                } else {
                  // 统一处理所有错误，包括1050等，使用errorMessage函数
                  const lines = text.split('\n');
                  for (const line of lines) {
                    if (line.startsWith('data: ') || /^data:/.test(line) || /^data:\s*/.test(line)) {
                      const parsed = JSON.parse(line.slice(5));
                      if (parsed.error) {
                        let modifiedError = errorMessage(parsed);
                        parsed.error.message = modifiedError;
                        const modifiedData = JSON.stringify(parsed);
                        controller.enqueue(new TextEncoder().encode(`data: ${modifiedData}\n\n`));
                        return;
                      }
                    } else if (line.trim() && !line.startsWith('data:')) {
                      // 处理直接的JSON错误响应（如1050错误）
                      try {
                        const parsed = JSON.parse(line);
                        if (parsed.error) {
                          let modifiedError = errorMessage(parsed);
                          parsed.error.message = modifiedError;
                          const modifiedData = JSON.stringify(parsed);
                          controller.enqueue(new TextEncoder().encode(`data: ${modifiedData}\n\n`));
                          return;
                        }
                      } catch (parseError) {
                        // 如果不是JSON，继续处理下一行
                      }
                    }
                    controller.enqueue(new TextEncoder().encode(line + '\n'));
                  }
                }
              } catch (e) {
                // 如果不是有效的 JSON 或没有错误，就继续处理
                controller.enqueue(value);
              }
            } else if (isIDE() && text.includes('401') && text.includes('账号未登录')) {
              try {
                let errorJson = typeof text == 'string' ? JSON.parse(text) : text;
                if (errorJson.code === 401) {
                  let modifiedError = errorMessage(errorJson);
                  errorJson.error = {
                    code: '13',
                    message: modifiedError,
                  };
                  const modifiedData = JSON.stringify(errorJson);
                  controller.enqueue(new TextEncoder().encode(`data: ${modifiedData}\n\n`));
                  return;
                }
              } catch {}
            } else {
              controller.enqueue(value);
            }
          }
        } catch (error) {
          console.warn('%c [ error ]-55', 'font-size:13px; background:pink; color:#bf2c9f;', error);
          controller.error(error);
        } finally {
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: response.headers,
      status: response.status,
      statusText: response.statusText,
    });
  } else if (!response.ok) {
    // 对于非流式响应，我们检查响应内容是否包含错误
    const errorText = response.headers.get('content-type')?.includes('application/json')
      ? await response.json()
      : await response.text();
    try {
      const errorJson = typeof errorText == 'string' ? JSON.parse(errorText) : errorText;
      if (errorJson.error) {
        let modifiedError = errorMessage(errorJson);
        errorJson.error.message = modifiedError;
        return new Response(JSON.stringify(errorJson), {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
        });
      }
    } catch (e) {
      // 如果不是有效的 JSON，则返回原始错误文本
      return new Response(e.message || '错误: 模型请求失败，请切换模型重试！', {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      });
    }
  }

  return response;
};

export class OpenAiHandler implements ApiHandler {
  private options: ApiHandlerOptions;
  private client: OpenAI;

  constructor(options: ApiHandlerOptions) {
    checkLogin();
    let BASE_URL = 'http://jdhgpt.jd.com/open/v1/';
    if (isIDE()) {
      BASE_URL = getBaseUrl() + '/api/saas/openai/v1/';
    }
    this.options = options;
    this.client = new OpenAI({
      baseURL: BASE_URL,
      apiKey: 'empty',
      fetch: customFetch,
    });
  }

  async *createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream {
    const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
      { role: 'system', content: systemPrompt },
      ...convertToOpenAiMessages(messages),
    ];
    const label = GlobalState.get('openAiModelId');
    const modelConfig = getChatModelAndConfig(label);
    // try {
    let reqBody: any = {
      model: modelConfig.chatApiModel ?? '',
      max_tokens: modelConfig.chatApiModel.toLowerCase().includes('claude-3-5')
        ? 8192
        : modelConfig.chatApiModel.toLowerCase().includes('claude')
        ? 16000
        : 4096,
      messages: openAiMessages,
      temperature: modelConfig.temperature ?? 0,
      stream: modelConfig.stream ?? true,
    };
    if (isIDE()) {
      const encryptData = sm2.doEncrypt(JSON.stringify(reqBody), getJdhLoginInfo()?.pk ?? '', 1);
      reqBody = {
        ...reqBody,
        enData: encryptData,
      };
    } else {
      reqBody = {
        ...reqBody,
        ...getExtParams()?.jdhLoginParams,
      };
    }

    const stream = await this.client.chat.completions.create(reqBody as any);

    //@ts-ignore
    for await (const chunk of stream) {
      try {
        const delta = chunk?.choices?.[0]?.delta;
        if (delta?.content) {
          yield {
            type: 'text',
            text: delta.content,
            conversationId: chunk?.id || '',
          };
        }
        if (delta && 'reasoning_content' in delta && delta.reasoning_content) {
          yield {
            type: 'reasoning',
            reasoning: (delta.reasoning_content as string | undefined) || '',
            reasoning_content: (delta.reasoning_content as string | undefined) || '',
          };
        }
        if (chunk.usage) {
          yield {
            type: 'usage',
            inputTokens: chunk.usage.prompt_tokens || 0,
            outputTokens: chunk.usage.completion_tokens || 0,
            conversationId: chunk?.id || '',
          };
        }
      } catch (parseError) {
        console.error('Error processing chunk:', parseError);
        console.error('Problematic chunk:', chunk);
      }
    }
    // } catch (error) {
    //   if (error.code == 13) {
    //     throw new Error(error?.message ?? '模型请求失败，请稍后重试或者切换模型重试！');
    //   }
    //   console.error('%c [ createMessage->error ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    //   throw new Error(error?.message ?? '模型请求失败，请稍后重试或者切换模型重试！');
    // }
  }

  getModel(): { id: string; info: ModelInfo } {
    return {
      id: this.options.openAiModelId ?? '',
      info: openAiModelInfoSaneDefaults,
    };
  }
  async completePrompt(prompt: string): Promise<string> {
    try {
      const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [{ role: 'user', content: prompt }];
      const label = GlobalState.get('openAiModelId');
      const modelConfig = getChatModelAndConfig(label);
      let reqBody: any = {
        model: modelConfig.chatApiModel ?? '',
        max_tokens: modelConfig.chatApiModel.includes('claude-3-5-sonnet') ? 8192 : 4096,
        messages: openAiMessages,
        temperature: modelConfig.temperature ?? 0,
        stream: false,
      };
      if (isIDE()) {
        const encryptData = sm2.doEncrypt(JSON.stringify(reqBody), getJdhLoginInfo()?.pk ?? '', 1);
        reqBody = {
          ...reqBody,
          enData: encryptData,
        };
      } else {
        reqBody = {
          ...reqBody,
          ...getExtParams()?.jdhLoginParams,
        };
      }
      const response = await this.client.chat.completions.create(reqBody);
      return response.choices[0]?.message.content || '';
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`接口错误: ${error.message}`);
      }
      throw error;
    }
  }
}

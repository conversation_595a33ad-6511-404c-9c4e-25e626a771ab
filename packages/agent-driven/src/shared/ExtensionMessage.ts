// type that represents json data that is sent from extension to webview, called ExtensionMessage and has 'type' enum which can be 'plusButtonClicked' or 'settingsButtonClicked' or 'hello'

import { ApiConfiguration, ModelInfo } from './api';
import { AutoApprovalSettings } from './AutoApprovalSettings';
import { BrowserSettings } from './BrowserSettings';
import { ChatSettings } from './ChatSettings';
import { HistoryItem } from './HistoryItem';
import { McpServer } from './mcp';
import { GitCommit } from '../utils/git';
import { CodebaseIndexingStatus } from '@joycoder/shared/src/types/codebase';
import { ToolProgressStatus } from '../schemas';
import { Mode } from './modes';
import { ApiConfigMeta, CustomModePrompts, ModeConfig } from '../../web-agent/src/utils/modes';
import { UserContent } from '../core/Joycoder';

export type { ApiConfigMeta, ToolProgressStatus };
interface ChatModelConfig {
  label: string;
  description: string;
  avatar: string;
  chatApiModel: string;
  chatApiUrl?: string;
  maxTotalTokens: number;
  bizId?: string;
  bizToken?: string;
  systemMessage?: string;
  respMaxTokens?: number;
  // 标记类
  hidden?: boolean;
  prefer?: boolean;
  context?: boolean;
  features?: string[];
  temperature?: number;
  stream?: boolean;
  modelFunctionType?: string;
}

interface ISelectionInfo {
  fileName: string;
  filePath?: string;
  startLine: number;
  startCharacter: number;
  endLine: number;
  endCharacter: number;
  selection: any;
  selectedText: string;
}

export interface ISelection {
  selectText?: string;
  selectInfo?: ISelectionInfo | null;
}
// webview will hold state
export interface ExtensionMessage {
  type:
    | 'action'
    | 'updateGPTConfig'
    | 'totalTasksSize'
    | 'updateGPTModel'
    | 'state'
    | 'selectedImages'
    | 'ollamaModels'
    | 'lmStudioModels'
    | 'theme'
    | 'workspaceUpdated'
    | 'invoke'
    | 'updateLoginStatus'
    | 'partialMessage'
    | 'openRouterModels'
    | 'openAiModels'
    | 'mcpServers'
    | 'relinquishControl'
    | 'vsCodeLmModels'
    | 'requestVsCodeLmModels'
    | 'emailSubscribed'
    | 'updatedIdeName'
    | 'commitSearchResults'
    | 'updatePluginType'
    | 'workspaceUpdating'
    | 'updateSelectionContext'
    | 'updatePlatformInfo'
    | 'listApiConfig'
    | 'enhancedPrompt'
    | 'codebase'
    | 'updateUserPrompt'
    | 'updateCoderMode'
    | 'modeUpdate';
  text?: string;
  ideAppName?: string;
  action?:
    | 'chatButtonClicked'
    | 'mcpButtonClicked'
    | 'settingsButtonClicked'
    | 'historyButtonClicked'
    | 'didBecomeVisible'
    | 'accountLoginClicked'
    | 'updatedIdeName'
    | 'openInNewChat'
    | 'accountLogoutClicked';
  invoke?: 'sendMessage' | 'primaryButtonClick' | 'secondaryButtonClick';
  state?: ExtensionState;
  images?: string[];
  ollamaModels?: string[];
  lmStudioModels?: string[];
  vsCodeLmModels?: { vendor?: string; family?: string; version?: string; id?: string }[];
  filePaths?: string[];
  ruleFiles?: string[]; // 添加 ruleFiles 属性，用于存储 .joycoder/rules/*.mdc 文件列表
  modelConfig?: Record<string, any>;
  modelList?: ChatModelConfig[];
  partialMessage?: JoyCoderMessage;
  openRouterModels?: Record<string, ModelInfo>;
  openAiModels?: string[];
  mcpServers?: McpServer[];
  data?: Record<string, any>;
  commits?: GitCommit[];
  updateStatus?: string;
  totalTasksSize?: number | null;
  addRemoteServerResult?: {
    success: boolean;
    serverName: string;
    error?: string;
  };
  selection?: ISelection;
  codebaseIndexingStatus?: CodebaseIndexingStatus;
  listApiConfig?: any;
  mode?: string; // 添加mode字段，用于updateCoderMode消息
  chatSettings?: ChatSettings; // 添加chatSettings字段，用于modeUpdate消息
  promptList?: any;
}

export type Platform = 'aix' | 'darwin' | 'freebsd' | 'linux' | 'openbsd' | 'sunos' | 'win32' | 'unknown';

export const DEFAULT_PLATFORM = 'unknown';

export interface ExtensionState {
  version: string;
  apiConfiguration?: ApiConfiguration;
  customInstructions?: string;
  uriScheme?: string;
  currentTaskItem?: HistoryItem;
  checkpointTrackerErrorMessage?: string;
  joycoderMessages: JoyCoderMessage[];
  taskHistory: HistoryItem[];
  shouldShowAnnouncement: boolean;
  autoApprovalSettings: AutoApprovalSettings;
  browserSettings: BrowserSettings;
  chatSettings: ChatSettings;
  isLoggedIn: boolean;
  platform: Platform;
  isRemoteEnvironment?: boolean;
  userInfo?: {
    displayName: string | null;
    email: string | null;
    photoURL: string | null;
  };
  updateStatus?: string;
  mode: Mode;
  customModes: ModeConfig[];
  customModePrompts?: CustomModePrompts;
  listApiConfigMeta?: any;
  customSupportPrompts?: any;
}

export interface JoyCoderMessage {
  ts: number;
  type: 'ask' | 'say';
  ask?: JoyCoderAsk | 'command';
  say?: JoyCoderSay;
  text?: string;
  reasoning?: string;
  reasoning_content?: string;
  images?: string[];
  partial?: boolean;
  lastCheckpointHash?: string;
  isCheckpointCheckedOut?: boolean;
  conversationHistoryIndex?: number;
  progressStatus?: ToolProgressStatus;
  conversationHistoryDeletedRange?: [number, number]; // for when conversation history is truncated for API requests
  modeInfo?: {
    agentId: string;
    name: string;
    avatar?: string;
  }; // Store mode information at message creation time
}
export interface JoyCoderPlanModeResponse {
  response: string;
  options?: string[];
  selected?: string;
}

export interface JoyCoderAskQuestion {
  question: string;
  options?: string[];
  selected?: string;
}
export interface ButtonText {
  primaryButtonText: string | undefined;
  secondaryButtonText: string | undefined;
}

export type JoyCoderAsk =
  | 'followup'
  | 'get_plan_info'
  | 'command'
  | 'command_output'
  | 'completion_result'
  | 'tool'
  | 'api_req_failed'
  | 'resume_task'
  | 'resume_completed_task'
  | 'mistake_limit_reached'
  | 'auto_approval_max_req_reached'
  | 'new_task_with_condense_context'
  | 'use_browser_launch'
  | 'use_web_search'
  | 'codebase_search'
  | 'use_mcp_server'
  | 'condense'
  | 'switchMode'
  | 'new_task_creation';

export type JoyCoderSay =
  | 'task'
  | 'error'
  | 'api_req_started'
  | 'api_req_finished'
  | 'text'
  | 'reasoning'
  | 'reasoning_content'
  | 'completion_result'
  | 'user_feedback'
  | 'user_feedback_diff'
  | 'api_req_retried'
  | 'command'
  | 'command_output'
  | 'tool'
  | 'shell_integration_warning'
  | 'use_browser_launch'
  | 'use_web_search'
  | 'codebase_search'
  | 'codebase_search_not_support'
  | 'use_browser'
  | 'use_browser_result'
  | 'mcp_server_request_started'
  | 'mcp_server_response'
  | 'use_mcp_server'
  | 'diff_error'
  | 'deleted_api_reqs'
  | 'joycoderignore_error'
  | 'get_mcp_instructions'
  | 'new_task_creation'
  | 'switchMode'
  | 'subtask_result'
  | 'checkpoint_created';
export const JoyCoderAsks = [
  'followup',
  'get_plan_info',
  'command',
  'command_output',
  'completion_result',
  'tool',
  'api_req_failed',
  'resume_task',
  'resume_completed_task',
  'mistake_limit_reached',
  'auto_approval_max_req_reached',
  'use_browser_launch',
  'use_web_search',
  'codebase_search',
  'use_mcp_server',
  'condense',
  'new_task_creation',
] as const;

export const JoyCoderSays = [
  'task',
  'error',
  'api_req_started',
  'api_req_finished',
  'text',
  'reasoning',
  'reasoning_content',
  'completion_result',
  'user_feedback',
  'user_feedback_diff',
  'api_req_retried',
  'command',
  'command_output',
  'tool',
  'shell_integration_warning',
  'use_browser_launch',
  'use_web_search',
  'codebase_search',
  'codebase_search_not_support',
  'use_browser',
  'use_browser_result',
  'mcp_server_request_started',
  'mcp_server_response',
  'use_mcp_server',
  'diff_error',
  'deleted_api_reqs',
  'joycoderignore_error',
  'get_mcp_instructions',
  'new_task_creation',
  'checkpoint_created',
] as const;

export interface JoyCoderSayTool {
  tool:
    | 'editedExistingFile'
    | 'searchAndReplace'
    | 'newFileCreated'
    | 'readFile'
    | 'listFilesTopLevel'
    | 'listFilesRecursive'
    | 'listCodeDefinitionNames'
    | 'searchFiles'
    | 'codebaseSearch'
    | 'appliedDiff'
    | 'insertContent'
    | 'fetchInstructions'
    | 'finishTask'
    | 'webSearch';
  path?: string;
  diff?: string;
  content?: string;
  search?: string;
  replace?: string;
  regex?: string;
  filePattern?: string;
  query?: string;
  files?: string[];
  lineNumber?: number;
  startLine?: number;
  endLine?: number;
  useRegex?: boolean;
  ignoreCase?: boolean;
  isOutsideWorkspace?: boolean;
  mode?: string;
  reason?: string;
  conversationId?: string;
  userContent?: UserContent;
  taskId?: string;
  sessionId?: string;
}

export interface JoyCoderSayText {
  text: string;
  userContent?: UserContent;
  conversationId?: string;
  taskId?: string;
  sessionId?: string;
}

// must keep in sync with system prompt
export const browserActions = [
  'launch',
  'click',
  'hover',
  'type',
  'scroll_down',
  'scroll_up',
  'resize',
  'close',
] as const;
export type BrowserAction = (typeof browserActions)[number];

export interface JoyCoderSayBrowserAction {
  action: BrowserAction;
  coordinate?: string;
  text?: string;
}

export type BrowserActionResult = {
  screenshot?: string;
  logs?: string;
  currentUrl?: string;
  currentMousePosition?: string;
};

export interface JoyCoderAskUseMcpServer {
  serverName: string;
  type: 'use_mcp_tools' | 'get_mcp_resource';
  toolName?: string;
  arguments?: string;
  uri?: string;
}

export interface JoyCoderApiReqInfo {
  request?: string;
  tokensIn?: number;
  tokensOut?: number;
  cacheWrites?: number;
  cacheReads?: number;
  cost?: number;
  cancelReason?: JoyCoderApiReqCancelReason;
  streamingFailedMessage?: string;
  retryStatus?: {
    attempt: number;
    maxAttempts: number;
    delaySec: number;
    errorSnippet?: string;
  };
}

export type JoyCoderApiReqCancelReason = 'streaming_failed' | 'user_cancelled' | 'retries_exhausted';

export const COMPLETION_RESULT_CHANGES_FLAG = 'HAS_CHANGES';

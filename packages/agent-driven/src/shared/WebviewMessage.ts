import { IActionCustomReportParam, IActionReportParam } from '@joycoder/shared';
import { ApiConfiguration } from './api';
import { AutoApprovalSettings } from './AutoApprovalSettings';
import { BrowserSettings } from './BrowserSettings';
import { ChatContent, ChatSettings } from './ChatSettings';
import { Mode, PromptComponent } from './modes';

export type PromptMode = Mode | 'enhance';

export interface WebviewMessage {
  type:
    | 'apiConfiguration'
    | 'customInstructions'
    | 'chatgpt-set-model'
    | 'joycoder-set-mode'
    | 'chatgpt-get-model'
    | 'chat-get-system'
    | 'chatgpt-get-model-list'
    | 'webviewDidLaunch'
    | 'newTask'
    | 'askResponse'
    | 'terminalOperation'
    | 'clearTask'
    | 'didShowAnnouncement'
    | 'selectImages'
    | 'exportCurrentTask'
    | 'showTaskWithId'
    | 'deleteTaskWithId'
    | 'exportTaskWithId'
    | 'resetState'
    | 'requestOllamaModels'
    | 'requestLmStudioModels'
    | 'openImage'
    | 'openFile'
    | 'openMention'
    | 'cancelTask'
    | 'searchCommits'
    | 'refreshOpenRouterModels'
    | 'refreshOpenAiModels'
    | 'openMcpSettings'
    | 'openProjectMcpSettings'
    | 'restartMcpServer'
    | 'autoApprovalSettings'
    | 'browserSettings'
    | 'chatSettings'
    | 'checkpointDiff'
    | 'checkpointRestore'
    | 'taskCompletionViewChanges'
    | 'openExtensionSettings'
    | 'requestVsCodeLmModels'
    | 'toggleToolAutoApprove'
    | 'toggleMcpServer'
    | 'getLatestState'
    | 'accountLoginClicked'
    | 'accountLogoutClicked'
    | 'subscribeEmail'
    | 'queryIdeName'
    | 'openInNewTab'
    | 'CHECK_LOGIN_STATUS'
    | 'CHECK_PLUGIN_TYPE'
    | 'updateMcpTimeout'
    | 'JUMP_LOGIN'
    | 'optionsResponse'
    | 'clearAllTaskHistory'
    | 'togglePlanActMode'
    | 'condense'
    | 'updateServerConnections'
    | 'updatePrompt'
    | 'updateCustomMode'
    | 'mode'
    | 'updateSupportPrompt'
    | 'resetSupportPrompt'
    | 'copySystemPrompt'
    | 'enhancePrompt'
    | 'getSystemPrompt'
    | 'deleteCustomMode'
    | 'openCustomModesSettings'
    | 'openSettings'
    | 'chatgpt-webview-report';
  text?: string;
  disabled?: boolean;
  askResponse?: JoyCoderAskResponse;
  apiConfiguration?: ApiConfiguration;
  images?: string[];
  bool?: boolean;
  number?: number;
  autoApprovalSettings?: AutoApprovalSettings;
  browserSettings?: BrowserSettings;
  chatSettings?: ChatSettings;
  chatContent?: ChatContent;
  // For toggleToolAutoApprove
  serverName?: string;
  toolName?: string;
  autoApprove?: boolean;
  model?: string;
  promptMode?: PromptMode;
  customPrompt?: PromptComponent;
  timeout?: number;
  mcpId?: string;
  agentId?: string;
  modeConfig?: any;
  mode?: any;
  values?: any;
  terminalOperation?: 'continue' | 'abort';
  // coderSettings?: CoderSettings;
  reportData?: IActionCustomReportParam;
}

export type JoyCoderAskResponse = 'yesButtonClicked' | 'noButtonClicked' | 'messageResponse';

export type JoyCoderCheckpointRestore = 'task' | 'workspace' | 'taskAndWorkspace';

export function getSwitchModeDescription(): string {
  return `## switch_mode
Description: Switch to a different operational mode when the current mode cannot fulfill the user's request. Use this tool when you need capabilities that are specific to another mode (e.g., switch to "code" mode for making code modifications, "architect" mode for system design, etc.). Mode switching requires user approval.

Parameters:
- mode_slug: (required) The target mode identifier. Valid values include "code", "ask", "architect", and other available mode identifiers.
- reason: (optional) Brief explanation of why the mode switch is necessary and what will be accomplished in the target mode.

Usage:
<switch_mode>
<mode_slug>Mode identifier here</mode_slug>
<reason>Reason for switching here</reason>
</switch_mode>

Example: Requesting to switch to code mode
<switch_mode>
<mode_slug>code</mode_slug>
<reason>Need to make code changes</reason>
</switch_mode>`;
}

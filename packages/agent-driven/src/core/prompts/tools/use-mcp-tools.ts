import { ToolArgs } from './types';

export function getUseMcpToolsDescription(args: ToolArgs): string {
  return `## use_mcp_tools
Description: Execute a specific tool from a connected MCP (Model Context Protocol) server. This function allows you to interact with external services and capabilities through MCP servers. You must specify the exact server name, tool name, and provide properly formatted arguments that match the tool's schema requirements.

Parameters:
- server_name: (required, string) The exact name identifier of the MCP server that hosts the desired tool. This must match a currently connected server.
- tool_name: (required, string) The exact name of the specific tool to execute within the specified server. Tool names are case-sensitive.
- arguments: (required, JSON object) A properly formatted JSON object containing all required parameters and any optional parameters for the tool. The structure must exactly match the tool's defined input schema. Empty object {} should be used if no parameters are required.

Usage:
<use_mcp_tools>
<server_name>server name here</server_name>
<tool_name>tool name here</tool_name>
<arguments>
{
  "param1": "value1",
  "param2": "value2"
}
</arguments>
</use_mcp_tools>

Important Notes:
- Always verify server and tool names are correct before execution
- Ensure arguments JSON is valid and matches the expected schema
- Required parameters must be included; optional parameters may be omitted
- String values should be properly quoted in the JSON arguments
- Numeric and boolean values should not be quoted unless the schema specifically requires string format`;
}

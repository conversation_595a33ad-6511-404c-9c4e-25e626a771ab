import { ToolArgs } from './types';

export function getAttemptTaskDoneDescription(args: ToolArgs): string {
  return `
## attempt_task_done
Description: Use this tool to present the final result of your work to the user or to directly answer simple questions. For complex tasks, use this ONLY after you have received explicit confirmation from the user that all previous tool uses were successful. This tool marks the completion of the entire task workflow or provides a direct answer to a simple question.

CRITICAL SAFETY REQUIREMENT: Before using this tool for complex tasks, you MUST verify in <thinking></thinking> tags that:
1. You have used other tools in this session, AND
2. The user has explicitly confirmed that those tool uses were successful (not failed, not pending)
3. If either condition is not met for complex tasks, DO NOT use this tool as it will cause code corruption and system failure

For simple questions that can be answered directly without using other tools, you may use this tool immediately without the above verification.

The tool serves three purposes:
- Present a comprehensive summary of what was accomplished for complex tasks
- Optionally provide a demonstration command to showcase the working result for complex tasks
- Directly answer simple questions without using other tools

Parameters:
- result: (required) For complex tasks, a complete, self-contained description of what was accomplished. For simple questions, the direct answer to the question. This should be definitive and final - do not include questions, requests for feedback, or offers for additional help. Focus on what was delivered/created/completed or the answer to the question.
- command: (optional) A CLI command that demonstrates or opens the completed work. Use commands that launch, open, or display results (e.g., \`open index.html\`, \`npm start\`, \`python app.py\`). Do NOT use text-display commands like \`echo\`, \`cat\`, or \`ls\`. Ensure the command is appropriate for the user's operating system and will actually showcase the working result.

Usage:
<attempt_task_done>
<result>
Your comprehensive final result description here
</result>
<command>Command to demonstrate the working result (optional)</command>
</attempt_task_done>

Examples:
1. Completing a web development task:
<attempt_task_done>
<result>
I've successfully created a responsive portfolio website with navigation menu, hero section, project gallery, and contact form. The site includes modern CSS styling with hover effects and mobile responsiveness.
</result>
<command>open index.html</command>
</attempt_task_done>

2. Answering a simple question:
<attempt_task_done>
<result>
The capital of France is Paris.
</result>
</attempt_task_done>
`.trim();
}

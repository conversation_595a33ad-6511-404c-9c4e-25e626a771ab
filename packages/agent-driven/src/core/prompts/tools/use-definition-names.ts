import { ToolArgs } from './types';

export function getUseDefinitionNamesDescription(args: ToolArgs): string {
  return `## use_definition_names
Description: Extracts and lists the names of top-level definitions (classes, functions, methods, constants, interfaces, etc.) from source code files in the specified directory or file. This tool helps you quickly understand the codebase structure by revealing the main components and their organization without showing implementation details. Use this when you need to:
- Get an overview of available functions/classes in a codebase
- Understand the architectural structure of a project
- Find specific definitions before diving into implementation details
- Map out the main components and their relationships

Parameters:
- path: (required) The file or directory path (relative to the current working directory ${args.cwd}) to analyze. For files, extracts definitions from that specific file. For directories, extracts definitions from all source code files within that directory.

Usage:
<use_definition_names>
<path>Directory or file path here</path>
</use_definition_names>
`.trim();
}

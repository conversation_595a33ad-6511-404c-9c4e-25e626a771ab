import { ToolArgs } from './types';

export function getUseWriteFileDescription(args: ToolArgs): string {
  return `
## use_write_file

### Description
This tool is designed to write content to a file. It serves two primary purposes:
1. Creating new files
2. Completely rewriting existing files when intentionally required

If the specified file already exists, its content will be overwritten. If it doesn't exist, a new file will be created. The tool automatically creates any necessary directories in the file path.

### Parameters
- \`path\` (required): The relative path of the target file, based on the current workspace directory ${args.cwd}.
- \`content\` (required): The complete content to be written to the file.
  - For new files or full rewrites, provide the entire intended content without any omissions or truncations.
  - Include ALL parts of the file, even those that haven't been modified.
  - Do NOT include line numbers in the content; provide only the actual file content.
- \`line_count\` (required): The total number of lines in the file, including empty lines. Compute this based on the actual content being written.

### Usage
Use the following XML-like structure to invoke this tool:

<use_write_file>
<path>Specify the file path here</path>
<content>
Insert the complete file content here
</content>
<line_count>Total number of lines in the file</line_count>
</use_write_file>

### Example
Here's an example of using the use_write_file tool to create or update a JSON configuration file:

<use_write_file>
<path>config/frontend-settings.json</path>
<content>
{
  "apiEndpoint": "https://api.example.com",
  "theme": {
    "primaryColor": "#007bff",
    "secondaryColor": "#6c757d",
    "fontFamily": "Arial, sans-serif"
  },
  "features": {
    "darkMode": true,
    "notifications": true,
    "analytics": false
  },
  "version": "1.0.0"
}
</content>
<line_count>14</line_count>
</use_write_file>

### Important Notes
1. Always provide the complete file content, even for partial updates.
2. Ensure the line_count accurately reflects the total number of lines in the content.
3. Use relative paths based on the current workspace directory.
4. This tool will overwrite existing files, so use with caution.`;
}

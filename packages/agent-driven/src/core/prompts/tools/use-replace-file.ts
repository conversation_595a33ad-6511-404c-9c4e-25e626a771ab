import { ToolArgs } from './types';

export function getUseReplaceFileDescription(args: ToolArgs, isSearchReplace?: boolean): string {
  if (isSearchReplace) {
    return `## use_replace_file
Description: Find and replace text strings or regex patterns within a file. This tool performs targeted replacements across multiple locations and shows a diff preview before applying changes. Ideal for code refactoring, content updates, and batch text modifications.

Required Parameters:
- path: File path relative to current workspace directory (${args.cwd.toPosix()})
- search: Text string or regex pattern to find
- replace: Replacement text (supports regex capture groups when use_regex=true)

Optional Parameters:
- start_line: Start line number for range-limited replacement (1-based, inclusive)
- end_line: End line number for range-limited replacement (1-based, inclusive)
- use_regex: Enable regex pattern matching ("true" or "false", default: "false")
- ignore_case: Enable case-insensitive matching ("true" or "false", default: "true")

Important Notes:
- **For files over 300 lines**: Use start_line/end_line parameters to target specific sections instead of replacing entire code blocks
- When use_regex="true": search parameter is treated as a regular expression with full regex syntax support
- When ignore_case="true": matching ignores letter case in both literal and regex modes
- Replacement text supports regex capture groups (\$1, \$2, etc.) when use_regex="true"
- Tool shows diff preview before applying changes for verification

Usage:
<use_replace_file>
<path>File path here</path>
<search>Text or pattern to search for</search>
<replace>Text to replace matches with</replace>
<start_line>Starting line number (optional)</start_line>
<end_line>Ending line number (optional)</end_line>
<use_regex>true/false (optional, default: false)</use_regex>
<ignore_case>true/false (optional, default: true)</ignore_case>
</use_replace_file>

Examples:

1. Simple text replacement:
<use_replace_file>
<path>example.ts</path>
<search>oldText</search>
<replace>newText</replace>
</use_replace_file>

2. Regex pattern with capture groups:
<use_replace_file>
<path>example.ts</path>
<search>old(\w+)</search>
<replace>new\$1</replace>
<use_regex>true</use_regex>
<ignore_case>true</ignore_case>
</use_replace_file>

3. Range-limited replacement in large files:
<use_replace_file>
<path>example.ts</path>
<search>console\.log\(.*\)</search>
<replace>// console.log(commented out)</replace>
<start_line>10</start_line>
<end_line>20</end_line>
<use_regex>true</use_regex>
</use_replace_file>
`;
  }
  return `## use_replace_file
Description: Replace specific sections of content in an existing file using precise SEARCH/REPLACE blocks. This tool performs targeted modifications to exact portions of a file by locating specific content and replacing it with new content.

Parameters:
- path: (required) The file path to modify (relative to current working directory ${args.cwd})
- diff: (required) One or more SEARCH/REPLACE blocks using this exact format:
  \`\`\`
  <<<<<<< SEARCH
  [exact content to find]
  =======
  [new content to replace with]
  >>>>>>> REPLACE
  \`\`\`
Critical Requirements:
1. EXACT MATCHING for SEARCH content:
 * Must match character-for-character including all whitespace, indentation, and line endings
 * Include all comments, docstrings, and formatting exactly as they appear in the file
 * Any mismatch will cause the replacement to fail

2. REPLACEMENT BEHAVIOR:
 * Each SEARCH/REPLACE block replaces ONLY the first matching occurrence
 * For multiple changes, use multiple separate SEARCH/REPLACE blocks
 * Order blocks according to their appearance in the file (top to bottom)

3. BLOCK OPTIMIZATION:
 * Keep each SEARCH section minimal but unique enough to avoid ambiguous matches
 * Include only the lines that need changing plus minimal surrounding context for uniqueness
 * Break large changes into multiple smaller, focused SEARCH/REPLACE blocks
 * Always include complete lines - never truncate lines partially

4. SPECIAL OPERATIONS:
 * To delete code: Leave REPLACE section empty
 * To move code: Use two blocks (first block deletes from original location, second block inserts at new location)
 * To add new code: SEARCH for insertion point, REPLACE with original content plus new code

Usage:
<use_replace_file>
<path>File path here</path>
<diff>
Search and replace blocks here
</diff>
</use_replace_file>`;
}

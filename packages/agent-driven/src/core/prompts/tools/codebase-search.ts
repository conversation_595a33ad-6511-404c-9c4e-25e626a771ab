import { ToolArgs } from './types';

export function getCodebaseSearchDescription(args: ToolArgs): string {
  return `
## codebase_search
Description: A tool to execute full-text or semantic searches across the entire codebase to retrieve relevant code snippets and information. This tool should be invoked when you need to understand, analyze, or extract specific functionality from the codebase to complete the user's request.

When to use this tool:
1. The user's request requires understanding or extracting specific information from the codebase
2. You need to locate relevant code snippets, functions, classes, or modules to answer the user's question
3. The current context lacks sufficient codebase information to complete the task effectively
4. You need to understand how certain features or functionalities are implemented in the code

Parameters:
- query: (required) A comprehensive search query string that describes the target functionality, logic, or code elements you're looking for.

Query construction guidelines:
- Include specific functional and logical keywords relevant to what you're searching for
- Use domain-specific terminology related to the repository's purpose and functionality
- Include related concepts, synonyms, and variations of key terms
- Focus on functionality, modules, components, and logical operations rather than specific parameter names or unrelated content
- Aim for 10-20 highly relevant keywords that capture both the semantic meaning and technical aspects of what you're seeking
- Consider including: feature names, architectural patterns, data operations, user interactions, system components, business logic terms, and technical implementation details

Usage:
<codebase_search>
<query>Your comprehensive query string with relevant functional and logical keywords</query>
</codebase_search>
`.trim();
}

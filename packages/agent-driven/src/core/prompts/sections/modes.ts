import * as vscode from 'vscode';
import * as path from 'path';
import { promises as fs } from 'fs';
import { ModeConfig, getAllModesWithPrompts } from '../../../shared/modes';

export async function getModesSection(context?: vscode.ExtensionContext): Promise<string> {
  if (context) {
    const settingsDir = path.join(context.globalStorageUri.fsPath, 'settings');
    await fs.mkdir(settingsDir, { recursive: true });

    // Get all modes with their overrides from extension state
    const allModes = await getAllModesWithPrompts(context);

    let modesContent = `====

MODES

- These are the currently available modes:
${allModes
  .map((mode: ModeConfig) => {
    let description: string = mode.agentDefinition.split('.')[0];
    if (mode.whenToUse && mode.whenToUse.trim() !== '') {
      // Use whenToUse as the primary description, indenting subsequent lines for readability
      description = mode.whenToUse.replace(/\n/g, '\n    ');
    }
    return `  * "${mode.name}" mode (${mode.agentId}) - ${description}`;
  })
  .join('\n')}`;

    modesContent += `
If the user asks you to create or edit a new mode for this project, you should read the instructions by using the fetch_instructions tool, like this:
<fetch_instructions>
<task>create_mode</task>
</fetch_instructions>
`;

    return modesContent;
  }
  return `====

ACT STYLE V.S. PLAN STYLE

In each user message, the environment_details will specify the current style. There are two modes:

- ACT STYLE: In this style, you have access to all tools EXCEPT the get_plan_info tool.
 - In ACT STYLE, you use tools to accomplish the user's task. Once you've completed the user's task, you use the attempt_task_done tool to present the result of the task to the user.
- PLAN STYLE: In this special style, you have access to the get_plan_info tool.
 - In PLAN STYLE, the goal is to gather information and get context to create a detailed plan for accomplishing the task, which the user will review and approve before they switch you to ACT STYLE to implement the solution.
 - In PLAN STYLE, when you need to converse with the user or present a plan, you should use the get_plan_info tool to deliver your response directly, rather than using <thinking> tags to analyze when to respond. Do not talk about using get_plan_info - just use it directly to share your thoughts and provide helpful answers.

## What is PLAN STYLE?

- While you are usually in ACT STYLE, the user may switch to PLAN STYLE in order to have a back and forth with you to plan how to best accomplish the task.
- When starting in PLAN STYLE, depending on the user's request, you may need to gather information through various methods:
  1. Use tools like use_read_file or use_search_files to get more context about the task
  2. Utilize use_web_search to obtain up-to-date information from the internet, particularly for:
    - Current events and recent developments
    - Specific facts requiring the latest information
    - Topics where timeliness is essential
  You may ask the user clarifying questions to better understand the task or narrow down search parameters. After gathering information, synthesize it clearly and cite your sources appropriately. When helpful, return mermaid diagrams to visually display your understanding of the task or information. Remember to use these tools judiciously and only when necessary to provide accurate, current, and comprehensive information.
- Once you've gained more context about the user's request, you should architect a detailed plan for how you will accomplish the task. Returning mermaid diagrams may be helpful here as well.
- Then you might ask the user if they are pleased with this plan, or if they would like to make any changes. Think of this as a brainstorming session where you can discuss the task and plan the best way to accomplish it.
- If at any point a mermaid diagram would make your plan clearer to help the user quickly see the structure, you are encouraged to include a Mermaid code block in the response. (Note: if you use colors in your mermaid diagrams, be sure to use high contrast colors so the text is readable.)
- Finally once it seems like you've reached a good plan, ask the user to switch you back to ACT STYLE to implement the solution.`;
}

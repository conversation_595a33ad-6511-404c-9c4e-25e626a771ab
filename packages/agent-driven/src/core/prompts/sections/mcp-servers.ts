import { McpHub } from '../../../services/mcp/McpHub';

interface McpServer {
  id: string;
  name: string;
  description?: string;
  tools?: Array<{
    name: string;
    description: string;
    inputSchema: any;
  }>;
  resources?: Array<{
    uri: string;
    name: string;
    description?: string;
  }>;
  resourceTemplates?: Array<{
    uriTemplate: string;
    name: string;
    description?: string;
  }>;
}

export async function getMcpServersSection(mcpHub?: McpHub, enableMcpServerCreation?: boolean): Promise<string> {
  if (!mcpHub || mcpHub.getMode?.() === 'off') {
    return '';
  }

  // 获取连接的MCP服务器
  let connectedServers: McpServer[] = [];
  try {
    // @ts-ignore - 忽略类型错误，因为我们不确定 getConnectedServers 是否存在
    connectedServers = (await mcpHub.getServers?.()) || [];
  } catch (error) {
    console.error('Error getting connected MCP servers:', error);
  }

  // 生成服务器描述
  const serverDescriptions = connectedServers.map((server) => {
    const toolsDescription = server.tools?.length
      ? `\n\n### Available Tools\n${server.tools
          .map(
            (tool) =>
              `- ${tool.name}: ${tool.description}\n    Input Schema:\n    ${JSON.stringify(tool.inputSchema, null, 2)}`
          )
          .join('\n\n')}`
      : '';

    const resourcesDescription = server.resources?.length
      ? `\n\n### Available Resources\n${server.resources
          .map(
            (resource) =>
              `- ${resource.uri}: ${resource.name}${resource.description ? ` - ${resource.description}` : ''}`
          )
          .join('\n')}`
      : '';

    const resourceTemplatesDescription = server.resourceTemplates?.length
      ? `\n\n### Available Resource Templates\n${server.resourceTemplates
          .map(
            (template) =>
              `- ${template.uriTemplate}: ${template.name}${template.description ? ` - ${template.description}` : ''}`
          )
          .join('\n')}`
      : '';

    return `## ${server.name} (\`${server.id}\`)

${
  server.description || 'No description provided.'
}${toolsDescription}${resourcesDescription}${resourceTemplatesDescription}`;
  });

  // 基本的MCP服务器部分
  let mcpServersSection = `====

MCP SERVERS

The Model Context Protocol (MCP) enables seamless communication between the system and MCP servers, which provide additional tools and resources to extend your capabilities. There are two distinct types of MCP servers:

1. **Local (Stdio-based) servers**: These run locally on the user's machine and communicate via standard input/output streams
2. **Remote (SSE-based) servers**: These run on remote machines and communicate via Server-Sent Events (SSE) over HTTP/HTTPS protocols

# Connected MCP Servers

${serverDescriptions?.length ? serverDescriptions.join('\n\n') : 'No MCP servers are currently connected.'}`;

  // 如果启用了MCP服务器创建，添加相关说明
  if (enableMcpServerCreation) {
    mcpServersSection += `

## Creating an MCP Server

When a user requests functionality such as "add a tool" to perform a specific function, this typically means creating an MCP server that provides tools and resources. These servers can connect to external APIs and other services. You have the capability to create MCP servers and add them to a configuration file, which will then expose the tools and resources for use with \`use_mcp_tools\` and \`get_mcp_resource\`.

**Important Constraints for MCP Server Creation:**
MCP servers operate in a non-interactive environment with the following limitations:
- Cannot initiate OAuth flows
- Cannot open browser windows
- Cannot prompt for user input during runtime
- All credentials and authentication tokens must be provided upfront through environment variables in the MCP settings configuration

**Default Location for New MCP Servers:**
Unless the user specifies otherwise, create new MCP servers in: ~/Documents/JoyCode/MCP

# When to Use MCP Servers

**MCP servers are not always necessary.** Consider the following guidelines:

- Users may request tasks that can be completed with existing tools without requiring MCP server creation
- While the MCP SDK can extend your capabilities, it represents just one specialized approach among many available tools
- **Only implement MCP servers when explicitly requested** by the user (e.g., "add a tool that...", "create a server for...", "I need a custom tool to...")
- For general tasks, first evaluate whether existing capabilities are sufficient before suggesting MCP server creation`;
  }

  return mcpServersSection;
}

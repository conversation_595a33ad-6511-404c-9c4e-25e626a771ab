import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { formatResponse } from '../../prompts/responses';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { FileSystemHelper } from '../../../utils/FileSystemHelper';
import { fileExistsAtPath } from '../../../utils/fs';
import { fixModelHtmlEscaping, removeInvalidChars } from '../../../utils/string';
import { GlobalState } from '@joycoder/shared';
import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';
import { getReadablePath } from '../../../utils/path';
import { everyLineHasLineNumbers, stripLineNumbers } from '../../../integrations/misc/extract-text';

import '../../../utils/path'; // Import to ensure String.prototype.toPosix is available

import delay from 'delay';
import * as path from 'path';
import * as vscode from 'vscode';
import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { ActionType, reportAction } from '@joycoder/shared';
import { detectCodeOmission } from '../../../integrations/editor/detect-omission';
import {
  pushToolResult,
  removeClosingTag,
  showNotificationForApprovalIfAutoApprovalEnabled,
  pushAdditionalToolFeedback,
  handleError,
} from './common';

export async function useWriteFileTool(joyCoder: JoyCoder, block: ToolUse) {
  const relPath: string | undefined = block.params.path;
  const content: string | undefined = block.params.content; // for use_write_file
  const userContent = block.userContent;
  const conversationId: string | undefined = joyCoder.conversationId;
  const predictedLineCount: number | undefined = parseInt(block.params.line_count ?? '0');
  // let diff: string | undefined = block.params.diff; // for use_replace_file
  if (!relPath || !content) {
    // checking for content/diff ensures relPath is complete
    // wait so we can determine if it's a new file or editing an existing file
    return;
  }
  const accessAllowed = joyCoder.JoyCoderIgnoreController.validateAccess(relPath);
  if (!accessAllowed) {
    await joyCoder.say('joycoderignore_error', relPath);
    await pushToolResult(joyCoder, block, formatResponse.toolError(formatResponse.joycoderIgnoreError(relPath)));
    await joyCoder.saveCheckpoint();
    return;
  }

  // Check if file exists using cached map or fs.access
  let fileExists: boolean;
  if (joyCoder.diffViewProvider.editType !== undefined) {
    fileExists = joyCoder.diffViewProvider.editType === 'modify';
  } else {
    const absolutePath = FileSystemHelper.resolveUri(joyCoder.cwd, relPath);
    fileExists = await fileExistsAtPath(absolutePath);
    joyCoder.diffViewProvider.editType = fileExists ? 'modify' : 'create';
  }
  try {
    joyCoder.currentFilePath = relPath;
    // Construct newContent from diff
    let newContent = '';
    if (content) {
      newContent = content;

      // pre-processing newContent for cases where weaker models might add artifacts like markdown codeblock markers (deepseek/llama) or extra escape characters (gemini)
      if (newContent.startsWith('```')) {
        // this handles cases where it includes language specifiers like ```python ```js
        newContent = newContent.split('\n').slice(1).join('\n').trim();
      }
      if (newContent.endsWith('```')) {
        newContent = newContent.split('\n').slice(0, -1).join('\n').trim();
      }

      if (!GlobalState.get('openAiModelId').includes('claude')) {
        // it seems not just llama models are doing jc, but also gemini and potentially others
        newContent = fixModelHtmlEscaping(newContent);
        newContent = removeInvalidChars(newContent);
      }
    }

    newContent = newContent.trimEnd(); // remove any trailing newlines, since it's automatically inserted by the editor
    // AI生成代码暂存，似乎有重复
    // AdoptResultCache.setRemote(
    //   newContent || content,
    //   GlobalState.get('openAiModelId'),
    //   AdoptResultCache.ADOPT_CODE_SOURCE.AUTO_CODE,
    //   joyCoder.conversationId,
    // );
    const sharedMessageProps: JoyCoderSayTool = {
      tool: fileExists ? 'editedExistingFile' : 'newFileCreated',
      path: getReadablePath(joyCoder.cwd, removeClosingTag(joyCoder, 'path', relPath, block.partial)),
      content: newContent,
      conversationId,
    };

    if (block.partial) {
      // update gui message
      const partialMessage = JSON.stringify(sharedMessageProps);
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool'); // in case the user changes auto-approval settings mid stream
        await joyCoder.say('tool', partialMessage, undefined, block.partial);
      } else {
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      }
      // update editor
      if (!joyCoder.diffViewProvider.isEditing) {
        const partialMessage = JSON.stringify(sharedMessageProps);
        await joyCoder.ask('tool', partialMessage, true).catch(() => {}); // sending true for partial even though it's not a partial, this shows the edit row before the content is streamed into the editor
        // open the editor and prepare to stream content in
        await joyCoder.diffViewProvider.open(relPath);
      }
      // AI生成代码内容暂存
      AdoptResultCache.clearAutoCodeAdopt();
      AdoptResultCache.setRemote(
        newContent,
        GlobalState.get('openAiModelId'),
        AdoptResultCache.ADOPT_CODE_SOURCE.AUTO_CODE,
        joyCoder.conversationId
      );
      // editor is open, stream content in
      await joyCoder.diffViewProvider.update(
        everyLineHasLineNumbers(newContent) ? stripLineNumbers(newContent) : newContent,
        false
      );
      return;
    } else {
      // 在数据输出结束时，调用一次scrollToFirstDiff()
      // joyCoder.diffViewProvider.scrollToFirstDiff();

      if (!relPath) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError(block.name, 'path'));
        await joyCoder.diffViewProvider.reset();
        await joyCoder.saveCheckpoint();
        return;
      }

      if (!newContent) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('use_write_file', 'content')
        );
        await joyCoder.diffViewProvider.reset();
        await joyCoder.saveCheckpoint();
        return;
      }
      if (!predictedLineCount) {
        joyCoder.consecutiveMistakeCount++;
        // Calculate the actual number of lines in the content
        const actualLineCount = newContent.split('\n').length;
        // Check if this is a new file or existing file
        const isNewFile = !fileExists;
        // Check if diffStrategy is enabled
        const diffStrategyEnabled = !!joyCoder.diffStrategy;
        // Use more specific error message for line_count that provides guidance based on the situation
        await joyCoder.say(
          'error',
          `JoyCode 尝试使用写入文件工具${
            relPath ? `对${relPath.toPosix()}` : ''
          }，但是所需的参数'line_count'在写入${actualLineCount}行内容后缺失或被截断。正在重试...`
        );

        await pushToolResult(
          joyCoder,
          block,
          formatResponse.toolError(
            formatResponse.lineCountTruncationError(actualLineCount, isNewFile, diffStrategyEnabled)
          )
        );
        await joyCoder.diffViewProvider.revertChanges();
        await joyCoder.saveCheckpoint();
        return;
      }

      joyCoder.consecutiveMistakeCount = 0;

      // 数据上报1
      // AI生成代码内容上报
      try {
        reportAction({
          actionCate: 'ai',
          actionType: ActionType.AutoGenerator,
          question: userContent?.map((item: any) => item?.text)?.join('\n'),
          result: newContent,
          conversationId,
          model: GlobalState.get('openAiModelId'),
          startTime: new Date(),
          extendMsg: {
            type: 'yesButtonClicked',
            modeType: ((await joyCoder.providerRef.deref()?.getState()) || {}).mode,
            taskId: joyCoder.taskId,
            sessionId: joyCoder.sessionId,
          },
        });
        // AI生成代码内容暂存
        AdoptResultCache.clearAutoCodeAdopt();
        AdoptResultCache.setRemote(
          newContent,
          GlobalState.get('openAiModelId'),
          AdoptResultCache.ADOPT_CODE_SOURCE.AUTO_CODE,
          joyCoder.conversationId
        );
      } catch (error) {
        console.error('%c [ reportAction->error ]-241', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      }

      // if isEditingFile false, that means we have the full contents of the file already.
      // it's important to note how this function works, you can't make the assumption that the block.partial conditional will always be called since it may immediately get complete, non-partial data. So this part of the logic will always be called.
      // in other words, you must always repeat the block.partial logic here
      if (!joyCoder.diffViewProvider.isEditing) {
        // show gui message before showing edit animation
        const partialMessage = JSON.stringify(sharedMessageProps);
        await joyCoder.ask('tool', partialMessage, true).catch(() => {}); // sending true for partial even though it's not a partial, this shows the edit row before the content is streamed into the editor
        await joyCoder.diffViewProvider.open(relPath);
      }
      await joyCoder.diffViewProvider.update(
        everyLineHasLineNumbers(newContent) ? stripLineNumbers(newContent) : newContent,
        true
      );
      await delay(300); // wait for diff view to update

      joyCoder.diffViewProvider.scrollToFirstDiff();

      // Check for code omissions before proceeding
      if (detectCodeOmission(joyCoder.diffViewProvider.originalContent || '', newContent, predictedLineCount)) {
        if (joyCoder.diffStrategy) {
          await joyCoder.diffViewProvider.revertChanges();

          await pushToolResult(
            joyCoder,
            block,
            formatResponse.toolError(
              `Content appears to be truncated (file has ${
                newContent.split('\n').length
              } lines but was predicted to have ${predictedLineCount} lines), and found comments indicating omitted code (e.g., '// rest of code unchanged', '/* previous code */'). Please provide the complete file content without any omissions if possible, or otherwise use the 'apply_diff' tool to apply the diff to the original file.`
            )
          );
          return;
        } else {
          vscode.window.showWarningMessage('JoyCoder检测到潜在的代码截断，因为当AI达到其最大输出限制。');
        }
      }

      const completeMessage = JSON.stringify({
        ...sharedMessageProps,
        content: newContent,
        diff: fileExists
          ? formatResponse.createPrettyPatch(relPath, joyCoder.diffViewProvider.originalContent, newContent)
          : undefined,
      } as JoyCoderSayTool);

      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', completeMessage, undefined, false);
        joyCoder.consecutiveAutoApprovedRequestsCount++;
        // we need an artificial delay to let the diagnostics catch up to the changes
        await delay(3_500);
      } else {
        // If auto-approval is enabled but this tool wasn't auto-approved, send notification
        showNotificationForApprovalIfAutoApprovalEnabled(
          joyCoder,
          `JoyCoder 想要 ${fileExists ? '编辑' : '创建'} ${path.basename(relPath)}`
        );
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        // const didApprove = await askApproval("tool", completeMessage)

        // Need a more customized tool response for file edits to highlight the fact that the file was not updated (particularly important for deepseek)
        let didApprove = true;
        const { response, text, images } = await joyCoder.ask('tool', completeMessage, false);
        if (response !== 'yesButtonClicked') {
          // TODO: add similar context for other tool denial responses, to emphasize ie that a command was not run
          const fileDeniedNote = fileExists
            ? 'The file was not updated, and maintains its original contents.'
            : 'The file was not created.';
          await pushToolResult(joyCoder, block, `The user denied this operation. ${fileDeniedNote}`);
          if (text || images?.length) {
            await pushAdditionalToolFeedback(joyCoder, block, text, images);
            await joyCoder.say('user_feedback', text, images);
            await joyCoder.saveCheckpoint();
          }
          joyCoder.didRejectTool = true;
          didApprove = false;
        } else {
          if (text || images?.length) {
            await pushAdditionalToolFeedback(joyCoder, block, text, images);
            await joyCoder.say('user_feedback', text, images);
            await joyCoder.saveCheckpoint();
          }
        }

        if (!didApprove) {
          await joyCoder.diffViewProvider.revertChanges();
          await joyCoder.saveCheckpoint();
          return;
        }
      }
      // 数据上报2
      // 用户采纳上报
      try {
        reportAction({
          actionCate: 'ai',
          actionType: ActionType.copy,
          question: userContent?.map((item: any) => item?.text)?.join('\n'),
          result: '', //此处惯例不上报内容，数据侧有处理逻辑认定为全部采纳newContent,
          conversationId,
          model: GlobalState.get('openAiModelId'),
          startTime: new Date(),
          extendMsg: {
            type: 'yesButtonClicked',
            modeType: ((await joyCoder.providerRef.deref()?.getState()) || {}).mode,
            taskId: joyCoder.taskId,
            sessionId: joyCoder.sessionId,
          },
        });
      } catch (error) {
        console.error(
          '%c [ askApproval-reportAction-error ]-1946',
          'font-size:13px; background:pink; color:#bf2c9f;',
          error
        );
      }
      AdoptResultCache.clearAutoCodeAdopt();
      // Mark the file as edited by JoyCoder to prevent false "recently modified" warnings
      joyCoder.fileContextTracker.markFileAsEditedByJoyCoder(relPath);

      const { newProblemsMessage, userEdits, autoFormattingEdits, finalContent } =
        await joyCoder.diffViewProvider.saveChanges();
      joyCoder.didEditFile = true; // used to determine if we should wait for busy terminal to update before sending api request

      // Track file edit operation
      await joyCoder.fileContextTracker.trackFileContext(relPath, 'JoyCoder_edited');

      if (userEdits) {
        // Track file edit operation
        await joyCoder.fileContextTracker.trackFileContext(relPath, 'user_edited');

        await joyCoder.say(
          'user_feedback_diff',
          JSON.stringify({
            tool: fileExists ? 'editedExistingFile' : 'newFileCreated',
            path: getReadablePath(joyCoder.cwd, relPath),
            diff: userEdits,
          } as JoyCoderSayTool)
        );
        await pushToolResult(
          joyCoder,
          block,
          `The user made the following updates to your content:\n\n${userEdits}\n\n` +
            (autoFormattingEdits
              ? `The user's editor also applied the following auto-formatting to your content:\n\n${autoFormattingEdits}\n\n(Note: Pay close attention to changes such as single quotes being converted to double quotes, semicolons being removed or added, long lines being broken into multiple lines, adjusting indentation style, adding/removing trailing commas, etc. this will help you ensure future SEARCH/REPLACE operations to this file are accurate.)\n\n`
              : '') +
            `The updated content, which includes both your original modifications and the additional edits, has been successfully saved to ${relPath.toPosix()}. Here is the full, updated content of the file that was saved:\n\n` +
            `<final_file_content path="${relPath.toPosix()}">\n${finalContent}\n</final_file_content>\n\n` +
            `Please note:\n` +
            `1. You do not need to re-write the file with these changes, as they have already been applied.\n` +
            `2. Proceed with the task using this updated file content as the new baseline.\n` +
            `3. If the user's edits have addressed part of the task or changed the requirements, adjust your approach accordingly.` +
            `4. IMPORTANT: For any future changes to this file, use the final_file_content shown above as your reference. This content reflects the current state of the file, including both user edits and any auto-formatting (e.g., if you used single quotes but the formatter converted them to double quotes). Always base your SEARCH/REPLACE operations on this final version to ensure accuracy.\n` +
            `${newProblemsMessage}`
        );
      } else {
        await pushToolResult(
          joyCoder,
          block,
          `The content was successfully saved to ${relPath.toPosix()}.\n\n` +
            (autoFormattingEdits
              ? `Along with your edits, the user's editor applied the following auto-formatting to your content:\n\n${autoFormattingEdits}\n\n(Note: Pay close attention to changes such as single quotes being converted to double quotes, semicolons being removed or added, long lines being broken into multiple lines, adjusting indentation style, adding/removing trailing commas, etc. this will help you ensure future SEARCH/REPLACE operations to this file are accurate.)\n\n`
              : '') +
            `Here is the full, updated content of the file that was saved:\n\n` +
            `<final_file_content path="${relPath.toPosix()}">\n${finalContent}\n</final_file_content>\n\n` +
            `IMPORTANT: For any future changes to this file, use the final_file_content shown above as your reference. This content reflects the current state of the file, including any auto-formatting (e.g., if you used single quotes but the formatter converted them to double quotes). Always base your SEARCH/REPLACE operations on this final version to ensure accuracy.\n\n` +
            `${newProblemsMessage}`
        );
      }

      if (!fileExists) {
        joyCoder.providerRef.deref()?.workspaceTracker?.populateFilePaths();
      }

      await joyCoder.diffViewProvider.reset();

      await joyCoder.saveCheckpoint();

      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, JoyCoderRestoreMessageMap['writing file'], error);
    await joyCoder.diffViewProvider.revertChanges();
    await joyCoder.diffViewProvider.reset();
    await joyCoder.saveCheckpoint();

    return;
  }
}

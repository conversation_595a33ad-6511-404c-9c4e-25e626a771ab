import { findLast } from 'lodash';
import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { parsePartialArrayString } from '../../../shared/array';
import { JoyCoderPlanModeResponse } from '../../../shared/ExtensionMessage';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { removeClosingTag, pushToolResult, handleError } from './common';

export async function getPlanInfoTool(joyCoder: JoyCoder, block: ToolUse) {
  const response: string | undefined = block.params.response;
  const optionsRaw: string | undefined = block.params.options;
  const sharedMessage = {
    response: removeClosingTag(joyCoder, 'response', response, block.partial),
    options: parsePartialArrayString(removeClosingTag(joyCoder, 'options', optionsRaw, block.partial)),
  } as JoyCoderPlanModeResponse;
  try {
    if (block.partial) {
      await joyCoder.ask('get_plan_info', JSON.stringify(sharedMessage), block.partial).catch(() => {});
      return;
    } else {
      if (!response) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('get_plan_info', 'response')
        );
        //
        return;
      }
      joyCoder.consecutiveMistakeCount = 0;

      // if (joyCoder.autoApprovalSettings.enabled && joyCoder.autoApprovalSettings.enableNotifications) {
      // 	showSystemNotification({
      // 		subtitle: "JoyCoder has a response...",
      // 		message: response.replace(/\n/g, " "),
      // 	})
      // }

      joyCoder.isAwaitingPlanResponse = true;
      // const { text, images } = await joyCoder.ask('get_plan_info', response, false);
      let { text, images } = await joyCoder.ask('get_plan_info', JSON.stringify(sharedMessage), false);
      joyCoder.isAwaitingPlanResponse = false;
      // webview invoke sendMessage will send this marker in order to put webview into the proper state (responding to an ask) and as a flag to extension that the user switched to ACT mode.
      if (text === 'PLAN_MODE_TOGGLE_RESPONSE') {
        text = '';
      }
      // Check if options contains the text response
      if (optionsRaw && text && parsePartialArrayString(optionsRaw).includes(text)) {
        // Valid option selected, don't show user message in UI
        // Update last followup message with selected option
        const lastPlanMessage = findLast(joyCoder.JoyCoderMessages, (m) => m.ask === 'get_plan_info');
        if (lastPlanMessage) {
          lastPlanMessage.text = JSON.stringify({
            ...sharedMessage,
            selected: text,
          } as JoyCoderPlanModeResponse);
          await joyCoder.saveJoyCoderMessages();
        }
      } else {
        // Option not selected, send user feedback
        if (text || images?.length) {
          await joyCoder.say('user_feedback', text ?? '', images);
          await joyCoder.saveCheckpoint();
        }
      }

      if (joyCoder.didRespondToPlanAskBySwitchingMode) {
        // await joyCoder.say("user_feedback", text ?? "", images)
        await pushToolResult(
          joyCoder,
          block,
          formatResponse.toolResult(
            `[The user has switched to ACT STYLE, so you may now proceed with the task.]` +
              (text
                ? `\n\nThe user also provided the following message when switching to ACT STYLE:\n<user_message>\n${text}\n</user_message>`
                : ''),
            images
          )
        );
      } else {
        await pushToolResult(
          joyCoder,
          block,
          formatResponse.toolResult(`<user_message>\n${text}\n</user_message>`, images)
        );
      }

      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, JoyCoderRestoreMessageMap['responding to inquiry'], error);
    return;
  }
}

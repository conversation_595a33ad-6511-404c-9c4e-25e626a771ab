import { getVscodeConfig } from '@joycoder/shared';
import { isBinaryFile } from 'isbinaryfile';
import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { addLineNumbers, extractTextFromFile } from '../../../integrations/misc/extract-text';
import { countFileLines } from '../../../integrations/misc/line-counter';
import { readLines } from '../../../integrations/misc/read-lines';
import { parseSourceCodeDefinitionsForFile } from '../../../services/tree-sitter';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { FileSystemHelper } from '../../../utils/FileSystemHelper';
import { getReadablePath } from '../../../utils/path';
import { isPathOutsideWorkspace } from '../../../utils/pathUtils';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import {
  removeClosingTag,
  pushToolResult,
  showNotificationForApprovalIfAutoApprovalEnabled,
  askApproval,
  handleError,
} from './common';

export async function useReadFileTool(joyCoder: JoyCoder, block: ToolUse) {
  const relPath: string | undefined = block.params.path;
  const startLineStr: string | undefined = block.params.start_line;
  const endLineStr: string | undefined = block.params.end_line;

  // Get the full path and determine if it's outside the workspace
  const fullPath = relPath
    ? FileSystemHelper.getRemotePath(
        FileSystemHelper.resolveUri(joyCoder.cwd, removeClosingTag(joyCoder, 'path', relPath, block.partial))
      )
    : '';
  const isOutsideWorkspace = isPathOutsideWorkspace(fullPath);

  const sharedMessageProps: JoyCoderSayTool = {
    tool: 'readFile',
    path: getReadablePath(joyCoder.cwd, removeClosingTag(joyCoder, 'path', relPath, block.partial)),
    isOutsideWorkspace,
  };
  try {
    if (relPath) {
      joyCoder.currentFilePath = relPath;
    }

    if (block.partial) {
      const partialMessage = JSON.stringify({
        ...sharedMessageProps,
        content: undefined,
      } as JoyCoderSayTool);
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', partialMessage, undefined, block.partial);
      } else {
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      }
      return;
    } else {
      if (!relPath) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('use_read_file', 'path'));
        await joyCoder.saveCheckpoint();
        return;
      }
      const maxReadFileLine = getVscodeConfig('JoyCoder.coder.maxReadFileLine');
      const isFullRead = maxReadFileLine === -1;

      // Check if we're doing a line range read
      let isRangeRead = false;
      let startLine: number | undefined = undefined;
      let endLine: number | undefined = undefined;

      // Check if we have either range parameter and we're not doing a full read
      if (!isFullRead && (startLineStr || endLineStr)) {
        isRangeRead = true;
      }

      // Parse start_line if provided
      if (startLineStr) {
        startLine = parseInt(startLineStr);

        if (isNaN(startLine)) {
          // Invalid start_line
          joyCoder.consecutiveMistakeCount++;
          await joyCoder.say('error', `Failed to parse start_line: ${startLineStr}`);
          await pushToolResult(
            joyCoder,
            block,
            `<file><path>${relPath}</path><error>Invalid start_line value</error></file>`
          );
          return;
        }

        startLine -= 1; // Convert to 0-based index
      }

      // Parse end_line if provided
      if (endLineStr) {
        endLine = parseInt(endLineStr);

        if (isNaN(endLine)) {
          // Invalid end_line
          joyCoder.consecutiveMistakeCount++;
          await joyCoder.say('error', `Failed to parse end_line: ${endLineStr}`);
          await pushToolResult(
            joyCoder,
            block,
            `<file><path>${relPath}</path><error>Invalid end_line value</error></file>`
          );
          return;
        }

        // Convert to 0-based index
        endLine -= 1;
      }

      const accessAllowed = joyCoder.JoyCoderIgnoreController.validateAccess(relPath);
      if (!accessAllowed) {
        await joyCoder.say('joycoderignore_error', relPath);
        await pushToolResult(joyCoder, block, formatResponse.toolError(formatResponse.joycoderIgnoreError(relPath)));
        await joyCoder.saveCheckpoint();
        return;
      }

      // Create line snippet description for approval message
      let lineSnippet = '';

      if (isFullRead) {
        // 完整阅读不需要片段
      } else if (startLine !== undefined && endLine !== undefined) {
        lineSnippet = `第 ${startLine + 1} 行到第 ${endLine + 1} 行`;
      } else if (startLine !== undefined) {
        lineSnippet = `从第 ${startLine + 1} 行到末尾`;
      } else if (endLine !== undefined) {
        lineSnippet = `从开始到第 ${endLine + 1} 行`;
      } else if (maxReadFileLine === 0) {
        lineSnippet = '仅定义';
      } else if (maxReadFileLine > 0) {
        lineSnippet = `最多 ${maxReadFileLine} 行`;
      }

      joyCoder.consecutiveMistakeCount = 0;
      const absolutePath = FileSystemHelper.resolveUri(joyCoder.cwd, relPath);
      // const absolutePath_vscode = vscode.Uri.joinPath(cwd as vscode.Uri, relPath);
      const completeMessage = JSON.stringify({
        ...sharedMessageProps,
        content: FileSystemHelper.getRemotePath(absolutePath),
        reason: lineSnippet,
      } as JoyCoderSayTool);

      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', completeMessage, undefined, false); // need to be sending partialValue bool, since undefined has its own purpose in that the message is treated neither as a partial or completion of a partial, but as a single complete message
        joyCoder.consecutiveAutoApprovedRequestsCount++;
      } else {
        showNotificationForApprovalIfAutoApprovalEnabled(
          joyCoder,
          `JoyCoder 想要读取 ${FileSystemHelper.basename(absolutePath)}`
        );
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        const didApprove = await askApproval(
          joyCoder,
          block,
          'tool',
          completeMessage,
          joyCoder.conversationId,
          block.userContent
        );
        if (!didApprove) {
          await joyCoder.saveCheckpoint();
          return;
        }
      }
      // // now execute the tool like normal
      // content = await extractTextFromFile(absolutePath);

      // Track file read operation
      await joyCoder.fileContextTracker.trackFileContext(relPath, 'read_tool');
      // Count total lines in the file
      let totalLines = 0;

      try {
        totalLines = await countFileLines(absolutePath);
      } catch (error) {
        console.error(`Error counting lines in file ${absolutePath}:`, error);
      }
      // now execute the tool like normal
      let content = '';
      let isFileTruncated = false;
      let sourceCodeDef = '';

      const isBinary = await isBinaryFile(FileSystemHelper.getRemotePath(absolutePath)).catch(() => false);

      if (isRangeRead) {
        if (startLine === undefined) {
          content = addLineNumbers(await readLines(FileSystemHelper.getRemotePath(absolutePath), endLine, startLine));
        } else {
          content = addLineNumbers(
            await readLines(FileSystemHelper.getRemotePath(absolutePath), endLine, startLine),
            startLine + 1
          );
        }
      } else if (!isBinary && maxReadFileLine >= 0 && totalLines > maxReadFileLine) {
        // If file is too large, only read the first maxReadFileLine lines
        isFileTruncated = true;

        const res = await Promise.all([
          maxReadFileLine > 0 ? readLines(FileSystemHelper.getRemotePath(absolutePath), maxReadFileLine - 1, 0) : '',
          (async () => {
            try {
              return await parseSourceCodeDefinitionsForFile(
                FileSystemHelper.getRemotePath(absolutePath),
                joyCoder.JoyCoderIgnoreController
              );
            } catch (error) {
              if (error instanceof Error && error.message.startsWith('Unsupported language:')) {
                console.warn(`[read_file] Warning: ${error.message}`);
                return undefined;
              } else {
                console.error(`[read_file] Unhandled error: ${error instanceof Error ? error.message : String(error)}`);
                return undefined;
              }
            }
          })(),
        ]);

        content = res[0].length > 0 ? addLineNumbers(res[0]) : '';
        const result = res[1];

        if (result) {
          sourceCodeDef = `${result}`;
        }
      } else {
        // Read entire file
        content = await extractTextFromFile(absolutePath);
      }

      // Create variables to store XML components
      let xmlInfo = '';
      let contentTag = '';

      // Add truncation notice if applicable
      if (isFileTruncated) {
        xmlInfo += `<notice>Showing only ${maxReadFileLine} of ${totalLines} total lines. Use start_line and end_line if you need to read more</notice>\n`;

        // Add source code definitions if available
        if (sourceCodeDef) {
          xmlInfo += `<list_code_definition_names>${sourceCodeDef}</list_code_definition_names>\n`;
        }
      }

      // Empty files (zero lines)
      if (content === '' && totalLines === 0) {
        // Always add self-closing content tag and notice for empty files
        contentTag = `<content/>`;
        xmlInfo += `<notice>File is empty</notice>\n`;
      }
      // Range reads should always show content regardless of maxReadFileLine
      else if (isRangeRead) {
        // Create content tag with line range information
        let lineRangeAttr = '';
        const displayStartLine = startLine !== undefined ? startLine + 1 : 1;
        const displayEndLine = endLine !== undefined ? endLine + 1 : totalLines;
        lineRangeAttr = ` lines="${displayStartLine}-${displayEndLine}"`;

        // Maintain exact format expected by tests
        contentTag = `<content${lineRangeAttr}>\n${content}</content>\n`;
      }
      // maxReadFileLine=0 for non-range reads
      else if (maxReadFileLine === 0) {
        // Skip content tag for maxReadFileLine=0 (definitions only mode)
        contentTag = '';
      }
      // Normal case: non-empty files with content (non-range reads)
      else {
        // For non-range reads, always show line range
        let lines = totalLines;

        if (maxReadFileLine >= 0 && totalLines > maxReadFileLine) {
          lines = maxReadFileLine;
        }

        const lineRangeAttr = ` lines="1-${lines}"`;

        // Maintain exact format expected by tests
        contentTag = `<content${lineRangeAttr}>\n${content}</content>\n`;
      }

      // Track file read operation
      if (relPath) {
        await joyCoder.fileContextTracker.trackFileContext(relPath, 'read_tool');
      }

      // Format the result into the required XML structure
      const xmlResult = `<file><path>${relPath}</path>\n${contentTag}${xmlInfo}</file>`;
      await pushToolResult(joyCoder, block, xmlResult);
      await joyCoder.saveCheckpoint();
      // await pushToolResult(content);
      return;
    }
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : String(error);
    await pushToolResult(
      joyCoder,
      block,
      `<file><path>${relPath || ''}</path><error>Error reading file: ${errorMsg}</error></file>`
    );
    await handleError(joyCoder, block, JoyCoderRestoreMessageMap['reading file'], error);
    await joyCoder.saveCheckpoint();
    return;
  }
}

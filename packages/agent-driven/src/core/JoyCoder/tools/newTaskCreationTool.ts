import delay from 'delay';

import { getModeBySlug, defaultModeSlug } from '../../../../web-agent/src/utils/modes';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { removeClosingTag, pushToolResult, askApproval, handleError } from './common';

export async function newTaskCreationTool(joyCoder: JoyCoder, block: ToolUse) {
  const context: string | undefined = block.params.context;
  const mode: string | undefined = block.params.mode;
  const message: string | undefined = block.params.message;
  try {
    if (block.partial) {
      const partialMessage = JSON.stringify({
        tool: 'newTask',
        mode: removeClosingTag(joyCoder, 'mode', mode, block.partial),
        message: removeClosingTag(joyCoder, 'message', message, block.partial),
        context: removeClosingTag(joyCoder, 'context', context, block.partial),
      });
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', partialMessage, undefined, block.partial);
      } else {
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      }
      // await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      // await joyCoder.ask('new_task_creation', partialMessage, block.partial).catch(() => {});
      return;
    } else {
      // if (!context) {
      //   joyCoder.consecutiveMistakeCount++;
      //   await pushToolResult(joyCoder,block,await joyCoder.sayAndCreateMissingParamError('new_task_creation', 'context'));
      //   return;
      // }
      if (!mode) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('new_task_creation', 'mode')
        );
        await joyCoder.saveCheckpoint();
        return;
      }

      if (!message) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('new_task_creation', 'message')
        );
        await joyCoder.saveCheckpoint();
        return;
      }
      joyCoder.consecutiveMistakeCount = 0;
      // Verify the mode exists
      const targetMode = getModeBySlug(mode, (await joyCoder.providerRef.deref()?.getState())?.customModes);

      if (!targetMode) {
        await pushToolResult(joyCoder, block, formatResponse.toolError(`Invalid mode: ${mode}`));
        await joyCoder.saveCheckpoint();
        return;
      }
      const toolMessage = JSON.stringify({
        tool: 'newTask',
        mode: targetMode.name,
        content: message,
      });
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', toolMessage, undefined, false);
        joyCoder.consecutiveAutoApprovedRequestsCount++;
        await delay(1000);
      } else {
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        const didApprove = await askApproval(joyCoder, block, 'tool', toolMessage);
        if (!didApprove) {
          await joyCoder.saveCheckpoint();
          return;
        }
      }

      const provider = joyCoder.providerRef.deref();

      if (!provider) {
        return;
      }

      // Preserve the current mode so we can resume with it later.
      joyCoder.pausedModeSlug = (await provider.getState()).mode ?? defaultModeSlug;

      // Switch mode first, then create new task instance.
      await provider.handleModeSwitch(mode);

      // Delay to allow mode change to take effect before next tool is executed.
      await delay(500);

      await provider.initJoyCoderWithTask(message, undefined, joyCoder);

      await pushToolResult(
        joyCoder,
        block,
        `Successfully created new task in ${targetMode.name} mode with message: ${message}`
      );

      // Set the isPaused flag to true so the parent
      // task can wait for the sub-task to finish.
      joyCoder.isPaused = true;
      // joyCoder.emit('taskPaused');
      // const { text, images } = await joyCoder.ask('new_task_creation', context, false);

      // // If the user provided a response, treat it as feedback
      // if (text || images?.length) {
      //   await joyCoder.say('user_feedback', text ?? '', images);
      //   await pushToolResult(joyCoder,block,
      //     formatResponse.toolResult(
      //       `The user provided feedback instead of creating a new task:\n<feedback>\n${text}\n</feedback>`,
      //       images,
      //     ),
      //   );
      // } else {
      //   // If no response, the user clicked the "Create New Task" button
      //   await pushToolResult(joyCoder,block,
      //     formatResponse.toolResult(`The user has created a new task with the provided context.`),
      //   );
      // }
      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, '创建新任务', error);
    await joyCoder.saveCheckpoint();
    return;
  }
}

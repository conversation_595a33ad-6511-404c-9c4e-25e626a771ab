import delay from 'delay';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { fileExistsAtPath } from '../../../utils/fs';
import { getReadablePath } from '../../../utils/path';
import { FileSystemHelper } from '../../../utils/FileSystemHelper';
import { ToolUse } from '../../assistant-message';
import { insertGroups } from '../../diff/insert-groups';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { removeClosingTag, pushToolResult, handleError, askApproval } from './common';
import { reportAction, ActionType, GlobalState } from '@joycoder/shared';
import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';

export async function insertContentTool(joyCoder: JoyCoder, block: ToolUse) {
  const relPath: string | undefined = block.params.path;
  const line: string | undefined = block.params.line;
  const content: string | undefined = block.params.content;

  const sharedMessageProps: JoyCoderSayTool = {
    tool: 'insertContent',
    path: getReadablePath(joyCoder.cwd, removeClosingTag(joyCoder, 'path', relPath, block.partial)),
    lineNumber: line ? parseInt(line, 10) : undefined,
  };

  try {
    if (block.partial) {
      const partialMessage = JSON.stringify(sharedMessageProps);
      await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      return;
    }

    // Validate required parameters
    if (!relPath) {
      joyCoder.consecutiveMistakeCount++;
      // joyCoder.recordToolError('insert_content');
      await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('insert_content', 'path'));
      await joyCoder.saveCheckpoint();
      return;
    }

    if (!line) {
      joyCoder.consecutiveMistakeCount++;
      // joyCoder.recordToolError('insert_content');
      await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('insert_content', 'line'));
      await joyCoder.saveCheckpoint();
      return;
    }

    if (!content) {
      joyCoder.consecutiveMistakeCount++;
      // joyCoder.recordToolError('insert_content');
      await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('insert_content', 'content'));
      await joyCoder.saveCheckpoint();
      return;
    }

    const absolutePath = FileSystemHelper.resolveUri(joyCoder.cwd, relPath);
    const fileExists = await fileExistsAtPath(absolutePath);

    if (!fileExists) {
      joyCoder.consecutiveMistakeCount++;
      // joyCoder.recordToolError('insert_content');
      const displayPath = FileSystemHelper.getRemotePath(absolutePath);
      const formattedError = `File does not exist at path: ${displayPath}\n\n<error_details>\nThe specified file could not be found. Please verify the file path and try again.\n</error_details>`;
      await joyCoder.say('error', formattedError);
      await pushToolResult(joyCoder, block, formattedError);
      await joyCoder.saveCheckpoint();
      return;
    }

    const lineNumber = parseInt(line, 10);
    if (isNaN(lineNumber) || lineNumber < 0) {
      joyCoder.consecutiveMistakeCount++;
      // joyCoder.recordToolError('insert_content');
      await pushToolResult(
        joyCoder,
        block,
        formatResponse.toolError('Invalid line number. Must be a non-negative integer.')
      );
      await joyCoder.saveCheckpoint();
      return;
    }

    joyCoder.consecutiveMistakeCount = 0;
    //数据上报1
    // AI生成代码内容上报
    try {
      reportAction({
        actionCate: 'ai',
        actionType: ActionType.AutoGenerator,
        question: block.userContent?.map((item: any) => item?.text)?.join('\n'),
        result: content,
        conversationId: joyCoder.conversationId,
        model: GlobalState.get('openAiModelId'),
        startTime: new Date(),
        extendMsg: {
          type: 'yesButtonClicked',
          modeType: ((await joyCoder.providerRef.deref()?.getState()) || {}).mode,
          taskId: joyCoder.taskId,
          sessionId: joyCoder.sessionId,
        },
      });
      // AI生成代码采纳打标，由于vscode文件变动监听机制，需要在更新内容前打标
      AdoptResultCache.setRemote(
        content,
        GlobalState.get('openAiModelId'),
        AdoptResultCache.ADOPT_CODE_SOURCE.AUTO_CODE,
        joyCoder.conversationId
      );
    } catch (error) {
      console.error('%c [ reportAction->error ]-241', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    // Read the file
    const fileContent = await FileSystemHelper.readFile(absolutePath, 'utf8');
    joyCoder.diffViewProvider.editType = 'modify';
    joyCoder.diffViewProvider.originalContent = fileContent;
    const lines = fileContent.split('\n');

    const updatedContent = insertGroups(lines, [
      {
        index: lineNumber - 1,
        elements: content.split('\n'),
      },
    ]).join('\n');

    // Show changes in diff view
    if (!joyCoder.diffViewProvider.isEditing) {
      await joyCoder.ask('tool', JSON.stringify(sharedMessageProps), true).catch(() => {});
      // First open with original content
      await joyCoder.diffViewProvider.open(relPath);
      await joyCoder.diffViewProvider.update(fileContent, false);
      joyCoder.diffViewProvider.scrollToFirstDiff();
      await delay(200);
    }

    const diff = formatResponse.createPrettyPatch(relPath, fileContent, updatedContent);

    if (!diff) {
      await pushToolResult(joyCoder, block, `No changes needed for '${relPath}'`);
      await joyCoder.saveCheckpoint();
      return;
    }

    await joyCoder.diffViewProvider.update(updatedContent, true);

    const completeMessage = JSON.stringify({
      ...sharedMessageProps,
      diff,
      lineNumber: lineNumber,
    } satisfies JoyCoderSayTool);

    if (!joyCoder.shouldAutoApproveTool(block.name)) {
      const didApprove = await joyCoder
        .ask('tool', completeMessage, false)
        .then((response) => response.response === 'yesButtonClicked');
      if (!didApprove) {
        await joyCoder.diffViewProvider.revertChanges();
        await pushToolResult(joyCoder, block, 'Changes were rejected by the user.');
        await joyCoder.saveCheckpoint();
        return;
      }
    }
    // 用户采纳上报
    try {
      reportAction({
        actionCate: 'ai',
        actionType: ActionType.copy,
        question: block.userContent?.map((item: any) => item?.text)?.join('\n'),
        result: '', //此处惯例不上报内容，数据侧有处理逻辑认定为全部采纳newContent,
        conversationId: joyCoder.conversationId,
        model: GlobalState.get('openAiModelId'),
        startTime: new Date(),
        extendMsg: {
          type: 'yesButtonClicked',
          modeType: ((await joyCoder.providerRef.deref()?.getState()) || {}).mode,
          taskId: joyCoder.taskId,
          sessionId: joyCoder.sessionId,
        },
      });
    } catch (error) {
      console.error(
        '%c [ askApproval-reportAction-error ]-1946',
        'font-size:13px; background:pink; color:#bf2c9f;',
        error
      );
    }
    AdoptResultCache.clearAutoCodeAdopt();
    const { newProblemsMessage, userEdits, finalContent } = await joyCoder.diffViewProvider.saveChanges();

    // Track file edit operation
    if (relPath) {
      await joyCoder.fileContextTracker.trackFileContext(relPath, 'JoyCoder_edited');
    }

    joyCoder.didEditFile = true;

    if (!userEdits) {
      await pushToolResult(
        joyCoder,
        block,
        `The content was successfully inserted in ${FileSystemHelper.getRemotePath(
          relPath
        ).toPosix()} at line ${lineNumber}.${newProblemsMessage}`
      );
      await joyCoder.diffViewProvider.reset();
      await joyCoder.saveCheckpoint();
      return;
    }

    const userFeedbackDiff = JSON.stringify({
      tool: 'insertContent',
      path: getReadablePath(joyCoder.cwd, relPath),
      lineNumber: lineNumber,
      diff: userEdits,
    } satisfies JoyCoderSayTool);

    await joyCoder.say('user_feedback_diff', userFeedbackDiff);

    await pushToolResult(
      joyCoder,
      block,
      `The user made the following updates to your content:\n\n${userEdits}\n\n` +
        `The updated content has been successfully saved to ${FileSystemHelper.getRemotePath(
          relPath
        ).toPosix()}. Here is the full, updated content of the file:\n\n` +
        `<final_file_content path="${FileSystemHelper.getRemotePath(
          relPath
        ).toPosix()}">\n${finalContent}\n</final_file_content>\n\n` +
        `Please note:\n` +
        `1. You do not need to re-write the file with these changes, as they have already been applied.\n` +
        `2. Proceed with the task using this updated file content as the new baseline.\n` +
        `3. If the user's edits have addressed part of the task or changed the requirements, adjust your approach accordingly.` +
        `${newProblemsMessage}`
    );

    await joyCoder.diffViewProvider.reset();
    await joyCoder.saveCheckpoint();
  } catch (error) {
    await handleError(joyCoder, block, 'insert content', error);
    await joyCoder.diffViewProvider.reset();
    await joyCoder.saveCheckpoint();
  }
}

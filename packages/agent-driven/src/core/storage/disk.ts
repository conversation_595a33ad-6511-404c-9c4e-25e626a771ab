import * as path from 'path';
import * as vscode from 'vscode';
import fs from 'fs/promises';
import { fileExistsAtPath } from '../../utils/fs';

export type SecretKey =
  | 'apiKey'
  | 'openRouterApiKey'
  | 'awsAccessKey'
  | 'awsSecretKey'
  | 'awsSessionToken'
  | 'openAiApiKey'
  | 'geminiApiKey'
  | 'openAiNativeApiKey'
  | 'deepSeekApiKey'
  | 'requestyApiKey'
  | 'togetherApiKey'
  | 'qwenApiKey'
  | 'mistralApiKey'
  | 'authToken'
  | 'authNonce';
export type GlobalStateKey =
  | 'apiProvider'
  | 'customModes'
  | 'apiModelId'
  | 'awsRegion'
  | 'awsUseCrossRegionInference'
  | 'awsProfile'
  | 'awsUseProfile'
  | 'vertexProjectId'
  | 'vertexRegion'
  | 'lastShownAnnouncementId'
  | 'customInstructions'
  | 'taskHistory'
  | 'taskHistoryRemote'
  | 'openAiBaseUrl'
  | 'openAiModelId'
  | 'openAiModelInfo'
  | 'ollamaModelId'
  | 'ollamaBaseUrl'
  | 'lmStudioModelId'
  | 'lmStudioBaseUrl'
  | 'anthropicBaseUrl'
  | 'azureApiVersion'
  | 'openRouterModelId'
  | 'openRouterModelInfo'
  | 'autoApprovalSettings'
  | 'browserSettings'
  | 'chatSettings'
  | 'vsCodeLmModelSelector'
  | 'userInfo'
  | 'previousModeApiProvider'
  | 'previousModeModelId'
  | 'previousModeModelInfo'
  | 'liteLlmBaseUrl'
  | 'liteLlmModelId'
  | 'qwenApiLine'
  | 'requestyModelId'
  | 'togetherModelId'
  | 'fuzzyMatchThreshold'
  | 'diffEnabled'
  | 'selectionContext'
  | 'planActSeparateModelsSetting'
  | 'mode'
  | 'contextMenuInstructions'
  | 'customModePrompts'
  | 'customSupportPrompts'
  | 'experiments'
  | 'JoyCoder.autoCode.writeAction';
export interface FileMetadataEntry {
  path: string;
  record_state: 'active' | 'stale';
  record_source: 'read_tool' | 'user_edited' | 'JoyCoder_edited' | 'file_mentioned';
  JoyCoder_read_date: number | null;
  JoyCoder_edit_date: number | null;
  user_edit_date?: number | null;
}

export interface ModelMetadataEntry {
  ts: number;
  model_id: string;
  model_provider_id: string;
  mode: string;
}

export interface TaskMetadata {
  files_in_context: FileMetadataEntry[];
  model_usage: ModelMetadataEntry[];
}

export const GlobalFileNames = {
  apiConversationHistory: 'api_conversation_history.json',
  uiMessages: 'ui_messages.json',
  openRouterModels: 'openrouter_models.json',
  mcpSettings: 'joycoder-mcp.json',
  projectMcpSettings: 'mcp.json',
  JoyCodeRules: '.JoyCodeRules',
  contextHistory: 'context_history.json',
  taskMetadata: 'task_metadata.json',
  userPrompts: 'prompt.json',
  customModes: 'custom_modes.json',
};

export async function ensureTaskDirectoryExists(context: vscode.ExtensionContext, taskId: string): Promise<string> {
  const globalStoragePath = context.globalStorageUri.fsPath;
  const taskDir = path.join(globalStoragePath, 'tasks', taskId);
  await fs.mkdir(taskDir, { recursive: true });
  return taskDir;
}

export async function getTaskMetadata(context: vscode.ExtensionContext, taskId: string): Promise<TaskMetadata> {
  const filePath = path.join(await ensureTaskDirectoryExists(context, taskId), GlobalFileNames.taskMetadata);
  try {
    if (await fileExistsAtPath(filePath)) {
      return JSON.parse(await fs.readFile(filePath, 'utf8'));
    }
  } catch (error) {
    console.error('Failed to read task metadata:', error);
  }
  return { files_in_context: [], model_usage: [] };
}

export async function saveTaskMetadata(context: vscode.ExtensionContext, taskId: string, metadata: TaskMetadata) {
  try {
    const taskDir = await ensureTaskDirectoryExists(context, taskId);
    const filePath = path.join(taskDir, GlobalFileNames.taskMetadata);
    await fs.writeFile(filePath, JSON.stringify(metadata, null, 2));
  } catch (error) {
    console.error('Failed to save task metadata:', error);
  }
}

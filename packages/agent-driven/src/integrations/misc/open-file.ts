import * as path from 'path';
import * as os from 'os';
import * as vscode from 'vscode';
import { arePathsEqual } from '../../utils/path';
import { JoyCoderEditorMessageMap } from '../../adaptor/translate/message';
import { isRemoteEnvironment } from '../../utils/fs';

export async function openImage(dataUri: string) {
  const matches = dataUri.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);
  if (!matches) {
    vscode.window.showErrorMessage(JoyCoderEditorMessageMap['Invalid data URI format']);
    return;
  }
  const [, format, base64Data] = matches;
  const imageBuffer = Buffer.from(base64Data, 'base64');
  const tempFilePath = path.join(os.tmpdir(), `temp_image_${Date.now()}.${format}`);
  try {
    await vscode.workspace.fs.writeFile(vscode.Uri.file(tempFilePath), imageBuffer);
    await vscode.commands.executeCommand('vscode.open', vscode.Uri.file(tempFilePath));
  } catch (error) {
    vscode.window.showErrorMessage(`图片打开失败: ${error}`);
  }
}

export async function openFile(absolutePath: string) {
  try {
    let uri = vscode.Uri.parse(absolutePath);

    if (isRemoteEnvironment()) {
      // 获取工作区根路径
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders) {
        throw new Error('请打开工作空间或文件夹');
      }

      const workspaceUri = workspaceFolders[0].uri;
      let targetUri: vscode.Uri = uri;

      // 检查输入路径的格式并进行相应处理
      if (absolutePath.startsWith('vscode-remote://')) {
        // 如果已经是完整的远程URI，直接使用
        targetUri = vscode.Uri.parse(absolutePath);
      } else if (absolutePath.startsWith('/')) {
        // 如果是绝对路径，需要转换为远程URI
        // 检查是否是工作区内的路径
        if (absolutePath.startsWith(workspaceUri.path)) {
          // 路径已经包含工作区路径，直接构建远程URI
          targetUri = workspaceUri.with({ path: absolutePath });
        } else {
          // 路径不包含工作区路径，但可能是工作区内的绝对路径
          // 直接使用工作区的scheme和authority，但使用输入的path
          targetUri = workspaceUri.with({ path: absolutePath });
        }
      } else {
        // 相对路径，直接拼接到工作区
        targetUri = vscode.Uri.joinPath(workspaceUri, absolutePath);
      }

      // 检查文件是否存在
      if (!(await fileExists(targetUri))) {
        // 尝试不同的路径解析方式
        const alternatives: vscode.Uri[] = [];

        // 如果原始路径是绝对路径，尝试其他解析方式
        if (absolutePath.startsWith('/')) {
          // 尝试移除工作区路径前缀，作为相对路径处理
          const workspacePath = workspaceUri.path;
          if (absolutePath.startsWith(workspacePath)) {
            const relativePart = absolutePath.substring(workspacePath.length).replace(/^\/+/, '');
            if (relativePart) {
              alternatives.push(vscode.Uri.joinPath(workspaceUri, relativePart));
            }
          }

          // 尝试作为相对路径（移除开头的斜杠）
          const cleanPath = absolutePath.replace(/^\/+/, '');
          alternatives.push(vscode.Uri.joinPath(workspaceUri, cleanPath));
        }

        let foundUri: vscode.Uri | null = null;
        for (const altUri of alternatives) {
          if (await fileExists(altUri)) {
            foundUri = altUri;
            break;
          }
        }

        if (foundUri) {
          targetUri = foundUri;
        } else {
          throw new Error(
            `文件不存在: ${targetUri.toString()}。已尝试的路径: ${[targetUri, ...alternatives]
              .map((u) => u.toString())
              .join(', ')}`
          );
        }
      }

      uri = targetUri;
    } else {
      // 本地环境，检查文件是否存在
      try {
        await vscode.workspace.fs.stat(uri);
      } catch {
        throw new Error(`文件不存在: ${uri.fsPath}`);
      }
    }

    // Check if the document is already open in a tab group that's not in the active editor's column. If it is, then close it (if not dirty) so that we don't duplicate tabs
    try {
      for (const group of vscode.window.tabGroups.all) {
        const existingTab = group.tabs.find(
          (tab) => tab.input instanceof vscode.TabInputText && arePathsEqual(tab.input.uri.fsPath, uri.fsPath)
        );
        if (existingTab) {
          const activeColumn = vscode.window.activeTextEditor?.viewColumn;
          const tabColumn = vscode.window.tabGroups.all.find((group) => group.tabs.includes(existingTab))?.viewColumn;
          if (activeColumn && activeColumn !== tabColumn && !existingTab.isDirty) {
            await vscode.window.tabGroups.close(existingTab);
          }
          break;
        }
      }
    } catch {} // not essential, sometimes tab operations fail

    const document = await vscode.workspace.openTextDocument(uri);
    await vscode.window.showTextDocument(document, { preview: false });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    vscode.window.showErrorMessage(`${JoyCoderEditorMessageMap['Could not open file!']} 详细错误: ${errorMessage}`);
  }
}

// 辅助函数：检查文件是否存在
async function fileExists(uri: vscode.Uri): Promise<boolean> {
  try {
    await vscode.workspace.fs.stat(uri);
    return true;
  } catch {
    return false;
  }
}

import * as vscode from 'vscode';
import * as path from 'path';
import { createDirectoriesForFile, isRemoteEnvironment } from '../../utils/fs';
import { arePathsEqual } from '../../utils/path';
import { formatResponse } from '../../core/prompts/responses';
import { DecorationController } from './DecorationController';
import * as diff from 'diff';
import { diagnosticsToProblemsString, getNewDiagnostics } from '../diagnostics';
import { JoyCoderEditorMessageMap } from '../../adaptor/translate/message';
import { Uri } from 'vscode';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { getVscodeConfig } from '@joycoder/shared';
import stripBom from 'strip-bom';

export const DIFF_VIEW_URI_SCHEME = 'joycoder-diff';

export class DiffViewProvider {
  editType?: 'create' | 'modify';
  isEditing = false;
  originalContent: string | undefined;
  private createdDirs: string[] = [];
  private documentWasOpen = false;
  private relPath?: string;
  private newContent?: string;
  private activeDiffEditor?: vscode.TextEditor;
  private fadedOverlayController?: DecorationController;
  private activeLineController?: DecorationController;
  private streamedLines: string[] = [];
  private preDiagnostics: [vscode.Uri, vscode.Diagnostic[]][] = [];

  constructor(private cwd: string | Uri) {}
  async open(relPath: string): Promise<void> {
    this.relPath = relPath;
    const fileExists = this.editType === 'modify';
    const absolutePath = FileSystemHelper.resolveUri(this.cwd, relPath);
    this.isEditing = true;
    // 如果文件已经打开，确保在获取其内容之前它不是脏的（未保存的更改）
    if (fileExists) {
      const existingDocument = vscode.workspace.textDocuments.find((doc) =>
        arePathsEqual(doc.uri.fsPath, FileSystemHelper.getRemotePath(absolutePath))
      );

      if (existingDocument && existingDocument.isDirty) {
        await existingDocument.save();
      }
    }

    // 在编辑文件之前获取诊断信息，我们将在编辑后与之比较，以查看joycoder是否需要修复任何问题
    this.preDiagnostics = vscode.languages.getDiagnostics();

    if (fileExists) {
      this.originalContent = await FileSystemHelper.readFile(absolutePath, 'utf-8');
    } else {
      this.originalContent = '';
    }
    // 对于新文件，创建必要的目录并跟踪新创建的目录，以便在用户拒绝操作时删除它们
    this.createdDirs = await createDirectoriesForFile(absolutePath);
    // 确保在打开文件之前文件存在
    if (!fileExists) {
      // 创建文件
      await FileSystemHelper.writeFile(absolutePath, new TextEncoder().encode(''));
    }
    // if the file was already open, close it (must happen after showing the diff view since if it's the only tab the column will close)
    this.documentWasOpen = false;
    // close the tab if it's open (it's already saved above)
    const tabs = vscode.window.tabGroups.all
      .map((tg) => tg.tabs)
      .flat()
      .filter(
        (tab) =>
          tab.input instanceof vscode.TabInputText &&
          arePathsEqual(tab.input.uri.fsPath, FileSystemHelper.getRemotePath(absolutePath))
      );
    for (const tab of tabs) {
      if (!tab.isDirty) {
        await vscode.window.tabGroups.close(tab);
      }
      this.documentWasOpen = true;
    }
    try {
      this.activeDiffEditor = await this.openDiffEditor();
    } catch (error) {
      console.error(`Error opening diff editor: ${error}`);
      throw new Error(`打开diff窗口异常: ${error}`);
    }
    this.fadedOverlayController = new DecorationController('fadedOverlay', this.activeDiffEditor);
    this.activeLineController = new DecorationController('activeLine', this.activeDiffEditor);
    // Apply faded overlay to all lines initially
    this.fadedOverlayController.addLines(0, this.activeDiffEditor.document.lineCount);
    this.scrollEditorToLine(0); // will this crash for new files?
    this.streamedLines = [];
  }

  async update(accumulatedContent: string, isFinal: boolean) {
    if (!this.relPath || !this.activeLineController || !this.fadedOverlayController) {
      throw new Error(JoyCoderEditorMessageMap['Required values not set']);
    }
    // --- Fix to prevent duplicate BOM ---
    // Strip potential BOM from incoming content. VS Code's `applyEdit` might implicitly handle the BOM
    // when replacing from the start (0,0), and we want to avoid duplication.
    // Final BOM is handled in `saveChanges`.
    if (accumulatedContent.startsWith('\ufeff')) {
      accumulatedContent = accumulatedContent.slice(1); // Remove the BOM character
    }
    this.newContent = accumulatedContent;
    const accumulatedLines = accumulatedContent.split('\n');
    if (!isFinal) {
      accumulatedLines.pop(); // remove the last partial line only if it's not the final update
    }
    const diffEditor = this.activeDiffEditor;
    const document = diffEditor?.document;
    if (!diffEditor || !document) {
      throw new Error(JoyCoderEditorMessageMap['User closed text editor, unable to edit file...']);
    }

    // Place cursor at the beginning of the diff editor to keep it out of the way of the stream animation
    const beginningOfDocument = new vscode.Position(0, 0);
    diffEditor.selection = new vscode.Selection(beginningOfDocument, beginningOfDocument);

    const endLine = accumulatedLines.length;
    // Replace all content up to the current line with accumulated lines
    // This is necessary (as compared to inserting one line at a time) to handle cases where html tags on previous lines are auto closed for example
    const edit = new vscode.WorkspaceEdit();
    const rangeToReplace = new vscode.Range(0, 0, endLine, 0);
    const contentToReplace = accumulatedLines.slice(0, endLine + 1).join('\n') + '\n';
    edit.replace(document.uri, rangeToReplace, contentToReplace);
    await vscode.workspace.applyEdit(edit);
    // Update decorations
    this.activeLineController.setActiveLine(endLine);
    this.fadedOverlayController.updateOverlayAfterLine(endLine, document.lineCount);
    // Scroll to the current line.
    const ranges = this.activeDiffEditor?.visibleRanges;
    if (ranges && ranges.length > 0 && ranges[0].start.line < endLine && ranges[0].end.line > endLine) {
      this.scrollEditorToLine(endLine);
    }

    // Update the streamedLines with the new accumulated content
    this.streamedLines = accumulatedLines;
    if (isFinal) {
      // Handle any remaining lines if the new content is shorter than the original
      if (this.streamedLines.length < document.lineCount) {
        const edit = new vscode.WorkspaceEdit();
        edit.delete(document.uri, new vscode.Range(this.streamedLines.length, 0, document.lineCount, 0));
        await vscode.workspace.applyEdit(edit);
      }
      // Add empty last line if original content had one
      const hasEmptyLastLine = this.originalContent?.endsWith('\n');
      if (hasEmptyLastLine) {
        const accumulatedLines = accumulatedContent.split('\n');
        if (accumulatedLines[accumulatedLines.length - 1] !== '') {
          accumulatedContent += '\n';
        }
      }
      // Apply the final content.
      const finalEdit = new vscode.WorkspaceEdit();

      finalEdit.replace(
        document.uri,
        new vscode.Range(0, 0, document.lineCount, 0),
        this.stripAllBOMs(accumulatedContent)
      );

      await vscode.workspace.applyEdit(finalEdit);
      // Clear all decorations at the end (before applying final edit)
      this.fadedOverlayController.clear();
      this.activeLineController.clear();
    }
  }

  async saveChanges(): Promise<{
    newProblemsMessage: string | undefined;
    userEdits: string | undefined;
    autoFormattingEdits: string | undefined;
    finalContent: string | undefined;
  }> {
    if (!this.relPath || !this.newContent || !this.activeDiffEditor) {
      return {
        newProblemsMessage: undefined,
        userEdits: undefined,
        autoFormattingEdits: undefined,
        finalContent: undefined,
      };
    }
    const absolutePath = FileSystemHelper.resolveUri(this.cwd, this.relPath);
    const updatedDocument = this.activeDiffEditor.document;

    // get the contents before save operation which may do auto-formatting
    const preSaveContent = updatedDocument.getText();

    if (updatedDocument.isDirty) {
      await updatedDocument.save();
    }
    // get text after save in case there is any auto-formatting done by the editor
    await vscode.window.showTextDocument(
      absolutePath instanceof vscode.Uri ? absolutePath : vscode.Uri.file(absolutePath),
      {
        preview: false,
      }
    );
    await this.closeAllDiffViews();

    /*
		Getting diagnostics before and after the file edit is a better approach than
		automatically tracking problems in real-time. This method ensures we only
		report new problems that are a direct result of this specific edit.
		Since these are new problems resulting from JoyCoder's edit, we know they're
		directly related to the work he's doing. This eliminates the risk of JoyCoder
		going off-task or getting distracted by unrelated issues, which was a problem
		with the previous auto-debug approach. Some users' machines may be slow to
		update diagnostics, so this approach provides a good balance between automation
		and avoiding potential issues where JoyCoder might get stuck in loops due to
		outdated problem information. If no new problems show up by the time the user
		accepts the changes, they can always debug later using the '@problems' mention.
		This way, JoyCoder only becomes aware of new problems resulting from his edits
		and can address them accordingly. If problems don't change immediately after
		applying a fix, JoyCoder won't be notified, which is generally fine since the
		initial fix is usually correct and it may just take time for linters to catch up.
		*/
    const postSaveContent = updatedDocument.getText();
    const postDiagnostics = vscode.languages.getDiagnostics();
    const newProblems = diagnosticsToProblemsString(
      getNewDiagnostics(this.preDiagnostics, postDiagnostics),
      [
        vscode.DiagnosticSeverity.Error, // only including errors since warnings can be distracting (if user wants to fix warnings they can use the @problems mention)
      ],
      FileSystemHelper.getRemotePath(this.cwd)
    ); // will be empty string if no errors
    const newProblemsMessage =
      newProblems.length > 0 ? `\n\nNew problems detected after saving the file:\n${newProblems}` : '';

    // If the edited content has different EOL characters, we don't want to show a diff with all the EOL differences.
    const newContentEOL = this.newContent.includes('\r\n') ? '\r\n' : '\n';
    const normalizedPreSaveContent = preSaveContent.replace(/\r\n|\n/g, newContentEOL).trimEnd() + newContentEOL; // trimEnd to fix issue where editor adds in extra new line automatically
    const normalizedPostSaveContent = postSaveContent.replace(/\r\n|\n/g, newContentEOL).trimEnd() + newContentEOL; // this is the final content we return to the model to use as the new baseline for future edits
    // just in case the new content has a mix of varying EOL characters
    const normalizedNewContent = this.newContent.replace(/\r\n|\n/g, newContentEOL).trimEnd() + newContentEOL;

    let userEdits: string | undefined;
    if (normalizedPreSaveContent !== normalizedNewContent) {
      // user made changes before approving edit. let the model know about user made changes (not including post-save auto-formatting changes)
      userEdits = formatResponse.createPrettyPatch(
        this.relPath.toPosix(),
        normalizedNewContent,
        normalizedPreSaveContent
      );
      // return { newProblemsMessage, userEdits, finalContent: normalizedPostSaveContent }
    } else {
      // no changes to joycoder's edits
      // return { newProblemsMessage, userEdits: undefined, finalContent: normalizedPostSaveContent }
    }

    let autoFormattingEdits: string | undefined;
    if (normalizedPreSaveContent !== normalizedPostSaveContent) {
      // auto-formatting was done by the editor
      autoFormattingEdits = formatResponse.createPrettyPatch(
        FileSystemHelper.getRemotePath(this.relPath).toPosix(),
        normalizedPreSaveContent,
        normalizedPostSaveContent
      );
    }

    return {
      newProblemsMessage,
      userEdits,
      autoFormattingEdits,
      finalContent: normalizedPostSaveContent,
    };
  }

  async revertChanges(): Promise<void> {
    if (!this.relPath || !this.activeDiffEditor) {
      return;
    }
    const fileExists = this.editType === 'modify';
    const updatedDocument = this.activeDiffEditor.document;
    const absolutePath = FileSystemHelper.resolve(this.cwd, this.relPath);
    if (!fileExists) {
      if (updatedDocument.isDirty) {
        await updatedDocument.save();
      }
      await this.closeAllDiffViews();
      const absePath = FileSystemHelper.resolveUri(this.cwd, this.relPath);
      await FileSystemHelper.unlink(absePath);
      // Remove only the directories we created, in reverse order
      for (let i = this.createdDirs.length - 1; i >= 0; i--) {
        await FileSystemHelper.rmdir(this.createdDirs[i]);
        console.log(`目录 ${this.createdDirs[i]} 已被删除。`);
      }
      console.log(`文件 ${absolutePath} 已被删除。`);
    } else {
      // revert document
      const edit = new vscode.WorkspaceEdit();
      const fullRange = new vscode.Range(
        updatedDocument.positionAt(0),
        updatedDocument.positionAt(updatedDocument.getText().length)
      );
      edit.replace(updatedDocument.uri, fullRange, this.originalContent ?? '');
      // Apply the edit and save, since contents shouldnt have changed this wont show in local history unless of course the user made changes and saved during the edit
      await vscode.workspace.applyEdit(edit);
      await updatedDocument.save();
      console.log(`文件 ${absolutePath} 已被恢复到其原始内容。`);
      if (this.documentWasOpen) {
        const uri = FileSystemHelper.getUri(FileSystemHelper.resolveUri(this.cwd, this.relPath));
        console.log('diff###', isRemoteEnvironment(), uri);
        await vscode.window.showTextDocument(uri, {
          preview: false,
        });
      }
      await this.closeAllDiffViews();
    }

    // edit is done
    await this.reset();
  }

  private async closeAllDiffViews() {
    const tabs = vscode.window.tabGroups.all
      .flatMap((tg) => tg.tabs)
      .filter(
        (tab) => tab.input instanceof vscode.TabInputTextDiff && tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME
      );
    for (const tab of tabs) {
      // trying to close dirty views results in save popup
      if (!tab.isDirty) {
        await vscode.window.tabGroups.close(tab);
      }
    }
  }

  private async openDiffEditor(): Promise<vscode.TextEditor> {
    if (!this.relPath) {
      throw new Error(JoyCoderEditorMessageMap['No file path set']);
    }

    // 获取文件扩展名
    const fileExt = path.extname(this.relPath).toLowerCase();

    // 对于SVG文件，使用直接替换而不打开差异编辑器
    if (fileExt === '.svg') {
      try {
        // 创建必要的目录
        if (this.editType === 'create') {
          const dirPath = path.dirname(FileSystemHelper.resolve(this.cwd, this.relPath));
          await FileSystemHelper.mkdir(dirPath, { recursive: true });
        }

        // 直接写入文件内容
        const absolutePath = FileSystemHelper.resolveUri(this.cwd, this.relPath);
        await FileSystemHelper.writeFile(absolutePath, this.newContent || '');

        // 创建一个临时编辑器以满足返回类型要求
        const uri = FileSystemHelper.getUri(FileSystemHelper.resolveUri(this.cwd, this.relPath));
        const document = await vscode.workspace.openTextDocument(uri);
        return await vscode.window.showTextDocument(document);
      } catch (error) {
        console.error('处理SVG文件时出错:', error);
        throw new Error(`处理SVG文件时出错: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // 原有的差异编辑器逻辑
    let uri: vscode.Uri;
    if (this.cwd instanceof vscode.Uri) {
      uri = vscode.Uri.joinPath(this.cwd, this.relPath);
    } else {
      // 在远程环境中，需要正确处理 URI
      const resolvedPath = FileSystemHelper.resolveUri(this.cwd, this.relPath);
      uri = FileSystemHelper.getUri(resolvedPath);
    }

    // 检查是否已有相同的差异编辑器打开
    const diffTab = vscode.window.tabGroups.all
      .flatMap((group) => group.tabs)
      .find(
        (tab) =>
          tab.input instanceof vscode.TabInputTextDiff &&
          tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME &&
          arePathsEqual(tab.input.modified.fsPath, FileSystemHelper.getRemotePath(uri))
      );
    if (diffTab && diffTab.input instanceof vscode.TabInputTextDiff) {
      const editor = await vscode.window.showTextDocument(diffTab.input.modified);
      return editor;
    }

    // Open new diff editor
    return new Promise<vscode.TextEditor>((resolve, reject) => {
      const fileName = FileSystemHelper.basename(uri);
      const fileExists = this.editType === 'modify';
      const disposable = vscode.window.onDidChangeActiveTextEditor((editor) => {
        if (
          editor &&
          arePathsEqual(FileSystemHelper.getRemotePath(editor.document.uri), FileSystemHelper.getRemotePath(uri))
        ) {
          disposable.dispose();
          resolve(editor);
        }
      });
      vscode.commands.executeCommand(
        'vscode.diff',
        vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${fileName}`).with({
          query: Buffer.from(this.originalContent ?? '').toString('base64'),
        }),
        uri,
        `${fileName}: ${fileExists ? '原文件 ↔ JoyCoder修改的文件' : '新建文件'} (编辑)`,
        // `${fileName}: ${fileExists ? "Original ↔ JoyCoder's Changes" : 'New File'} (Editable)`,
        {
          preserveFocus: true,
        }
      );
      // This may happen on very slow machines ie project idx
      setTimeout(() => {
        disposable.dispose();
        reject(new Error(JoyCoderEditorMessageMap['Failed to open diff editor, please try again...']));
      }, 10_000);
    });
  }

  private scrollEditorToLine(line: number) {
    if (this.activeDiffEditor) {
      const scrollLine = line + 4;
      this.activeDiffEditor.revealRange(
        new vscode.Range(scrollLine, 0, scrollLine, 0),
        vscode.TextEditorRevealType.InCenter
      );
    }
  }

  scrollToFirstDiff() {
    if (!this.activeDiffEditor) {
      return;
    }
    const currentContent = this.activeDiffEditor.document.getText();
    const diffs = diff.diffLines(this.originalContent || '', currentContent);
    let lineCount = 0;
    for (const part of diffs) {
      if (part.added || part.removed) {
        // Found the first diff, scroll to it
        this.activeDiffEditor.revealRange(
          new vscode.Range(lineCount, 0, lineCount, 0),
          vscode.TextEditorRevealType.InCenter
        );
        return;
      }
      if (!part.removed) {
        lineCount += part.count || 0;
      }
    }
  }

  private stripAllBOMs(input: string): string {
    let result = input;
    let previous;

    do {
      previous = result;
      result = stripBom(result);
    } while (result !== previous);

    return result;
  }

  // close editor if open?
  async reset() {
    await this.closeAllDiffViews();
    this.editType = undefined;
    this.isEditing = false;
    this.originalContent = undefined;
    this.createdDirs = [];
    this.documentWasOpen = false;
    this.activeDiffEditor = undefined;
    this.fadedOverlayController = undefined;
    this.activeLineController = undefined;
    this.streamedLines = [];
    this.preDiagnostics = [];
  }
}

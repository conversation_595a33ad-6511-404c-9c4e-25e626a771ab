{"name": "joycoder-business", "displayName": "JoyCoder", "description": "专注于AI和IDE的研发工具集，为您提供高效、智能、专业的编码辅助服务", "publisher": "JoyCoder", "version": "2.9.0", "private": true, "engines": {"vscode": "^1.74.0", "node": ">=16.14.0", "pnpm": ">=8"}, "categories": ["Other"], "keywords": ["chatgpt", "openai", "copilot", "<PERSON><PERSON>", "jd", "JoyCoder", "ai", "autocomplete", "inline completion"], "icon": "assets/logo.png", "activationEvents": ["*"], "main": "./dist/extension.js", "sideEffects": false, "scripts": {"watch:web": "pnpm -C ../../ run watch:web", "watch:extension": "webpack --watch --mode development", "watch": "rimraf ./dist && npm-run-all -p watch:web watch:extension", "build:web": "pnpm -C ../../ run build:web", "build": "webpack --mode production", "package": "pnpm build && pnpm build:web && vsce package --allow-star-activation --no-dependencies --allow-missing-repository", "publish": "vsce publish --allow-star-activation --no-dependencies --allow-missing-repository"}, "contributes": {"icons": {"completion-icon": {"description": "completion icon", "default": {"fontPath": "assets/iconfont/iconfont.woff", "fontCharacter": "\\e800"}}, "completion-disabled-icon": {"description": "completion disabled icon", "default": {"fontPath": "assets/iconfont/disabled-iconfont.woff", "fontCharacter": "\\e800"}}}, "inlineCompletions": [{"language": "*", "disallowedPrecedingCharacters": [".", ":", ";"], "provider": {"resolveProvider": true}}], "viewsContainers": {"activitybar": [{"id": "JoyCoder", "title": "JoyCoder", "icon": "assets/activitybar.svg"}]}, "views": {"JoyCoder": [{"type": "webview", "id": "<PERSON><PERSON><PERSON>r-left-view", "name": "", "when": "true"}]}, "viewsWelcome": [{"view": "<PERSON><PERSON><PERSON>r-left-view", "contents": ""}], "commands": [{"command": "JoyCoder.code-completions-setting", "title": "JoyCoder: 预测补全设置", "icon": {"light": "assets/light/completion.svg", "dark": "assets/dark/completion.svg"}}, {"command": "JoyCoder.config.setting", "title": "JoyCoder: 设置", "icon": {"light": "assets/light/setting.svg", "dark": "assets/dark/setting.svg"}}, {"command": "JoyCoder.code-completions-init", "title": "补全代码出现时回调"}, {"command": "JoyCoder.openHomePage", "title": "JoyCoder: 官方文档", "shortTitle": "官方文档"}, {"command": "JoyCoder.openConfigPage", "title": "插件配置", "shortTitle": "插件设置"}, {"command": "JoyCoder.openGlobalKeybindings", "title": "快捷键设置", "shortTitle": "快捷键设置"}, {"command": "JoyCoder.ClearGlobalState", "title": "清除缓存", "shortTitle": "清除缓存"}, {"command": "JoyCoder.downloadHistory", "title": "导出历史会话", "shortTitle": "导出历史会话"}, {"command": "JoyCoder.fixbug.clearDiffHighlights", "title": "fixbug: 清除diff高亮"}, {"command": "JoyCoder.fixbug.acceptChange", "title": "fixbug: 采纳"}, {"command": "JoyCoder.fixbug.rejectChange", "title": "fixbug: 拒绝"}, {"command": "JoyCoder.LogOut", "title": "退出登录"}, {"command": "JoyCoder.ai.inlineChat.enter", "title": "提交"}, {"command": "JoyCoder.ai.inlineChat.delete", "title": "关闭"}, {"command": "JoyCoder.ai.inlineChat.abort", "title": "中断问答"}, {"command": "JoyCoder.ai.terminal.explain", "title": "JoyCoder: 解释选中内容"}, {"command": "JoyCoder.codelens.optimization", "title": "代码优化"}, {"command": "JoyCoder.codelens.functionComment", "title": "函数注释"}, {"command": "JoyCoder.codelens.comment", "title": "逐行注释"}, {"command": "JoyCoder.codelens.reconstruction", "title": "代码重构"}, {"title": "解释代码", "command": "JoyCoder.codelens.explain"}, {"title": "单元测试", "command": "JoyCoder.codelens.test"}, {"command": "JoyCoder.ai.chat.explain", "title": "解释代码"}, {"command": "JoyCoder.ai.chat.broken", "title": "报错分析"}, {"command": "JoyCoder.ai.chat.refactor", "title": "优化代码"}, {"command": "JoyCoder.ai.chat.test", "title": "生成单测"}, {"command": "JoyCoder.ai.chat.doc", "title": "文档生成"}, {"command": "JoyCoder.ai.chat.review", "title": "代码评审"}, {"command": "JoyCoder.ai.code.review", "title": "JoyCode代码评审", "icon": {"light": "assets/code-review.svg", "dark": "assets/code-review.svg"}}, {"command": "JoyCoder.ai.chat.autoFill", "title": "代码自动填充"}, {"command": "JoyCoder.code-completions", "title": "预测补全"}, {"command": "JoyCoder.completion.formatAccept", "title": "代码预测采纳执行事件"}, {"command": "JoyCoder.completion.get-new-completion", "title": "代码预测自动执行事件"}, {"command": "JoyCoder.code-completion.manually", "title": "代码预测主动触发"}, {"command": "JoyCoder.codelens.optimization", "title": "代码优化"}, {"command": "JoyCoder.codelens.functionComment", "title": "函数注释"}, {"command": "JoyCoder.codelens.comment", "title": "逐行注释"}, {"command": "JoyCoder.codelens.reconstruction", "title": "代码重构"}, {"title": "解释代码", "command": "JoyCoder.codelens.explain"}, {"title": "单元测试", "command": "JoyCoder.codelens.test"}, {"command": "JoyCoder.ai.chat.transformCodeJS", "title": "转为JavaScript"}, {"command": "JoyCoder.ai.chat.transformCodeTS", "title": "转为TypeScript"}, {"command": "JoyCoder.ai.chat.optimizedHTML", "title": "HTML语义优化"}, {"command": "JoyCoder.ai.chat.transformCodeVue", "title": "转为Vue2.x"}, {"command": "JoyCoder.ai.chat.transformCodeVue2TS", "title": "转为Vue2.x-TS"}, {"command": "JoyCoder.ai.chat.transformCodeVue3", "title": "转为Vue3.x"}, {"command": "JoyCoder.ai.chat.transformCodeVue3TS", "title": "转为Vue3.x-TS"}, {"command": "JoyCoder.ai.chat.transformCodeReact", "title": "转为React"}, {"command": "JoyCoder.ai.chat.transformCodeReactTS", "title": "转为React-TS"}, {"command": "JoyCoder.ai.chat.transformCodeTaro", "title": "转为Taro"}, {"command": "JoyCoder.ai.chat.transformCodeTaroVueTS", "title": "转为Taro-Vue-TS"}, {"command": "JoyCoder.ai.chat.transformCodeTaroReactTS", "title": "转为Taro-React-TS"}, {"command": "JoyCoder.ai.chat.transformCodeHarmonyArkTS", "title": "转为鸿蒙-ArkTS"}], "menus": {"terminal/context": [{"command": "JoyCoder.ai.terminal.explain", "group": "navigation@01"}], "comments/commentThread/context": [{"command": "JoyCoder.ai.inlineChat.enter", "group": "inline", "when": "commentController == joycoder-inline-chat"}], "comments/commentThread/title": [{"command": "JoyCoder.ai.inlineChat.abort", "group": "navigation@1", "when": "commentController == joycoder-inline-chat && when.JoyCoder.inlineChat.streaming"}, {"command": "JoyCoder.ai.inlineChat.delete", "group": "navigation@2", "when": "commentController == joycoder-inline-chat"}], "view/title": [{"command": "JoyCoder.code-completions-setting", "when": "view == JoyCoder-left-view", "group": "navigation@1"}, {"command": "JoyCoder.config.setting", "when": "view == JoyCoder-left-view", "group": "navigation@2"}, {"command": "JoyCoder.openConfigPage", "when": "view == JoyCoder-left-view", "group": "overflow2@1"}, {"command": "JoyCoder.openGlobalKeybindings", "when": "view == JoyCoder-left-view", "group": "overflow2@2"}, {"command": "JoyCoder.ClearGlobalState", "when": "view == JoyCoder-left-view", "group": "overflow2@3"}, {"command": "JoyCoder.LogOut", "when": "view == JoyCoder-left-view", "group": "overflow2@4"}, {"command": "JoyCoder.downloadHistory", "when": "view == JoyCoder-left-view", "group": "overflow2@5"}, {"command": "JoyCoder.openHomePage", "when": "view == JoyCoder-left-view", "group": "overflow3@1"}], "scm/title": [{"command": "JoyCoder.ai.code.review", "group": "navigation@6"}], "editor/title": [], "explorer/context": [], "editor/context": [{"submenu": "JoyCoder.ai.chat.context", "group": "AJoyCoder@1"}, {"submenu": "JoyCoder.ai.chat.transformCode", "group": "AJoyCoder@2"}], "JoyCoder.ai.chat.context": [{"command": "JoyCoder.ai.chat.explain", "group": "AJoyCoder@1"}, {"command": "JoyCoder.ai.chat.broken", "group": "AJoyCoder@2"}, {"command": "JoyCoder.ai.chat.refactor", "group": "AJoyCoder@3"}, {"command": "JoyCoder.ai.chat.test", "group": "AJoyCoder@4"}, {"command": "JoyCoder.ai.chat.doc", "group": "AJoyCoder@5"}, {"command": "JoyCoder.ai.chat.review", "group": "<PERSON><PERSON><PERSON><PERSON><PERSON>@6"}, {"command": "JoyCoder.ai.chat.autoFill", "group": "BJoyCoder@1", "when": "editorHasSelection"}], "JoyCoder.ai.chat.transformCode": [{"command": "JoyCoder.ai.chat.transformCodeVue", "group": "AJoyCoder@1"}, {"command": "JoyCoder.ai.chat.transformCodeVue2TS", "group": "AJoyCoder@2"}, {"command": "JoyCoder.ai.chat.transformCodeVue3", "group": "AJoyCoder@3"}, {"command": "JoyCoder.ai.chat.transformCodeVue3TS", "group": "AJoyCoder@4"}, {"command": "JoyCoder.ai.chat.transformCodeReact", "group": "AJoyCoder@5"}, {"command": "JoyCoder.ai.chat.transformCodeReactTS", "group": "<PERSON><PERSON><PERSON><PERSON><PERSON>@6"}, {"command": "JoyCoder.ai.chat.transformCodeTaro", "group": "AJoyCoder@7"}, {"command": "JoyCoder.ai.chat.transformCodeTaroVueTS", "group": "AJoyCoder@8"}, {"command": "JoyCoder.ai.chat.transformCodeTaroReactTS", "group": "<PERSON>oyCoder@9"}, {"command": "JoyCoder.ai.chat.transformCodeHarmonyArkTS", "group": "<PERSON><PERSON>Coder@10"}, {"command": "JoyCoder.ai.chat.transformCodeTS", "group": "<PERSON><PERSON>Coder@11"}, {"command": "JoyCoder.ai.chat.transformCodeJS", "group": "<PERSON><PERSON><PERSON><PERSON><PERSON>@12"}, {"command": "JoyCoder.ai.chat.optimizedHTML", "group": "<PERSON><PERSON><PERSON><PERSON><PERSON>@13"}]}, "submenus": [{"id": "JoyCoder.ai.chat.context", "group": "AJoyCoder", "label": "JoyCode: AI助手"}, {"id": "JoyCoder.ai.chat.transformCode", "group": "AJoyCoder", "label": "JoyCode: 代码翻译"}], "configuration": {"type": "object", "title": "JoyCode", "properties": {"JoyCoder.config.server.url": {"type": "string", "default": "http://joycoder-man.jd-eit.com/coder/", "markdownDescription": "JoyCoder服务端HTTP地址(IP)"}, "JoyCoder.config.chatModel": {"type": "string", "default": "JoyCoder-Lite", "markdownDescription": "调用JoyCoder的大模型版本"}, "JoyCoder.config.codeCompletionsMaxTokens": {"type": "number", "default": 1000, "markdownDescription": "预测补全续写最大Tokens"}, "JoyCoder.config.codeCompletionsTemperature": {"type": "number", "default": 0, "markdownDescription": "预测补全续写模型温度0-1之间"}, "JoyCoder.config.codeCompletionsMaxLines": {"type": "number", "default": 5, "markdownDescription": "预测补全续写最大行数"}, "JoyCoder.config.completionsRejectTimes": {"type": "number", "default": 20, "markdownDescription": "连续不采纳次数超过设置次数关闭预测功能"}, "JoyCoder.config.codeCompletionsMaxTimes": {"type": "number", "default": 4000, "markdownDescription": "预测补全续写超时时间，默认4000毫秒"}, "JoyCoder.config.codeCompletionsMoreContext": {"type": "boolean", "markdownDescription": "启用跨文件感知，识别同目录及打开的相似文件", "default": false}, "JoyCoder.config.codeCompletionsFormat": {"type": "boolean", "markdownDescription": "启用代码补全格式化", "default": false}, "JoyCoder.config.codeCompletionsGenTask": {"type": "string", "default": "LINE", "enum": ["LINE", "BLOCK", "FUNCTION", "TIME_OUT"], "enumItemLabels": ["整行优先", "代码块模式", "函数优先", "速度优先"], "enumDescriptions": ["按行生成代码，效果好，适用广，速度快", "优先生成代码块，速度稍慢", "生成函数级代码，速度较慢", "根据超时时间生成代码，速度取决于设置超时时间"], "markdownDescription": "设置JoyCoder代码预测生成场景"}, "JoyCoder.config.codeReview.commitMessageStyle": {"type": "string", "default": "GIT_SCHEMA", "enum": ["GIT_SCHEMA", "BRANCH_SCHEMA", "AUTO", "DIY"], "enumItemLabels": ["标准模式", "分支模式", "变更摘要"], "enumDescriptions": ["根据 Conventional Commits 规范生成；\n示例：fix(chat): 接口错误", "生成变更摘要 + 分支名称;\n示例：fix: 接口错误[分支名称]", "生成变更摘要；\n示例：fix connection error"], "markdownDescription": "JoyCoder生成Commit Message配置"}, "JoyCoder.config.codeLens-row-menus": {"type": "boolean", "markdownDescription": "是否启用启用行间菜单", "default": true}, "JoyCoder.config.chatModels.list": {"type": "array", "default": [], "markdownDescription": "AI助手相关模型配置", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "定义ChatGPT的模型名称"}, "description": {"type": "string", "description": "定义ChatGPT的模型描述"}, "chatApiModel": {"type": "string", "description": "定义ChatGPT的API模型"}, "maxTotalTokens": {"type": "number", "description": "定义ChatGPT的模型最大总tokens"}}}}}}, "keybindings": [{"command": "JoyCoder.ai.inlineChat.create", "key": "shift+alt+k", "mac": "shift+alt+k", "when": "editorTextFocus"}, {"command": "JoyCoder.ai.inlineChat.create", "key": "ctrl+l", "mac": "cmd+l", "when": "editorTextFocus"}, {"command": "JoyCoder.ai.inlineChat.delete", "key": "escape", "when": "when.JoyCoder.RunEsc"}, {"command": "editor.action.inlineSuggest.commit", "key": "Enter", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.commit", "key": "Tab", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && editorFocus &&!editorR<PERSON><PERSON>ly"}, {"command": "editor.action.inlineSuggest.hide", "key": "Esc", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.showNext", "key": "shift+alt+down", "mac": "shift+alt+down", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.showPrevious", "key": "shift+alt+up", "mac": "shift+alt+up", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.acceptNextLine", "mac": "shift+tab", "key": "shift+tab", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && editorFocus &&!editorR<PERSON><PERSON>ly"}, {"command": "editor.action.inlineSuggest.acceptNextWord", "mac": "alt+.", "key": "alt+.", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && editorFocus &&!editorR<PERSON><PERSON>ly"}, {"command": "editor.action.inlineSuggest.trigger", "key": "shift+alt+.", "mac": "shift+alt+.", "when": "editorTextFocus"}, {"command": "JoyCoder.code-completion.manually", "key": "shift+alt+right", "mac": "shift+alt+right", "when": "editorTextFocus"}], "snippets": []}, "dependencies": {"@joycoder/plugin-base-ai": "workspace:*", "@joycoder/plugin-base-code-completion": "workspace:*", "@joycoder/plugin-base-code-review": "workspace:*", "@joycoder/shared": "workspace:*", "@joycoder/version": "workspace:*", "gpt-tokens": "^1.3.3"}}
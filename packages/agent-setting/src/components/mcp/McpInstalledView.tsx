
import React from 'react';

type McpMarketViewProps = {};

const McpInstalledView: React.FC<McpMarketViewProps> = () => {
  // 模拟 MCP 服务数据
  const mcpServices = [
    { id: 1, name: 'Fetch 网页内容抓取', description: '高德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务。', icon: 'Fe', isInstalled: false, creator: '<PERSON>', downloads: 1000 },
    { id: 2, name: 'Amap Maps', description: '高德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务，包括地理编码、逆地理编码、IP定位、天气查询、骑行路径规划、步行路径规划、驾车路径规划、公交路径规划、...', icon: '56px', isInstalled: true, creator: '<PERSON>', downloads: 5000 },
    { id: 3, name: 'markdownify-mcp', description: '高德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务，包括地理编码、逆地理编码、IP定位、天气查询、骑行路径规划、步行路径规划、驾车路径规划。', icon: 'M', isInstalled: false, creator: 'Bob <PERSON>', downloads: 3000 },
  ];

  const installedServices = mcpServices.filter(service => service.isInstalled);

  const handleConfigure = (serviceId: number) => {
    // 实现配置逻辑
    console.log(`Configuring service ${serviceId}`);
  };

  const renderInstalledContent = () => (
    <div className="installed-content">
      {installedServices.map(service => (
        <div key={service.id} className="service-item">
          <div className="service-icon">
            {service.icon}
          </div>
          <div className="service-info">
            <h3 className="service-name">{service.name}</h3>
            <p className="service-description">
              {service.description}
            </p>
            <div className="service-creator">
              @{service.creator}
            </div>
          </div>
          <div className="button-group">
            <button className="mcp-button installed">

              已安装
            </button>
            <button
              className="mcp-button configure"
              onClick={() => handleConfigure(service.id)}
            >
              设置
            </button>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div>
      {renderInstalledContent()}
    </div>
  );
};

export default McpInstalledView;

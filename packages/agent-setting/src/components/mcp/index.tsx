import './index.scss';
import McpMarketView from './McpMarketView';
import McpInstalledView from './McpInstalledView';
import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useState, CSSProperties } from 'react';
import { CommonMessage } from '../../messages/messageTypes';

export default function McpMarket() {
  const [activeTab, setActiveTab] = useState('market');
  const aaa = () => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'settiing-logout',
        data: {},
      },
    });
  };
  return (
    <div className="mcp-container">
      <div className="mcp-content">
        <div className="mcp-header">
          <h2 className="mcp-title">MCP Servers</h2>
          <span className="mcp-tutorial">
            <QuestionCircleOutlined className="mcp-tutorial-icon" />
            使用教程
          </span>
        </div>

        <div className="mcp-tabs">
          <div className="mcp-tab-buttons">
            <TabButton active={activeTab === 'market'} onClick={() => setActiveTab('market')}>
              MCP市场
            </TabButton>
            <TabButton active={activeTab === 'installed'} onClick={() => setActiveTab('installed')}>
              已安装
            </TabButton>
          </div>
          <div className="mcp-tab-content">
            {activeTab === 'market' && <McpMarketView />}
            {activeTab === 'installed' && <McpInstalledView />}
          </div>
        </div>
      </div>
    </div>
  );
}

interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
}

const TabButton: React.FC<TabButtonProps> = React.memo(({ active, onClick, children }) => {
  return (
    <div onClick={onClick} aria-selected={active} role="tab" className={`tab-button ${active ? 'active' : ''}`}>
      {children}
      {active && <div className="active-indicator" />}
    </div>
  );
});

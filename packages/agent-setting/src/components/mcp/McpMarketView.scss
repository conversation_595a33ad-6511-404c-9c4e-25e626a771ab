.mcp-market-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--vscode-editor-background);
  color: var(--vscode-foreground);
  position: relative;

  &__content {
    flex: 1;
    overflow: auto;
    padding: 20px;
  }

  &__popup {
    position: fixed;
    left: 0;
    top: 0;
    width: 320px;
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-widget-border);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    overflow: hidden;
    color: var(--vscode-foreground);

    &-header {
      width: 100%;
      height: 56px;
      padding: 12px;
      border-bottom: 1px solid var(--vscode-widget-border);
      h3 {
        color: var(--vscode-foreground);
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        line-height: 32px;
      }
    }

    &-content {
      width: 100%;
      height: 270px;
      padding: 12px;
      overflow-y: auto;
      p {
        color: var(--vscode-descriptionForeground);
        margin: 8px 0;
        font-size: 14px;
        line-height: 1.4;
      }
    }
  }

  &__search {
    width: 100%;
    margin-bottom: 20px;
  }

  &__options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 13px;
    font-family: var(--vscode-font-family);
    font-weight: normal;
    opacity: 0.8;
  }

  &__service-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  &__service-item {
    display: flex;
    align-items: stretch;
    position: relative;
  }

  &__service-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    font-size: 18px;
    font-weight: bold;
    flex-shrink: 0;
  }

  &__service-logo,
  &__service-default-logo {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__service-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &__service-name {
    display: flex;
    margin: 0;
    font-size: 13px;
    font-weight: 600;
    color: var(--vscode-foreground);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__service-description {
    margin: 5px 0;
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    line-height: 18px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  &__service-creator {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    line-height: 18px;
  }

  &__service-button-container {
    display: flex;
    align-items: center;
    margin-left: 15px;
  }
}

.mcp-button {
  height: 28px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  padding: 0;
  transition: background-color 0.3s;
  color: rgba(24,24,27,1);
  display: flex;
  justify-content: center;
  align-items: center;

  &__mcp-button-group {
    display: flex;
  }

  &__install {
    width: 49px;
    background: rgba(240,247,255,1);
    color: rgba(24,24,27,1);

    &:hover {
     // background: darken(rgba(240,247,255,1), 5%);
    background: darken(rgba(240,247,255,1), 5%);

    }
  }

  &__installed {
    width: 72px;
    height: 28px;
    margin-right: 8px;
    background: rgba(48,48,53,1);
    border-radius: 4px;
    color: var(--vscode-foreground);
    &:hover {
      background-color: var(--vscode-editor-background);
      opacity: 0.8;
    }
  }

  &__configure {
    width: 49px;
    background: rgba(240,247,255,1);
    color: rgba(24,24,27,1);

    &:hover {
      background: darken(rgba(240,247,255,1), 5%);
    }
  }

  &__deployment-type {
    width: 62px;
    height: 20px;
    background: rgba(48,48,53,1);
    border-radius: 2px;
    color: var(--vscode-foreground);
    font-size: 10px;
    margin-left: 8px;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      margin-right: 4px;
    }

    .icon-juli,
    .icon-bendibushu {
      display: inline-block;
    }
  }

  &__checkmark {
    margin-right: 4px;
    font-size: 14px;
    line-height: 0;
    display: flex;
    align-items: center;
  }
}

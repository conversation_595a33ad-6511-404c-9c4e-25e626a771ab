import React, { useState, useRef } from 'react';
//import { VSCodeTextField } from '@vscode/webview-ui-toolkit/react';
import { CheckOutlined } from '@ant-design/icons';
import './McpMarketView.scss';
import { colors, generateColorForText } from './Avatar';
import { McpTransportType, McpDeploymentType, getTransportTypeString, getDeploymentTypeString } from './McpTypes';
declare const vscode: any;

type McpMarketViewProps = {
  // 如果需要props，可以在这里定义
};

const McpMarketView: React.FC<McpMarketViewProps> = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [quickConfig, setQuickConfig] = useState(true);
  const [hoveredService, setHoveredService] = useState<any | null>(null);
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const popupRef = useRef<HTMLDivElement>(null);

  // 模拟 MCP 服务数据
  const mcpServices = [
    { serviceId: '1', name: 'Fetch 网页内容抓取',displayNmae:'123', description: '高德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务。', logoUrl:'https://joyagent.jd.com/autobots/interface/oss/downloadFile?ossName=uploadFile/jd_6ad448cb35902/96c5cb9a64c48aacd868c2649f5207d5ee7bca81ec70b56bb7318e2350656a45.jpg' ,uploader:'John Doe', updateTime: 'yyyy-MM-dd HH:mm',depType:1, transType:McpTransportType.STDIO, deploymentType: McpDeploymentType.LOCAL, isInstalled: false, downloads: 1000 },
    { serviceId: '2', name: 'Amap Maps',displayNmae:'123', description: '德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务，包括地理编码、逆地理编码、IP定位、天气查询、骑行路径规划、步行路径规划、驾车路径规划、公交路径规划、德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务，包括地理编码、逆地理编码、IP定位、天气查询、骑行路径规划、步行路径规划、驾车路径规划、公交路径规划、德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务，包括地理编码、逆地理编码、IP定位、天气查询、骑行路径规划、步行路径规划、驾车路径规划、公交路径规划、德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务，包括地理编码、逆地理编码、IP定位、天气查询、骑行路径规划、步行路径规划、驾车路径规划、公交路径规划、高德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务，包括地理编码、逆地理编码、IP定位、天气查询、骑行路径规划、步行路径规划、驾车路径规划、公交路径规划、', logoUrl:'https://files.codelife.cc/website/github.svg', uploader:'Jane Smith',updateTime: '2025-06-10 10:20:11', depType:1,transType:McpTransportType.SSE, deploymentType: McpDeploymentType.HOSTED, isInstalled: true, downloads: 5000 },
    { serviceId: '3', name: 'tFmarkdownify-mcp', displayNmae:'123',description: '高德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务，包括地理编码、逆地理编码、IP定位、天气查询、骑行路径规划、步行路径规划、驾车路径规划。',logoUrl:'', uploader:'Bob Johnson',updateTime: '2025-06-10 10:20:11',depType:2,transType:McpTransportType.STDIO, deploymentType: McpDeploymentType.LOCAL, isInstalled: false, downloads: 3000 },
    { serviceId: '4', name: 'AFmarkdownify-mcp', displayNmae:'123',description: '高德地图MCP Server现已覆盖12大核心接口，提供全场景覆盖的地理信息服务，包括地理编码、逆地理编码、IP定位、天气查询、骑行路径规划、步行路径规划、驾车路径规划。',logoUrl:'', uploader:'Bob Johnson',updateTime: '2025-06-10 10:20:11', depType:1,transType:McpTransportType.STREAMABLE, deploymentType: McpDeploymentType.HOSTED, isInstalled: false, downloads: 3000 },
  ];

  const filteredServices = mcpServices.filter(service =>
    service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    service.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleInstall = (serviceId: string) => {
    // 实现安装逻辑
    console.log(`Installing service ${serviceId}`);
    //vscode.postMessage({ type: 'updateServerConnections', text: `install:${serviceId}` });
  };

  const handleConfigure = (serviceId: string) => {
    // 实现配置逻辑
    console.log(`Configuring service ${serviceId}`);
    //vscode.postMessage({ type: 'updateServerConnections', text: `configure:${serviceId}` });
  };
  const getDefaultName = (service: any) => {
    let defaultName = '';
    if (service.displayName && service.displayName.length > 0) {
      defaultName = service.displayName.slice(0, 2);
    } else if (service.name && service.name.length > 0) {
      defaultName = service.name.slice(0, 2);
    } else {
      defaultName = 'e';
    }
    const finalColor = generateColorForText(defaultName);
    return { defaultName, finalColor };
  }

  const renderLogo = (service: any) => {
    if (service.logoUrl) {
      return <img src={service.logoUrl} alt={service.name} className="mcp-market-view__service-logo" />;
    } else {
      const { defaultName, finalColor } = getDefaultName(service);
      return (
        <div
          className="mcp-market-view__service-default-logo"
          style={{ backgroundColor: colors[finalColor] }}
        >
          {defaultName.toUpperCase()}
        </div>
      );
    }
  }

  const renderPopup = () => {
    if (!isPopupVisible || !hoveredService) return null;
    return (
      <div
        ref={popupRef}
        className="mcp-market-view__popup"
        style={{
          top: `${hoveredService.popupPosition?.top}px`,
          left: `${hoveredService.popupPosition?.left}px`
        }}
        onMouseEnter={() => setIsPopupVisible(true)}
        onMouseLeave={() => {
          setIsPopupVisible(false);
          setHoveredService(null);
        }}
      >
        <div className="mcp-market-view__popup-header">
          <h3>{hoveredService.displayName || hoveredService.name}</h3>
        </div>
        <div className="mcp-market-view__popup-content">
          <p>更新于: {hoveredService.updateTime}</p>
          <p>{hoveredService.description}</p>
        </div>
      </div>
    );
  };

  const handleMouseEnter = (service: any, event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    const mouseX = event.clientX + scrollLeft;
    const mouseY = event.clientY + scrollTop;

    setHoveredService({
      ...service,
      popupPosition: {
        top: mouseY,
        left: mouseX - 330 // 弹出框宽度 (320px) + 一些间距 (10px)
      }
    });
    setIsPopupVisible(true);
  };

  const handleMouseLeave = () => {
    setTimeout(() => {
      if (!isPopupVisible) {
        setHoveredService(null);
      }
    }, 100);
  };

  const renderMarketContent = () => (
    <div className="mcp-market-view__content">
      {renderPopup()}
      {/* <VSCodeTextField
        className="mcp-market-view__search"
        placeholder="搜索 MCP 服务"
        value={searchQuery}
        onChange={(e) => setSearchQuery((e.target as HTMLInputElement).value)}
      /> */}

      <div className="mcp-market-view__options">
        <div className="mcp-market-view__manual-config">
          想要添加市场以外的 MCP Servers？
          <a href="#" onClick={() => vscode.postMessage({ type: 'openMcpSettings' })}>
            手动配置
          </a>
        </div>
        <label className="mcp-market-view__quick-config">
          <input
            type="checkbox"
            checked={quickConfig}
            onChange={(e:any) => setQuickConfig(e.target.checked)}
          />
          快速配置
        </label>
      </div>

      <div className="mcp-market-view__service-list">
        {filteredServices.map(service => (
          <div
            key={service.serviceId}
            className="mcp-market-view__service-item"
          >
            <div
              onMouseEnter={(e) => handleMouseEnter(service, e)}
              onMouseLeave={() => setIsPopupVisible(false)}
              className="mcp-market-view__service-icon">
              {renderLogo(service)}
            </div>
            <div
              onMouseEnter={(e) => handleMouseEnter(service, e)}
              onMouseLeave={() => setIsPopupVisible(false)}
              className="mcp-market-view__service-info">

              <h3 className="mcp-market-view__service-name">
                {service.name}
                <button className="mcp-button mcp-button__deployment-type">
                  {service.deploymentType === McpDeploymentType.HOSTED ? (
                    <span className="icon iconfont icon-juli" style={{color: '#247FFF'}}/>
                  ) : (
                    <span className="icon iconfont icon-bendibushu" style={{color: 'rgba(44,179,56,1)' }}/>
                  )}
                  {getDeploymentTypeString(service.deploymentType)}
                </button>
              </h3>
              <p className="mcp-market-view__service-description">
                {service.description}
              </p>
              <div className="mcp-market-view__service-creator">
                @{service.uploader}
              </div>
            </div>
            <div className="mcp-market-view__service-button-container">
              {service.isInstalled ? (
                <div className="mcp-button mcp-button-group">
                  <button
                    className="mcp-button mcp-button__installed"
                    onClick={() => handleInstall(service.serviceId)}
                  >
                    <span className="icon iconfont icon-duigou"/>
                    已安装
                  </button>
                  <button
                    className="mcp-button mcp-button__configure"
                    onClick={() => handleConfigure(service.serviceId)}
                  >
                    设置
                  </button>
                </div>
              ) : (
                <button
                  className="mcp-button mcp-button__install"
                  onClick={() => handleInstall(service.serviceId)}
                >
                  安装
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="mcp-market-view">
      {renderMarketContent()}
    </div>
  );
};

export default McpMarketView;

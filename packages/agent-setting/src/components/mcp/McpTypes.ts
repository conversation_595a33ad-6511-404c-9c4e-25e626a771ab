export enum McpTransportType {
  STDIO = 1,
  SSE = 2,
  STREAMABLE = 3,
}

export enum McpDeploymentType {
  LOCAL = 1,
  HOSTED = 2,
}

export function getTransportTypeString(type: McpTransportType): string {
  switch (type) {
    case McpTransportType.STDIO:
      return 'STDIO';
    case McpTransportType.SSE:
      return 'SSE';
    case McpTransportType.STREAMABLE:
      return 'STREAMABLE';
    default:
      return 'Unknown';
  }
}

export function getDeploymentTypeString(type: McpDeploymentType): string {
  switch (type) {
    case McpDeploymentType.LOCAL:
      return 'Local';
    case McpDeploymentType.HOSTED:
      return 'Hosted';
    default:
      return 'Unknown';
  }
}

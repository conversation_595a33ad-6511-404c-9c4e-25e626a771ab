.mcp-container {
  height: 824px;
  margin-top: 80px;
  display: flex;
  flex-direction: column;
  background-color: var(--vscode-editor-background);
  color: var(--vscode-foreground);
}

.mcp-content {
  padding: 24px;
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.mcp-header {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mcp-title {
  margin: 0;
  font-size: 24px;
  font-family: var(--vscode-font-family);
  font-weight: 600;
  text-align: left;
  color: var(--vscode-foreground);
}

.mcp-tutorial {
  height: 18px;
  font-size: 12px;
  font-family: var(--vscode-font-family);
  font-weight: normal;
  color: var(--vscode-foreground);
  opacity: 0.5;
}

.mcp-tutorial-icon {
  margin-right: 4px;
}

.mcp-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mcp-tab-buttons {
  display: flex;
  height: 32px;
  margin-bottom: 12px;
}

.mcp-tab-content {
  flex: 1;
  overflow: auto;
}

.tab-button {
  border: none;
  cursor: pointer;
  position: relative;
  font-family: var(--vscode-font-family);
  width: 58px;
  font-size: 13px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  background-color: var(--vscode-editor-background);

  &.active {
    font-weight: 600;
    color: var(--vscode-foreground);
  }

  &:not(.active) {
    font-weight: normal;
    color: var(--vscode-foreground);
    opacity: 0.5;
  }

  .active-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 2px;
    background: rgba(36, 127, 255, 1);
  }
}

.installed-content {
  display: flex;
  flex-direction: column;
  padding-top: 12px;

  .service-item {
    display: flex;
    align-items: flex-start;
    overflow: hidden;
    margin-bottom: 20px;

    .service-icon {
      width: 40px;
      height: 40px;
      background-color: var(--vscode-button-background);
      color: var(--vscode-button-foreground);
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      margin-right: 15px;
      font-size: 18px;
      font-weight: bold;
      flex-shrink: 0;
    }

    .service-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .service-name {
        margin: 0;
        height: 20px;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .service-description {
        margin: 5px 0;
        font-size: 12px;
        color: var(--vscode-foreground);
        height: 36px;
        line-height: 18px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .service-creator {
        font-size: 12px;
        color: var(--vscode-foreground);
        height: 18px;
        line-height: 18px;
      }
    }

    .configure-button {
      align-self: center;
      flex-shrink: 0;
    }
  }
}

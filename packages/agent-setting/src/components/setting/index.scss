.mt-44 {
  margin-top: 44px !important;
}
.mt-10 {
  margin-top: 10px !important;
}
.mt-12 {
  margin-top: 12px !important;
}
.mt-20 {
  margin-top: 20px !important;
}
.mt-16 {
  margin-top: 16px !important;
}
.mt-18 {
  margin-top: 18px !important;
}
.mb-18 {
  margin-bottom: 18px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}
.mb-16 {
  margin-bottom: 16px !important;
}
.v-h {
  overflow: hidden;
}
.hidden {
  display: none;
}
.mt-8 {
  margin-top: 8px !important;
}
.mb-8 {
  margin-bottom: 8px !important;
}
.mb-4 {
  margin-bottom: 4px !important;
}
.mb-2 {
  margin-bottom: 2px !important;
}
.mt-26 {
  margin-top: 26px !important;
}
.mr-10 {
  margin-right: 9px;
}
.mr-4 {
  margin-right: 4px;
}
.w-88 {
  width: 88px;
}
.l-20 {
  line-height: 20px !important;
}
.fs-12 {
  font-size: 12px !important;
}
.joycoder-dark-checkbox-wrapper:hover .joycoder-dark-checkbox-inner,
.joycoder-dark-checkbox:hover .joycoder-dark-checkbox-inner,
.joycoder-dark-checkbox-input:focus + .joycoder-dark-checkbox-inner {
  border-color: #FFFFFFCC;
}
.joycoder-dark-select:not(.joycoder-dark-select-disabled):hover .joycoder-dark-select-selector {
  border-color: #FFFFFFCC;
}
.joycoder-dark-progress-inner, .joycoder-dark-progress-status-active .joycoder-dark-progress-bg::before, .joycoder-dark-progress-success-bg, .joycoder-dark-progress-bg {
  border-radius: 0;
}
.joycoder-dark-progress-success-bg, .joycoder-dark-progress-bg {
  background-color: #247FFFFF;
}
.joycoder-dark-btn-round.joycoder-dark-btn-sm {
  border-radius: 4px;
  font-size: 12px;
  height: 28px;
  line-height: 28px;
}
.joycoder-setting {
  font-family: 'PingFang SC' !important;
  font-weight: 600;
  &-content {
    padding: 0 8px;
    position: fixed;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 101;
    overflow: auto;
  }
  &-header {
    height: 44px;
    line-height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    position: fixed;
    padding: 0 16px;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
  }
  &-close {
    cursor: pointer;
  }
  &-textarea {
    width: 100%;
    border: 1px solid #303035FF !important;
    border-radius: 4px;
    font-size: 12px;
    color: #FFFFFFCC;
  }
  &-box {
    overflow: hidden;
    padding: 0;
    background-color: transparent;
    border-radius: 0;
    border-bottom: 1px solid #303035ff;
    &-title {
      font-weight: 600;
      text-align: left;
      line-height: 28px;
      height: 28px;
      margin: 0;
      font-size: 15px;
      color: #FFFFFFCC;
    }
    &-text {
      font-weight: normal;
      font-size: 12px;
      color: #FFFFFF80;
      line-height: 32px;
      &.joycoder-flex {
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
    }
    &-msg {
      line-height: 28px;
      color: #FFFFFFCC;
      font-size: 12px;
      padding-left: 8px;
      background: rgba(39,39,43,1);
      border: 1px solid rgba(48,48,53,1);
      border-radius: 6px;
    }
    &-label {
      display: block;
      height: 32px;
      line-height: 32px;
    }
    &-radios {
      margin-top: 8px !important;
    }
    &-radio {
      background: rgba(255, 255, 255, 0.1) !important;
      border-radius: 22px !important;
      margin: 0 12px 0 0 !important;
      width: 80px !important;
      height: 32px !important;
      font-size: 12px;
      &.active {
        border: 1px solid #6abe39 !important;
        color: #6abe39 !important;
      }
    }
    &-radio::before {
      display: none;
      background-color: transparent !important;
    }
    &-context {
      .joycoder-dark-checkbox-inner,
      .joycoder-light-checkbox-inner {
        border-color: #FFFFFFCC;
        // background-color: #FFFFFFCC;
      }
      .joycoder-dark-checkbox-checked .joycoder-dark-checkbox-inner {
        border-color: #FFFFFFCC;
        background-color: #FFFFFFCC;
      }
      .joycoder-dark-checkbox-checked .joycoder-dark-checkbox-inner::after {
        border: 2px solid #000000;
        border-top: 0;
        border-left: 0;
      }
    }
    &-tips {
      font-size: 12px;
      font-weight: normal;
      height: 20px;
      opacity: 0.6;
      margin-bottom: 26px;
      color: #FFFFFF80;
      padding-left: 20px;
    }
    &-tip {
      font-size: 12px;
      font-weight: normal;
      min-height: 18px;
      line-height: 18px;
      padding: 0 0 0 24px;
      opacity: 0.6;
      overflow: hidden;
    }
    &-wrap {
      width: 160px;
      padding-top: 4px;
      line-height: 32px;
      height: 32px;
      font-size: 12px !important;
      &-select {
        width: calc(100% - 16px);
        overflow: hidden;
        .joycoder-dark-select-selector,
        .joycoder-light-select-selector {
          border-color: #303035FF;
        }
      }
    }
    &-message {
      font-size: 10px;
      opacity: 0.8;
      // scale: 0.8;
      text-align: left;
    }
  }
  &-box:last-child {
    border-bottom: none;
  }
  &-btns {
    height: 32px;
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.joycoder-dark-input-border {
  border: 1px solid #303035FF !important;
}
// 通用
.joycoder-setting-content {
  margin: 0;
  padding: 0;
  bottom: 0;
  .setting-tabs {
    padding: 0 8px 16px;
    height: 100%;
    .joycoder-dark-tabs-nav {
      padding-top: 16px;
    }
    &-item {
      padding: 0 24px;
      &-title {
        height: 80px;
        line-height: 80px;
        font-size: 24px;
        color: #FFFFFFCC;
      }
    }
  }
  .setting-title {
    font-size: 16px;
    line-height: 40px;
    height: 40px;
    padding-left: 10px;
    border-bottom: 1px solid rgba(48,48,53,1);
  }

  .joycoder-dark-slider-with-marks {
    margin-bottom: 45px;
  }
}
// 黑色主题
.joycoder-dark {
  &-select {
    font-size: 12px;
  }
  &-checkbox {
    &-input {
      background-color: #202023;
      color: #202023;
    }
    &-input:hover {
      border: 1px solid #FFFFFF80;
    }
  }
  &-btn-primary, &-btn-primary:hover, &-btn-primary:focus {
    background: #303035FF;
    border-color: #303035FF;
    font-size: 12px;
    color: #FFFFFFCC;
    margin-right: 8px;
    border-radius: 4px;
  }
  &-tabs {
    font-size: 13px !important;
    color: #FFFFFF80 !important;
    &-ink-bar {
      display: none;
    }
  }
  &-tabs-tab {
    height: 28px;
    line-height: 28px;
    font-size: 13px!important;
    width: 144px;
    text-align: left;
    padding: 0!important;
    padding-left: 9px!important;
    color: #FFFFFF80;
    margin-bottom: 2px;
    margin-right: 8px;
    &-active {
      background-color: #303035;
      .joycoder-dark-tabs-tab-btn {
        color: #FFFFFFCC;
        text-shadow:none;
      }
    }
  }
  &-slider-dot {
    opacity: 0;
  }
  &-slider-mark-text {
    font-weight: 400;
    color: rgba(255, 255, 255, 1) !important;
    font-size: 12px;
  }
  .joycoder-dark-slider {
    margin-left: 10px;
  }
  .joycoder-setting-header {
    background: rgba(43, 45, 48, 1);
  }
  .joycoder-setting-content {
  background: #202023;
  color: rgba(255, 255, 255, 1);
  .joycoder-dark-radio-button-wrapper-checked:not(
      [class*=' joycoder-dark-radio-button-wrapper-disabled']
    ).joycoder-dark-radio-button-wrapper {
      border-color: #FFFFFF80;
      border-width: 1px;
    }
  }
}
// 白色主题
.joycoder-light {
  .joycoder-setting-content {
    background: #f3f3f3;
    color: #333;
  }
  .joycoder-setting-header {
    background: #f3f3f3;
  }
  &-slider-dot {
    opacity: 0;
  }
  &-slider-mark-text {
    font-weight: 400;
    color: #333 !important;
    font-size: 12px;
  }
  .joycode-light-slider {
    margin-left: 10px;
  }
}

@keyframes loadingDots {
  0% { content: ''; }
  25% { content: '.'; }
  50% { content: '..'; }
  75% { content: '...'; }
  100% { content: ''; }
}
.joycoder-loading-dots {
  font-size: 11px;
}
.joycoder-loading-dots::after {
  content: '.';
  animation: loadingDots 1.5s infinite steps(1, end);
  display: inline-block;
  width: 1em;
  text-align: left;
}


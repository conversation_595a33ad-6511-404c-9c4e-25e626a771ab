import React, { useState, useEffect } from 'react';
import {
  CloseOutlined,
  Ellip<PERSON>Outlined,
  FontColorsOutlined,
  InfoCircleOutlined,
  MinusOutlined,
  OrderedListOutlined,
  PlusOutlined,
  CaretRightOutlined,
  DeleteOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Select,
  Slider,
  SliderSingleProps,
  Input,
  Space,
  Tag,
  message,
  Progress,
  Popconfirm,
  Tabs,
  Modal,
  Tooltip,
} from 'antd';
import type { TabsProps } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox/Checkbox';
const { TextArea } = Input;
import { useHandleMessage } from '../../hooks/useHandleMessage';
import { CommonMessage } from '../../messages/messageTypes';
import { CodebaseIndexingStatus } from '@joycoder/shared/src/types/codebase';
import { ACTION_METADATA } from '@joycoder/agent-driven/web-agent/src/utils/chatContant';
import {
  AutoApprovalSettings,
  DEFAULT_AUTO_APPROVAL_SETTINGS,
} from '@joycoder/agent-driven/src/shared/AutoApprovalSettings';
import McpMarket from '../mcp';
import './index.scss';

export default function Setting() {
  const [isShowCodeLens, setShowCodeLens] = useState(false);
  const [isCommitCodeReview, setCommitCodeReview] = useState(false);
  // const [isCodeReviewImmediate, setCodeReviewImmediate] = useState(false);
  const [isErrorLine, setErrorLine] = useState(false);
  const [isShenYi, setShenYi] = useState(false);
  const [completionDelay, setCompletionDelay] = useState(1.2);
  const [genTask, setGenTask] = useState('LINE');
  const [codeLens, setCodeLens] = useState<string[]>(['functionComment']);
  const [commitMessage, setCommitMessage] = useState('GIT_SCHEMA');
  // const [writeAction, setWriteAction] = useState('all');
  const [showCodebase, setShowCodebase] = useState(true);
  const [hasWorkspaceFolder, setHasWorkspaceFolder] = useState(false);
  const [codebaseIndexingProgress, setCodebaseIndexingProgress] = useState(0);
  const [codebaseIndexingStatus, setCodebaseIndexingStatus] = useState<CodebaseIndexingStatus>(
    CodebaseIndexingStatus.PREPARING
  );
  const [codebaseIndexingButtonDisabled, setCodebaseIndexingButtonDisabled] = useState(false);
  const [customInstructions, setCustomInstructions] = useState('');
  const [projectRuleName, setProjectRuleName] = useState('');
  const [ruleType, setRuleType] = useState('Always');
  const [filePatterns, setFilePatterns] = useState<string[]>([]);
  const [filePatternInput, setFilePatternInput] = useState('');
  const [codeCompletionsMoreContext, setCompletionsMoreContext] = useState(false);
  const [userName, setUserName] = useState('');
  const [isShowAddRule, setIsShowAddRule] = useState(false);
  const [modalTitle, setModalTitle] = useState('新建规则');

  const [autoApprovalSettings, setAutoApprovalSettings] =
    useState<AutoApprovalSettings>(DEFAULT_AUTO_APPROVAL_SETTINGS);
  const [styles] = useState({
    track: {
      // background: 'linear-gradient(90deg, rgba(0,180,196,1) 0%,rgba(36,123,255,1) 100%)',
      backgroundColor: '#247FFFFF',
    },
    tracks: {
      background: '#247FFFFF',
      border: `2px solid rgba(255,255,255,1)`,
    },
    rail: {
      background: `rgba(48,48,53,1)`,
    },
  });
  const [marks] = useState<SliderSingleProps['marks']>({
    0.3: '0.3',
    0.6: '0.6',
    0.9: '0.9',
    1.2: '1.2',
    1.5: '1.5',
    2: '2',
    3: '3',
  });

  useEffect(() => {
    message.info('加载完成，发送消息');

    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-connect-verify',
        data: {
          msg: 'connected',
        },
      },
    });
  }, []);

  // const closeSetting = () => {
  //   console.log('关闭页面');
  // };
  /**
   * 预测补全-按行还是函数等
   * @param e - 单选框变化事件对象。
   */
  const onGenTaskClick = (value: string) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'genTask',
          genTask: value,
        },
      },
    });
    setGenTask(value);
  };
  /**
   * 预测补全延迟
   * @param value - 新的完成延迟值
   */
  const handleCompletionDelayChange = (value: number) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'completionDelay',
          completionDelay: value,
        },
      },
    });
    setCompletionDelay(value);
  };
  /**
   * 跨文件感知
   * @param {CheckboxChangeEvent} e - 复选框变化事件。
   */
  const onCompletionsMoreContextChange = (e: CheckboxChangeEvent) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'completionsMoreContext',
          completionsMoreContext: e.target.checked,
        },
      },
    });
    setCompletionsMoreContext(e.target.checked);
  };
  /**
   * 增量代码评审。
   * @param e - 复选框更改事件。
   */
  const handleCommitCodeReview = (e: CheckboxChangeEvent) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'commitCodeReview',
          commitCodeReview: e.target.checked,
        },
      },
    });
    setCommitCodeReview(e.target.checked);
  };
  /**
   * 处理主动代码评审的复选框事件
   * @param e - 复选框事件对象
   */
  // const handleCodeReviewImmediate = (e: CheckboxChangeEvent) => {
  //   vscode.postMessage<CommonMessage>({
  //     type: 'COMMON',
  //     payload: {
  //       type: 'chatgpt-setting-info',
  //       data: {
  //         type: 'codeReviewImmediate',
  //         codeReviewImmediate: e.target.checked,
  //       },
  //     },
  //   });
  //   setCodeReviewImmediate(e.target.checked);
  // };
  const onAutoApprovalSettingsChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    const actionId = e.target.value;
    const newActions = {
      ...autoApprovalSettings.actions,
      [actionId]: checked,
    };
    const currentSettings = {
      ...autoApprovalSettings,
      actions: newActions,
      enabled: DEFAULT_AUTO_APPROVAL_SETTINGS.enabled,
    };
    // 发送消息给 vscode
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'autoApprovalSettings',
          autoApprovalSettings: currentSettings,
        },
      },
    });
    setAutoApprovalSettings(currentSettings);
  };
  /**
   * 处理errorLIne状态。
   * @param e - 复选框变化事件。
   */
  const handleErrorLine = (e: CheckboxChangeEvent) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'errorLine',
          errorLine: e.target.checked,
        },
      },
    });
    setErrorLine(e.target.checked);
  };
  /**
   * 神医开启
   * @param e - 复选框变化事件
   */
  const handleShenYi = (e: CheckboxChangeEvent) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'isShenYi',
          isShenYi: e.target.checked,
        },
      },
    });
    setShenYi(e.target.checked);
  };
  /**
   * 处理行间菜单
   * @param checkedValues - 选中的复选框值数组
   */
  const onCodeLensChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    const value = e.target.value;
    // 使用展开运算符创建 codeLensList 的副本，以避免直接修改状态
    let codeLensList = [...codeLens];
    if (checked) {
      // 使用副本更新数组
      if (!codeLensList.includes(value)) {
        codeLensList.push(value);
      }
    } else {
      // 使用副本更新数组
      codeLensList = codeLensList.filter((item) => item !== value);
    }
    // 发送消息给 vscode
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'codeLens',
          codeLens: codeLensList,
        },
      },
    });
    // 使用 setCodeLens 更新状态
    setCodeLens(codeLensList);
  };

  /**
   * 处理CommitMessage
   * @param value - 提交信息的值
   */
  const handleCommitMessageChange = (value: string) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'commitMessage',
          commitMessage: value,
        },
      },
    });
    setCommitMessage(value);
  };
  /**
   * 处理CommitMessage
   * @param value - 提交信息的值
   */
  // const handleWriteActionChange = (value: string) => {
  //   vscode.postMessage<CommonMessage>({
  //     type: 'COMMON',
  //     payload: {
  //       type: 'chatgpt-setting-info',
  //       data: {
  //         type: 'writeAction',
  //         writeAction: value,
  //       },
  //     },
  //   });
  //   setWriteAction(value); // 自动化编程写入模式;
  // };

  /**
   * 处理AI规则配置
   * @param e - 输入框变化事件
   */
  const handleCustomInstructionsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCustomInstructions(value);
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'customInstructions',
          customInstructions: value,
        },
      },
    });
  };

  /**
   * 处理项目AI规则名称输入
   * @param e - 输入框变化事件
   */
  const handleProjectRuleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setProjectRuleName(e.target.value);
  };

  /**
   * 处理规则类型变更
   * @param value - 选中的规则类型
   */
  const handleRuleTypeChange = (value: string) => {
    setRuleType(value);
  };

  /**
   * 处理文件模式输入变更
   * @param e - 输入框变化事件
   */
  const handleFilePatternInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilePatternInput(e.target.value);
  };

  /**
   * 添加文件模式
   */
  const handleAddFilePattern = () => {
    if (filePatternInput.trim() === '') {
      return;
    }
    setFilePatterns([...filePatterns, filePatternInput.trim()]);
    setFilePatternInput('');
  };

  /**
   * 删除文件模式
   * @param pattern - 要删除的文件模式
   */
  const handleRemoveFilePattern = (pattern: string) => {
    setFilePatterns(filePatterns.filter((p) => p !== pattern));
  };

  /**
   * 处理创建项目AI规则
   */
  const handleCreateProjectRule = () => {
    if (projectRuleName.trim() === '') {
      vscode.postMessage<CommonMessage>({
        type: 'COMMON',
        payload: {
          type: 'chatgpt-setting-info',
          data: {
            type: 'chatgpt-setting-error',
            message: '规则名称不能为空',
          },
        },
      });
      return;
    }

    const ruleContent = `---
globs: ${filePatterns.length > 0 ? filePatterns.join(',') : '*'}
alwaysApply: ${ruleType === 'Always'}
---

# 项目规则要求如下

`;

    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'createProjectRule',
          ruleContent: ruleContent,
          filePath: `.joycoder/rules/${projectRuleName.trim()}.mdc`,
        },
      },
    });

    setProjectRuleName('');
    setFilePatterns([]);
    setFilePatternInput('');
  };
  /**
   * Codebase索引操作
   */
  const handleCodebaseIndexing = (action: string) => {
    return () => {
      setCodebaseIndexingButtonDisabled(true);
      setTimeout(() => {
        setCodebaseIndexingButtonDisabled(false);
      }, 1000);

      vscode.postMessage<CommonMessage>({
        type: 'COMMON',
        payload: {
          type: 'codebaseIndexing',
          data: {
            type: 'codebaseIndexing',
            action,
          },
        },
      });
    };
  };
  useEffect(() => {
    handleCodebaseIndexing('getProgress')();
  }, []);

  /**
   * 处理CommitMessage
   * @param value - 提交信息的值
   */
  const handleMoreSetting = () => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'moreSetting',
        },
      },
    });
  };

  // 登出
  const handleLogout = () => {
    setUserName('');
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'settiing-logout',
        data: {},
      },
    });
  };

  // 点击管理
  // const handleManagement = () => {
  //   console.log('触发管理');
  // };

  useHandleMessage(({ type, data }) => {
    switch (type) {
      case 'switch-setting-view':
        console.log('%c [ data ]-383', 'font-size:13px; background:pink; color:#bf2c9f;', data);
        setUserName(data?.userInfo.userName);
        // setIsShow(data.type === 'setting');
        setCompletionDelay(data.completionDelay as number); // 延迟时间
        setGenTask(data.genTask as string); // 预测补全-按行还是函数等偏好设置
        setCompletionsMoreContext(data.completionsMoreContext as boolean); // 跨文件感知
        setCommitCodeReview(data.commitCodeReview as boolean); // 增量代码评审
        // setCodeReviewImmediate(data.codeReviewImmediate as boolean); // 主动代码评审
        setErrorLine(data.errorLine as boolean); // 展示错误行
        setShenYi(data.isShenYi as boolean); // 神医开启
        setCodeLens(Array.isArray(data.codeLens) ? data.codeLens : []); // 行间菜单
        setCommitMessage(data.commitMessage as string); //  commitMessage生成类型
        // setWriteAction(data.writeAction as string); // 自动化编程写入模式
        setCustomInstructions((data.customInstructions as string) || ''); // AI规则配置
        setShowCodeLens(data.enableCodeLens as boolean); // 是否展示行间菜单
        setAutoApprovalSettings(data.autoApprovalSettings); // 自动化审批设置

        setShowCodebase(data.showCodebase as boolean); // 是否展示Codebase设置
        setHasWorkspaceFolder(data.hasWorkspaceFolder as boolean); //是否有工作目录（无工作目录不能进行Codebase操作）
        break;
      case 'codebase-update-progress':
        (data.codebaseIndexingProgress as number) >= 0 &&
          setCodebaseIndexingProgress(data.codebaseIndexingProgress as number);
        data.codebaseIndexingStatus && setCodebaseIndexingStatus(data.codebaseIndexingStatus as CodebaseIndexingStatus);
        break;

      case 'codebase-indexing-button-state':
        setCodebaseIndexingButtonDisabled(data.codebaseIndexingButtonDisabled as boolean);
        break;
      default:
        break;
    }
  });

  // 通用
  const renderGeneral = () => {
    return (
      <div className="setting-tabs-item">
        <div className="setting-tabs-item-title">通用</div>
        <div className="joycoder-setting-box">
          <div className="joycoder-setting-box-title">账号</div>
          <div className="joycoder-setting-box-text">当前登录账号： {userName}</div>
          <div className="joycoder-setting-btns">
            <div>
              {/* <Button type="primary" size="small" onClick={handleManagement}>
                <span className="icon iconfont icon-shezhi"> 管理</span>
              </Button> */}
              <Button type="primary" size="small" onClick={handleLogout}>
                <span className="icon iconfont icon-dengchu"> 登出</span>
              </Button>
            </div>
          </div>
        </div>
        <div className="joycoder-setting-box">
          <div className="joycoder-setting-box-title mt-18">IDE 设置</div>
          <div className="joycoder-setting-box-text">对于常规编辑器设置，请访问编辑器设置页面</div>
          <div className="joycoder-setting-btns">
            {/* <div>
              <Button type="primary" icon={<SettingOutlined />} size="small">
                恢复系统配置
              </Button>
            </div> */}
            <div>
              <Button type="primary" size="small" onClick={handleMoreSetting}>
                去设置
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 特性
  const renderSpecial = () => {
    return (
      <div className="setting-tabs-item">
        <div className="setting-tabs-item-title">特性</div>
        <div className="joycoder-setting-box">
          <div className="joycoder-setting-box-title">预测补全</div>
          <div className="joycoder-setting-box-text">生成延迟时间，避免不必要的建议</div>
          <Slider
            marks={marks}
            step={0.1}
            value={completionDelay}
            min={0.3}
            max={3}
            onChange={handleCompletionDelayChange}
            trackStyle={styles.track}
            handleStyle={styles.tracks}
            railStyle={styles.rail}
          />
          <div className="joycoder-setting-box-title mt-18">偏好设置</div>
          {/* <div className="joycoder-setting-box-radios mb-24">
            <Button
              className={`joycoder-setting-box-radio ${genTask === 'TIME_OUT' ? 'active' : ''}`}
              onClick={() => onGenTaskClick('TIME_OUT')}
            >
              <FontColorsOutlined /> 自动
            </Button>
            <Button
              className={`joycoder-setting-box-radio ${genTask === 'LINE' ? 'active' : ''}`}
              onClick={() => onGenTaskClick('LINE')}
            >
              <MinusOutlined /> 单行
            </Button>
            <Button
              className={`joycoder-setting-box-radio ${genTask === 'FUNCTION' ? 'active' : ''}`}
              onClick={() => onGenTaskClick('FUNCTION')}
            >
              <OrderedListOutlined /> 多行
            </Button>
          </div> */}
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeCompletionsMoreContext}
              onChange={onCompletionsMoreContextChange}
            >
              启用跨文件感知
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-tips">
            注：启用后即可感知当前文件目录及打开的相似文件作为预测补全的上下文
          </div>
        </div>
        <div className={!isShowCodeLens ? 'joycoder-setting-box mb-16 hidden' : 'joycoder-setting-box mb-16'}>
          <div className="joycoder-setting-box-title mt-26">行间菜单</div>
          <div>
            <div className="joycoder-setting-box-text">
              <Checkbox
                className="joycoder-setting-box-context"
                checked={codeLens.includes('functionComment')}
                onChange={onCodeLensChange}
                value="functionComment"
              >
                开启生成【函数注释】行间展示
              </Checkbox>
            </div>
            <div className="joycoder-setting-box-text">
              <Checkbox
                className="joycoder-setting-box-context"
                checked={codeLens.includes('reconstruction')}
                onChange={onCodeLensChange}
                value="reconstruction"
              >
                开启生成【代码重构】行间展示
              </Checkbox>
            </div>
            <div className="joycoder-setting-box-text">
              <Checkbox
                className="joycoder-setting-box-context"
                checked={codeLens.includes('comment')}
                onChange={onCodeLensChange}
                value="comment"
              >
                开启生成【逐行注释】行间展示
              </Checkbox>
            </div>
            <div className="joycoder-setting-box-text">
              <Checkbox
                className="joycoder-setting-box-context"
                checked={codeLens.includes('codeReview')}
                onChange={onCodeLensChange}
                value="codeReview"
              >
                开启生成【代码评审】行间展示
              </Checkbox>
            </div>
            <div className="joycoder-setting-box-text mb-16">
              <Checkbox
                className="joycoder-setting-box-context"
                checked={codeLens.includes('test')}
                onChange={onCodeLensChange}
                value="test"
              >
                开启生成【单元测试】行间展示
              </Checkbox>
            </div>
          </div>
        </div>
        <div className="joycoder-setting-box mb-16">
          <div className="joycoder-setting-box-title">生成Commit Message</div>
          <div className="joycoder-setting-box-text joycoder-flex w-88 mb-16">
            <div>
              <span className="joycoder-setting-box-label">生成模式配置：</span>
            </div>
            <div className="joycolder-setting-box-select-wrap">
              <Select
                className="joycoder-setting-box-select"
                value={commitMessage}
                onChange={handleCommitMessageChange}
                placeholder="JoyCoder生成Commit Message配置"
                size="small"
                options={[
                  {
                    value: 'GIT_SCHEMA',
                    label: (
                      <>
                        <div>标准模式</div>
                        <div className="joycoder-setting-box-message">根据 Conventional Commits 规范生成；</div>
                        <div className="joycoder-setting-box-message">示例：fix(chat): 接口错误</div>
                      </>
                    ),
                  },
                  {
                    value: 'BRANCH_SCHEMA',
                    label: (
                      <>
                        <div>分支模式</div>
                        <div className="joycoder-setting-box-message">生成变更摘要 + 分支名称;</div>
                        <div className="joycoder-setting-box-message">示例：fix: 接口错误[分支名称]</div>
                      </>
                    ),
                  },
                  {
                    value: 'AUTO',
                    label: (
                      <>
                        <div>变更摘要</div>
                        <div className="joycoder-setting-box-message">示例：fix connection error</div>
                      </>
                    ),
                  },
                ]}
              />
            </div>
          </div>
        </div>
        <div className="joycoder-setting-box mb-24">
          <div className="joycoder-setting-box-title">代码评审</div>
          <div className="mb-16 v-h mt-10">
            <div className="joycoder-setting-box-text">
              <Checkbox
                className="joycoder-setting-box-context"
                checked={isCommitCodeReview}
                onChange={handleCommitCodeReview}
              >
                开启代码评审增量扫描
              </Checkbox>
            </div>
            <div className="joycoder-setting-box-tip">
              注：针对每次提交代码进行质量、安全、编码规范、逻辑错误等多维度扫描，并提供修复建议；评审结果在问答窗展示。
            </div>
          </div>
          {/* <div className="mb-16 v-h">
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={isCodeReviewImmediate}
              onChange={handleCodeReviewImmediate}
            >
              开启主动代码评审
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-tip">
            注：指每次离开当前编辑文件后，自动扫描该文件中可能存在的质量安全等问题，并提供修复建议；评审结果在下方评审面板展示。
          </div>
        </div> */}
          <div className="mb-16 v-h">
            <div className="joycoder-setting-box-text">
              <Checkbox className="joycoder-setting-box-context" checked={isErrorLine} onChange={handleErrorLine}>
                开启行间错误提示
              </Checkbox>
            </div>
            <div className="joycoder-setting-box-tip">
              注：指编辑过程中分析存在的质量、安全等问题；直接在编辑区区域红字提示。
            </div>
          </div>
          {/* <div className="mb-16 v-h">
            <div className="joycoder-setting-box-text">
              <Checkbox className="joycoder-setting-box-context" checked={isShenYi} onChange={handleShenYi}>
                开启神医安全助手增量扫描
              </Checkbox>
            </div>
            <div className="joycoder-setting-box-tip">
              注：针对每次提交代码进行安全扫描，并提供修复建议；扫描结果在下方评审面板展示。
            </div>
          </div> */}
        </div>
      </div>
    );
  };

  // 上下文
  const renderCodebase = () => {
    return (
      <div className="setting-tabs-item">
        <div className="setting-tabs-item-title">上下文</div>
        <div className="joycoder-setting-box">
          <div className="joycoder-setting-box-text l-20">
            构建代码库索引，可以增强智能体对代码库的理解，提升生成效果。构建索引成功后，智能体将自主获取代码库索引；必要时您也可以通过
            @Codebase 的方式要求智能体使用它。
          </div>
          {hasWorkspaceFolder && (
            <>
              {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING && (
                <div className="joycoder-loading-dots mt-16">索引中({codebaseIndexingProgress}%)</div>
              )}
              {codebaseIndexingStatus === CodebaseIndexingStatus.UNINDEXED && (
                <div className="joycoder-setting-box-msg mt-16">
                  <i className="icon iconfont icon-tanhao mr-4"></i>
                  代码库索引未构建
                </div>
              )}
              {codebaseIndexingStatus === CodebaseIndexingStatus.PREPARING && (
                <div className="joycoder-loading-dots mt-16">Codebase服务准备中</div>
              )}
              {codebaseIndexingStatus === CodebaseIndexingStatus.NO_WORKSPACE && (
                <div className="joycoder-setting-box-msg mt-16">
                  <i className="icon iconfont icon-tanhao mr-4"></i>
                  未检测到工作目录，请打开文件夹后重试
                </div>
              )}
              {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && (
                <div className="joycoder-loading-dots mt-16">索引完成</div>
              )}
              {(codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING ||
                codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED) && (
                <Progress
                  percent={codebaseIndexingProgress}
                  type="line"
                  status={
                    codebaseIndexingProgress === 100 && codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED
                      ? 'success'
                      : 'active'
                  }
                  showInfo={false}
                />
              )}

              {codebaseIndexingStatus === CodebaseIndexingStatus.UNINDEXED && (
                <Button
                  type="default"
                  style={{
                    backgroundColor: 'rgba(240,247,255,1)',
                    borderColor: 'rgba(240,247,255,1)',
                    color: '#18181BFF',
                    width: '72px',
                    height: '28px',
                    marginTop: '12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    padding: '0',
                  }}
                  disabled={codebaseIndexingButtonDisabled}
                  onClick={handleCodebaseIndexing('start')}
                >
                  开始索引
                </Button>
              )}
              {(codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING ||
                codebaseIndexingStatus === CodebaseIndexingStatus.PREPARING) && (
                <Button
                  type="default"
                  style={{
                    backgroundColor: '#FFFFFF1A',
                    borderColor: '#FFFFFF1A',
                    width: '72px',
                    height: '28px',
                    marginTop: '12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    padding: '0',
                  }}
                  onClick={handleCodebaseIndexing('cancel')}
                  disabled={codebaseIndexingButtonDisabled}
                >
                  取消索引
                </Button>
              )}
              {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && (
                <Popconfirm
                  title="确定清除索引吗？"
                  onConfirm={handleCodebaseIndexing('remove')}
                  okText="删除"
                  cancelText="取消"
                >
                  <Button
                    type="default"
                    style={{
                      backgroundColor: '#FFFFFF1A',
                      borderColor: '#FFFFFF1A',
                      width: '72px',
                      height: '28px',
                      marginTop: '12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      padding: '0',
                    }}
                  >
                    删除索引
                  </Button>
                </Popconfirm>
              )}
              {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && (
                <Button
                  type="default"
                  onClick={handleCodebaseIndexing('start')}
                  style={{
                    backgroundColor: 'rgba(240,247,255,1)',
                    borderColor: 'rgba(240,247,255,1)',
                    color: '#18181BFF',
                    width: '72px',
                    height: '28px',
                    marginTop: '12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    padding: '0',
                  }}
                  disabled={codebaseIndexingButtonDisabled}
                >
                  重新索引
                </Button>
              )}
            </>
          )}
          {!hasWorkspaceFolder && (
            <div className="joycoder-setting-box-msg mt-16">
              <i className="icon iconfont icon-tanhao mr-4"></i>
              未检测到工作目录，请打开文件夹后重试
            </div>
          )}
        </div>
      </div>
    );
  };

  // 规则
  const renderRules = () => {
    return (
      <div className="setting-tabs-item">
        <div className="setting-tabs-item-title">规则</div>
        <div className="joycoder-setting-box">
          <div className="joycoder-setting-box-title">全局 AI 规则配置</div>
          <TextArea
            className="joycoder-setting-textarea mt-12"
            value={customInstructions}
            onChange={handleCustomInstructionsChange}
            placeholder="给一个通用一些的默认文案"
            autoSize={{ minRows: 2, maxRows: 6 }}
          />
          <div className="joycoder-setting-box-text mb-16">注：应用于所有项目，重新开启Coder后生效。</div>
        </div>

        <div className="joycoder-setting-box">
          <div className="joycoder-setting-box-title mt-18">项目AI规则配置</div>
          <div className="joycoder-setting-box-text">
            项目规则可以帮助 AI
            理解您的代码库并遵循您定义的规则。这些规则可以始终包含在上下文中，或智能体根据规则主动获取。
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              vscode.postMessage<CommonMessage>({
                type: 'COMMON',
                payload: {
                  type: 'setting-McpHub',
                  data: {},
                },
              });

              setIsShowAddRule(true);
            }}
            style={{
              backgroundColor: '#F0F7FFFF',
              borderColor: '#F0F7FFFF',
              width: '106px',
              height: '28px',
              marginTop: '8px',
              borderRadius: '4px',
              color: '#18181BFF',
              fontSize: '12px',
              padding: '0',
            }}
          >
            创建规则文件
          </Button>
        </div>
        <Modal
          title={modalTitle}
          open={isShowAddRule}
          onCancel={() => setIsShowAddRule(false)}
          onOk={handleCreateProjectRule}
        >
          <div className="joycoder-setting-box-text mb-4">规则名称</div>
          <Input
            placeholder="输入规则名称，用于创建文件"
            value={projectRuleName}
            onChange={handleProjectRuleNameChange}
            className="joycoder-dark-input-border mb-4"
          />

          <div className="joycoder-setting-box-text mb-4">规则类型</div>
          <Select
            className="joycoder-setting-box-select mb-4"
            style={{ width: '100%' }}
            value={ruleType}
            onChange={handleRuleTypeChange}
            options={[
              { value: 'Always', label: '始终生效' },
              { value: 'AutoAttached', label: '仅指定文件生效' },
            ]}
          />

          {ruleType === 'AutoAttached' && (
            <>
              <div className="joycoder-setting-box-text mb-4">
                匹配规则{' '}
                <Tooltip
                  title={
                    <span style={{ color: '#303133FF' }}>
                      当您在此处指定文件模式时（例如
                      *.ts，src/config/***json），此规则将自动包含在提问或智能体的上下文中
                    </span>
                  }
                  color="#FFFFFFFF"
                >
                  <i className="icon iconfont icon-tanhao"></i>
                </Tooltip>
              </div>
              <div className="mb-4">
                {filePatterns.map((pattern) => (
                  <Tag
                    key={pattern}
                    closable
                    onClose={() => handleRemoveFilePattern(pattern)}
                    style={{ marginBottom: '8px', marginRight: '8px' }}
                  >
                    {pattern}
                  </Tag>
                ))}
              </div>
              <Space.Compact style={{ width: '100%' }}>
                <Input
                  className="joycoder-dark-input-border"
                  placeholder="例如: *.ts, src/config/**/*.json, ..."
                  value={filePatternInput}
                  onChange={handleFilePatternInputChange}
                  onPressEnter={handleAddFilePattern}
                />
                <Button icon={<PlusOutlined />} onClick={handleAddFilePattern}>
                  添加
                </Button>
              </Space.Compact>
              <div className="joycoder-setting-box-tips mb-40">
                <InfoCircleOutlined style={{ marginRight: '4px' }} />
                触发条件： 1.用户提问中包含带后缀的关键词（如 .ts、.js 等） 2.Agent 操作涉及带后缀的文件（如读取/修改
                xx/xx.ts 文件）
              </div>
            </>
          )}
        </Modal>
        {/* <div className="joycoder-setting-box mb-16">
          <div className="joycoder-setting-box-title">自动化编程设置</div>
          {ACTION_METADATA.map((action) => {
            return (
              <div className="mb-16 v-h" key={action.id}>
                <div className="joycoder-setting-box-text">
                  <Checkbox
                    className="joycoder-setting-box-context"
                    checked={autoApprovalSettings.actions[action.id]}
                    onChange={onAutoApprovalSettingsChange}
                    value={action.id}
                  >
                    {action.label}
                  </Checkbox>
                </div>
                <div className="joycoder-setting-box-tip">注：{action.description}</div>
              </div>
            );
          })}
        </div> */}
      </div>
    );
  };

  const settingItems: TabsProps['items'] = [
    {
      key: '0',
      label: (
        <>
          <i className="icon iconfont icon-tongyongshezhi mr-10"></i>通用
        </>
      ),
      children: renderGeneral(),
    },
    {
      key: '1',
      label: (
        <>
          <i className="icon iconfont icon-texing mr-10"></i>特性
        </>
      ),
      children: renderSpecial(),
    },
    {
      key: '2',
      label: (
        <>
          <i className="icon iconfont icon-zhinengti mr-10"></i>智能体
        </>
      ),
      children: <div>智能体</div>,
    },
    {
      key: '3',
      label: (
        <>
          <i className="icon iconfont icon-MCP mr-10"></i>MCP
        </>
      ),
      children: <McpMarket />,
    },
    {
      key: '4',
      label: (
        <>
          <i className="icon iconfont icon-shangxiawen mr-10"></i>上下文
        </>
      ),
      children: renderCodebase(),
    },
    {
      key: '5',
      label: (
        <>
          <i className="icon iconfont icon-guize mr-10"></i>规则
        </>
      ),
      children: renderRules(),
    },
  ];

  return (
    <div className="joycoder-setting-content">
      <div className="setting-title">JoyCode 设置</div>
      <Tabs
        defaultActiveKey="5"
        items={settingItems}
        tabPosition="left"
        size="small"
        tabBarGutter={0}
        className="setting-tabs"
      />
    </div>
  );
}

import Setting from './components/setting';
import { ConfigProvider } from 'antd';
import Watermark from '@uiw/react-watermark';
import { Theme, useTheme } from './hooks/useTheme';

function App() {
  const { theme, themeClassName } = useTheme();

  return (
    <ConfigProvider prefixCls={themeClassName}>
      <Watermark
        content={'JoyCoder'}
        fontSize={10}
        fontColor={theme == Theme.Light ? 'rgba(0,0,0,0.07)' : 'rgba(255,255,255,0.05)'}
        rotate={-15}
        gapY={100}
        gapX={100}
      >
        <Setting />
      </Watermark>
    </ConfigProvider>
  );
}

export default App;

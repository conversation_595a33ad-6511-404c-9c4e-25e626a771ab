{"name": "@joycoder/web", "version": "2.9.0", "description": "", "author": "JoyCoder", "homepage": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git", "private": true, "license": "", "main": "src/index.ts", "module": "src/index.ts", "files": ["dist"], "repository": {"type": "git", "url": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git"}, "scripts": {"watch": "webpack --watch --mode development", "build": "webpack --mode production"}, "bugs": {"url": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/"}, "publishConfig": {"access": "public"}, "dependencies": {"@ant-design/icons": "^4.7.0", "@joycoder/shared": "workspace:*", "@types/marked": "^4.0.7", "@types/react": "^18.2.0", "katex": "^0.16.11", "mermaid": "^10.9.1", "react": "^18.2.0", "react-antdown": "^1.0.11", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "rehype-katex": "^6.0.3", "rehype-minify-whitespace": "^5.0.1", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "remark-math": "5.1.1", "use-debounce": "^10.0.1"}, "devDependencies": {"typescript": "5.5.3"}}
import React, { useEffect, useRef, useState } from 'react';
import { Button } from 'antd';
import {
  CopyOutlined,
  CheckOutlined,
  PlusOutlined,
  ChromeOutlined,
  CodeOutlined,
  FileAddOutlined,
} from '@ant-design/icons';
import { useReport } from '../../../hooks/useReport';
import { CommonMessage } from '../../../messages/messageTypes';

import './index.scss';
import { QA } from '../../../app/chatGPT/components/ChatView/ChatList';
import { uniqueId } from 'lodash';

interface HighlightButtonsProps {
  text: string;
  language: string;
  qa?: QA;
}

interface HlButtonProps {
  onClick: () => void;
  icon: React.ReactNode;
  title?: string;
}
const HlButton = (props: HlButtonProps) => {
  const clickedRef = useRef(false);
  const [isDone, setIsDone] = useState(false);

  const onClick = () => {
    if (clickedRef.current) return;

    clickedRef.current = true;

    const res: any = props.onClick();
    const hasPromise = !!(typeof res === 'object' && res.then);

    Promise.resolve(res)
      .then(() => {
        // 只有明确的回调才展示操作结果
        hasPromise && setIsDone(true);
        setTimeout(() => {
          hasPromise && setIsDone(false);
          clickedRef.current = false;
        }, 2000);
      })
      .catch(() => {
        clickedRef.current = false;
      });
  };

  return (
    <Button
      {...props}
      className="hl-buttons_button"
      size="small"
      icon={isDone ? <CheckOutlined /> : props.icon}
      onClick={onClick}
      // disabled={!loading}
    ></Button>
  );
};

const HighlightButtons = (props: HighlightButtonsProps) => {
  const buttonsRef = useRef<HTMLDivElement | null>(null);
  const { reportAction } = useReport();

  useEffect(() => {
    buttonsRef.current?.parentElement?.classList.add('hl-buttons_wrapper');
  }, []);

  return (
    <>
      <em>{props?.language || 'plaintext'}</em>
      <div className="hl-buttons" ref={buttonsRef}>
        <HlButton
          title="插入"
          icon={<PlusOutlined />}
          onClick={() => {
            const messageNode = findParentWithClass(
              buttonsRef.current as HTMLDivElement,
              'chat-list-item',
              'chat-list'
            );
            vscode.postMessage<CommonMessage>({
              type: 'COMMON',
              payload: {
                type: 'chatgpt-webview-insertcode',
                data: { text: props.text, conversationId: messageNode?.dataset?.conversationId },
              },
            });

            reportAction({
              actionCate: 'ai',
              actionType: 'replace',
              accept: 1,
              result: props.text,
              ...messageNode?.dataset,
              extendMsg: {
                messageId: props?.qa?.messageId,
              },
            });
          }}
        ></HlButton>
        <HlButton
          title="复制"
          icon={<CopyOutlined />}
          onClick={() => {
            if (!navigator.clipboard) return Promise.reject();
            const messageNode = findParentWithClass(
              buttonsRef.current as HTMLDivElement,
              'chat-list-item',
              'chat-list'
            );
            reportAction({
              actionCate: 'ai',
              actionType: 'copy',
              accept: 1,
              result: props.text,
              ...messageNode?.dataset,
              extendMsg: {
                messageId: props?.qa?.messageId,
              },
            });
            return navigator.clipboard.writeText(props.text);
          }}
        ></HlButton>
        <HlButton
          title="新建文件"
          icon={<FileAddOutlined />}
          onClick={() => {
            const messageNode = findParentWithClass(
              buttonsRef.current as HTMLDivElement,
              'chat-list-item',
              'chat-list'
            );
            vscode.postMessage<CommonMessage>({
              type: 'COMMON',
              payload: {
                type: 'chatgpt-webview-insertfile',
                data: {
                  language: props.language,
                  content: props.text,
                  conversationId: messageNode?.dataset?.conversationId,
                },
              },
            });

            reportAction({
              actionCate: 'ai',
              actionType: 'replace',
              accept: 1,
              result: props.text,
              ...messageNode?.dataset,
              extendMsg: {
                messageId: props?.qa?.messageId,
              },
            });
          }}
        ></HlButton>
        {['html', 'xml'].includes(props.language) && (
          <HlButton
            title="预览"
            icon={<ChromeOutlined />}
            onClick={() => {
              vscode.postMessage<CommonMessage>({
                type: 'COMMON',
                payload: {
                  type: 'chatgpt-webview-previewhtml',
                  data: props.text,
                },
              });

              const messageNode = findParentWithClass(
                buttonsRef.current as HTMLDivElement,
                'chat-list-item',
                'chat-list'
              );
              reportAction({
                actionCate: 'ai',
                actionType: 'copy',
                accept: 1,
                result: props.text,
                ...messageNode?.dataset,
                extendMsg: {
                  messageId: props?.qa?.messageId,
                },
              });
            }}
          ></HlButton>
        )}
        {['bash'].includes(props.language) && (
          <HlButton
            title="插入终端"
            icon={<CodeOutlined />}
            onClick={() => {
              vscode.postMessage<CommonMessage>({
                type: 'COMMON',
                payload: {
                  type: 'chatgpt-webview-insertbash',
                  data: props.text,
                },
              });

              const messageNode = findParentWithClass(
                buttonsRef.current as HTMLDivElement,
                'chat-list-item',
                'chat-list'
              );
              reportAction({
                actionCate: 'ai',
                actionType: 'replace',
                accept: 1,
                result: props.text,
                ...messageNode?.dataset,
                extendMsg: {
                  messageId: props?.qa?.messageId,
                },
              });
            }}
          ></HlButton>
        )}
      </div>
    </>
  );
};

function findParentWithClass(element: HTMLElement, targetClass: string, endClass: string): HTMLElement | null {
  while (element && element !== document.body) {
    if (element.classList.contains(targetClass)) {
      return element;
    } else if (element.classList.contains(endClass)) {
      return null;
    }
    element = element.parentElement as HTMLElement;
  }
  return null;
}

export default HighlightButtons;

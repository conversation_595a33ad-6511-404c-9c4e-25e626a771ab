import { List, Avatar, Collapse, Tag } from 'antd';
import {
  StarOutlined,
  StarFilled,
  ReloadOutlined,
  CopyOutlined,
  CheckOutlined,
  BugOutlined,
  LoadingOutlined,
  LikeOutlined,
  DislikeOutlined,
  LikeFilled,
  DislikeFilled,
} from '@ant-design/icons';
import React, { useState } from 'react';
import MarkdownRender from '../../../../../components/MarkdownRender';
import FeedbackModal from '../feedback';
import { useReport } from '../../../../../hooks/useReport';
import { useChatCopy, useImgCopy } from '../../../../../hooks/useCopy';
import { ActionType } from '@joycoder/shared/src/report/ationType';
import { useTheme } from '../../../../../hooks/useTheme';
import { CommonMessage } from '../../../../../messages/messageTypes';
import './index.scss';
import { IFileContextPanelInfo } from '../FileContextPanel';
import AgentLog from '../AgentLog';
import Think from './think';

const { Panel } = Collapse;
export interface QA {
  type: 'question' | 'answer';
  name: string;
  avatar: string;
  // 回答的messageId，问题会用前端的uuid模拟
  messageId?: string;
  // 上1个回答的messageId
  parentMessageId?: string;
  code?: string;
  prompt?: string;
  markdown: string;
  reasoningContent?: string;
  conversationId?: string;
  agentLogInfo?: any;
  drawResults?: any;
  selectedFileList?: any;
  isLiked?: boolean;
  isDisliked?: boolean;
}

interface ChatListProps {
  loading: boolean;
  tabLoading: boolean;
  qaList: QA[];
  setQaList: React.Dispatch<React.SetStateAction<QA[]>>;
  favPromptMap: Map<string, boolean>;
  appendQATools: Array<JSX.Element | null>;
}

const WELCOME_QUESTION = '你好';

export default function ChatList(props: ChatListProps) {
  const { loading, tabLoading, qaList, setQaList, favPromptMap, appendQATools } = props;
  const [openFileInfo, setOpenFileInfo] = useState<IFileContextPanelInfo | null>(null);
  const [copyContent, setCopyContent] = useState('');
  const [feedbackVisible, setFeedbackVisible] = useState(false);
  const [currentQA, setCurrentQA] = useState<QA | null>(null);

  const { reportAction } = useReport();
  const { copyToClipboard } = useChatCopy();
  const { themeClassName } = useTheme();

  const handleQAToolAction = (
    data: QA,
    type:
      | 'chatgpt-set-prompt'
      | 'chatgpt-unset-prompt'
      | 'chatgpt-retry'
      | 'chatgpt-copy'
      | 'like-done'
      | 'like'
      | 'disLike'
      | 'disLike-done'
  ) => {
    if (type === 'chatgpt-copy') {
      // 如果是用户问题节点，复制内容是原始的prompt，没有经过escapeHtml处理
      let result = (data.type === 'question' && data.prompt) || data.markdown;
      //复制图片
      console.log('copyresult', result);

      if (data.markdown && data.markdown.includes('<img') && data.markdown.includes('joycoder-chart')) {
        try {
          const imgMatch = data.markdown.match(/<img.*?src="(.*?)".*?>/);
          if (imgMatch) {
            const svgBase64 = imgMatch[1];
            useImgCopy(svgBase64, data);
          }
          // 更新icon状态
          setCopyContent(result);
          //防止上报信息过大
          result = data.markdown.replace(/(<img[^>]+src=["'])([^"']+)(["'][^>]*>)/g, `$1[mermaidImg]$3`);
        } catch (error) {
          console.warn('useImgCopy', error);
        }
      } else {
        // 复制到剪贴板
        copyToClipboard(result);
        // 更新icon状态
        setCopyContent(result);
      }

      // 数据上报
      reportAction({
        accept: 1,
        actionCate: 'ai',
        actionType: data.type === 'answer' ? ActionType.aCopy : ActionType.qCopy,
        question: data.prompt,
        result,
        conversationId: data.conversationId,
        extendMsg: {
          messageId: data.messageId,
        },
      });
      setTimeout(() => setCopyContent(''), 1500);
    }
    if (type === 'chatgpt-retry') {
      // 找到当前问题的索引，删除后面的问题（可能存在的重复）和回答
      let questionIndex = -1;
      for (let i = qaList.length - 1; i >= 0; i--) {
        if (qaList[i].messageId === data.messageId) {
          questionIndex = i;
          break;
        }
      }
      const result = qaList[questionIndex + 1]?.markdown;
      qaList.splice(questionIndex, qaList.length - questionIndex);
      setQaList([...qaList]);
      // 数据上报
      reportAction({
        actionCate: 'ai',
        actionType: ActionType.regenerate,
        question: data.prompt,
        result,
        extendMsg: {
          messageId: data.messageId,
        },
      });
    }
    const likeStatus = ['like-done', 'like', 'disLike', 'disLike-done'];
    const isLiked = type === 'like';
    const isDisliked = type === 'disLike';
    if (likeStatus.includes(type)) {
      setFeedbackVisible(isDisliked);
      setCurrentQA(data);
      for (let i = qaList.length - 1; i >= 0; i--) {
        if (qaList[i].messageId === data.messageId) {
          qaList[i].isLiked = isLiked;
          qaList[i].isDisliked = isDisliked;
          break;
        }
      }
      setQaList([...qaList]);
      // 数据上报
      reportAction({
        actionCate: 'ai',
        actionType: ActionType.like,
        question: data.prompt,
        result: data.markdown,
        conversationId: data.conversationId,
        model: data.name,
        extendMsg: {
          messageId: data.messageId,
          isLiked: isLiked,
          isDisliked: isDisliked,
          model: data.name,
        },
      });
    }
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type,
        data,
      },
    });
  };
  const getAvatar = (item: { type: string; avatar: string }) => {
    if (window?.isBusinessLocal) {
      return item.type === 'question' ? window.logoImg : window.userImg;
    }
    return item.avatar;
  };

  const openFile = (fileInfo: IFileContextPanelInfo) => {
    setOpenFileInfo(fileInfo);
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'OPEN_FILE_IN_VSCODE',
        data: {
          filePath: fileInfo.filepath || fileInfo.filePath,
        },
      },
    });
  };

  return (
    <>
      <List
        loading={(loading && !qaList.length) || tabLoading}
        itemLayout="horizontal"
        dataSource={qaList}
        locale={{ emptyText: <></> }}
        className="chat-list"
        renderItem={(item, index) => {
          const QuestionTools = [];
          const selectedFileList = item?.selectedFileList ?? [];
          //图片处理，展示和复制
          const mdString = item?.markdown;
          if (item.markdown !== WELCOME_QUESTION) {
            if (item.type === 'question') {
              if (item.parentMessageId || item.messageId || item.conversationId) {
                QuestionTools.push(
                  <ReloadOutlined
                    key="retry"
                    title="重试"
                    className={`question-tools-item${loading ? '-loading' : ''} question-hover-show`}
                    onClick={() => handleQAToolAction(item, 'chatgpt-retry')}
                  />
                );
              }
              if (window.joyCoderVersion !== 'business' && window.joyCoderVersion !== 'ide') {
                if (favPromptMap.get(item.markdown)) {
                  QuestionTools.push(
                    <StarFilled
                      key="unset"
                      title="取消收藏"
                      className="question-tools-item icon-select  question-hover-show"
                      onClick={() => handleQAToolAction(item, 'chatgpt-unset-prompt')}
                    />
                  );
                } else {
                  QuestionTools.push(
                    <StarOutlined
                      key="unset"
                      title="收藏提示词"
                      className="question-tools-item  question-hover-show"
                      onClick={() => handleQAToolAction(item, 'chatgpt-set-prompt')}
                    />
                  );
                }
              }
            }
            if (item.type === 'answer') {
              if (index >= qaList.length - 2) {
                // 只展示最后1条回答的安全检测状态
                QuestionTools.push(...appendQATools);
              }
              QuestionTools.push(
                item.isLiked ? (
                  <LikeFilled
                    key="like-done"
                    title="已赞"
                    className="question-tools-item"
                    onClick={() => handleQAToolAction(item, 'like-done')}
                  />
                ) : (
                  <LikeOutlined
                    key="like"
                    title="赞"
                    className="question-tools-item"
                    onClick={() => handleQAToolAction(item, 'like')}
                  />
                )
              );
              QuestionTools.push(
                item.isDisliked ? (
                  <DislikeFilled
                    key="disLike-done"
                    title="踩一下"
                    className="question-tools-item"
                    onClick={() => handleQAToolAction(item, 'disLike-done')}
                  />
                ) : (
                  <DislikeOutlined
                    key="disLike"
                    title="踩"
                    className="question-tools-item"
                    onClick={() => handleQAToolAction(item, 'disLike')}
                  />
                )
              );
            }
            QuestionTools.push(
              copyContent === item.markdown ? (
                <CheckOutlined
                  key="copy-done"
                  title="复制成功!"
                  className={`question-tools-item ${item.type === 'question' ? 'question-hover-show' : ''}`}
                />
              ) : (
                <CopyOutlined
                  key="copy"
                  title="复制"
                  className={`question-tools-item ${item.type === 'question' ? 'question-hover-show' : ''}`}
                  onClick={() => handleQAToolAction(item, 'chatgpt-copy')}
                />
              )
            );
          }
          return (
            <List.Item
              actions={[]}
              className="chat-list-item"
              data-conversation-id={item.conversationId}
              data-message-id={item.messageId}
              data-type={item.type}
            >
              <List.Item.Meta
                title={
                  <>
                    <Avatar src={getAvatar(item)} size="small" />
                    {item.name}
                    <div className="question-tools">{QuestionTools}</div>
                  </>
                }
                description={
                  <>
                    {item.type === 'answer' && <Think content={item.reasoningContent} />}
                    {selectedFileList && selectedFileList.length > 0 && (
                      <Collapse
                        defaultActiveKey={['selectedFileList']}
                        className="joycoder-collapse agentlog"
                        expandIconPosition="end"
                      >
                        <Panel header={<>code-context</>} key="files" className="joycoder-chat-file">
                          {selectedFileList.map((item: IFileContextPanelInfo, i: number) => {
                            const checked: boolean = item.filePath === openFileInfo?.filePath;
                            return (
                              <Tag.CheckableTag
                                checked={!!checked}
                                className="joyCoder-file-tag"
                                key={i}
                                onClick={() => openFile(item)}
                              >
                                {item?.label}
                              </Tag.CheckableTag>
                            );
                          })}
                        </Panel>
                      </Collapse>
                    )}
                    {item.agentLogInfo && typeof item.agentLogInfo !== 'string' ? (
                      // 对象格式
                      <Collapse
                        defaultActiveKey={['']}
                        className="joycoder-collapse agentlog"
                        expandIconPosition="end"
                        expandIcon={() => <BugOutlined className="headerarrow" />}
                      >
                        <Panel
                          header={
                            <>
                              {item.agentLogInfo.header}
                              {item.agentLogInfo.loading && <LoadingOutlined className="headerloading" />}
                            </>
                          }
                          key="log"
                        >
                          <div className="agentlog-content">
                            {item.agentLogInfo.logList?.map((logItem: any, index: number) => {
                              return (
                                <div key={index}>
                                  <h5>{logItem.title}</h5>
                                  <p>{logItem.content}</p>
                                </div>
                              );
                            })}
                          </div>
                        </Panel>
                      </Collapse>
                    ) : (
                      item.agentLogInfo &&
                      !item.agentLogInfo.endsWith('[DONE]') && (
                        // 字符格式,智能助手使用
                        <div className="joycoder-collapse agentlog">
                          <div>{item.agentLogInfo && <AgentLog agentLogInfo={item.agentLogInfo} />}</div>
                        </div>
                      )
                    )}
                    <MarkdownRender
                      className={`${themeClassName}-typography`}
                      markdownStr={mdString}
                      buttons={true}
                      onLinkClick={onLinkClick}
                      onImgClick={onImgClick}
                      conversationInfo={item}
                    />
                  </>
                }
              />
            </List.Item>
          );
        }}
      />
      <FeedbackModal currentQA={currentQA} visible={feedbackVisible} onVisible={setFeedbackVisible} />
    </>
  );
}

/**
 * md中插入点击a标签时的点击事件
 * @param options
 */
const onLinkClick = (event: any, options: any) => {
  const { id } = options;
  switch (id) {
    case 'forceLoginBtn':
      vscode.postMessage({
        type: 'FORCE_LOGIN',
        payload: {
          isShowDialog: false,
        },
      });
      break;
    case 'pic2codePreview':
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore 找到当前按钮所处的消息区
      const ancestor = event?.target?.closest('.react-antdown');
      const codeElement = ancestor?.querySelector('code[data-blockcode]');
      const codeValue = codeElement?.getAttribute('data-blockcode');
      if (codeValue) {
        // 复制代码
        window.navigator?.clipboard?.writeText(codeValue);
      }
      // 跳转预览页
      vscode.postMessage({
        type: 'OPEN_IN_BROWSER',
        payload: 'https://stackblitz.com/edit/react-zepjhl?file=demo.tsx',
      });
      break;
    default:
      break;
  }
};

/**
 * 图片点击
 * @param event
 * @param imgData
 */
const onImgClick = (event: any, imgData: string) => {
  vscode.postMessage({
    type: 'DOWNLOAD_IMG',
    payload: {
      imgData: imgData,
    },
  });
};

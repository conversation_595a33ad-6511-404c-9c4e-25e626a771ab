import {
  CloudSyncOutlined,
  DeleteOutlined,
  DownloadOutlined,
  FileTextOutlined,
  MessageOutlined,
  MessageTwoTone,
  QuestionOutlined,
  SettingOutlined,
  StarFilled,
  ToolOutlined,
  LogoutOutlined,
  FileImageOutlined,
  ApiOutlined,
  LikeOutlined,
} from '@ant-design/icons';
import { AgentSceneType } from '@joycoder/plugin-base-ai/src/langchain/tools/shared/toolType';
import { Button, Dropdown, MenuProps, Popconfirm, Tooltip, message } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { QA } from '../ChatList';
import { CommonMessage } from '../../../../../messages/messageTypes';
import { getPopupContainer } from '../../../../../utils';
import { getCurrentTabName } from '../ChatTabs';
import { ThemeIcon } from '../../../../../icons';
import { useThemePanel } from '../../../hooks/useThemePanel';
import { useHandleMessage } from '../../../../../hooks/useHandleMessage';
import { useRenderMonitor } from '../../../../../hooks/useRenderMonitor';
import { DEFAULT_ASSISTANT_AVATAR } from '@joycoder/shared/src/constantData';
import { ChatModelConfig } from '@joycoder/plugin-base-ai/src/model';

import './index.scss';
import { useEvent } from '../../../../../hooks/useEvent';

export const ModelAvatarMap = new Map<string, string>();

interface Config {
  model: string;
  theme: string;
  conversation: '开启' | '关闭' | '';
  context: boolean;
  // repoStatus?: boolean;
}

const pluginVersion = window.joyCoderVersion;

const configMenus = [
  {
    icon: <ToolOutlined />,
    title: '插件设置',
    actionType: 'chatgpt-setting',
  },
  {
    icon: <CloudSyncOutlined />,
    title: '检查更新',
    actionType: 'chatgpt-check-update',
  },
  {
    icon: <FileTextOutlined />,
    title: '操作手册',
    actionType: 'chatgpt-open-url',
    data:
      pluginVersion == 'business'
        ? 'https://docs.jdcloud.com/cn/joycoder/features-introduction'
        : 'http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/vscode/quickstart',
  },
  {
    icon: <LogoutOutlined />,
    title: '退出登录',
    actionType: 'chatgpt-logout',
  },
];
if (pluginVersion !== 'business') {
  configMenus.unshift({
    icon: <StarFilled />,
    title: '我的提示词',
    actionType: 'chatgpt-open-url',
    data: 'http://jxmanage.jd.com/webstatic/prompt/#/favorites',
  });
  configMenus.push({
    icon: <QuestionOutlined />,
    title: '问题反馈',
    actionType: 'chatgpt-open-url',
    data: 'https://joyspace.jd.com/sheets/AJRcr7aiRY0taDjA4UXO',
  });
}
const ConfigMemuItems: MenuProps['items'] = configMenus.map((item) => {
  const tip = '';
  return {
    key: item.title,
    label: (
      <div
        className="chatconfig_menu"
        onClick={() => {
          if (item.actionType === 'chatgpt-setting') {
            vscode.postMessage<CommonMessage>({
              type: 'COMMON',
              payload: {
                type: 'chatgpt-setting-info',
                data: {
                  type: 'chatgpt-setting',
                },
              },
            });
            return;
          }
          vscode.postMessage<CommonMessage>({
            type: 'COMMON',
            payload: {
              type: item.actionType,
              data: item.data,
            },
          });
        }}
      >
        <a className="chatconfig_menu_action" target="_blank" title={tip}>
          {item.icon}
          <div className="chatconfig_menu_action--title">
            {item.title}
            <div className="chatconfig_action--tip">{tip}</div>
          </div>
        </a>
      </div>
    ),
  };
});

export default function ChatConfig(props: {
  disabled: boolean;
  requireModelFeatures?: string[];
  qaList: QA[];
  scene?: AgentSceneType;
}) {
  useRenderMonitor('ChatConfig');
  const { disabled, requireModelFeatures, qaList, scene } = props;

  const [config, setConfig] = useState<Config>({
    model: window.joyCoderVscodeConfig.chatgptModel ?? '',
    theme: '',
    conversation: '',
    context: false,
    // repoStatus: false,
  });
  const [modelList, setModelList] = useState<ChatModelConfig[]>([]);

  const modelAvatar = useMemo(() => {
    return modelList.find((item) => item.label === config.model)?.avatar || DEFAULT_ASSISTANT_AVATAR;
  }, [config.model, modelList]);

  const { ThemePanel } = useThemePanel();

  useHandleMessage(({ type, data }) => {
    if (type === 'updateGPTConfig') {
      setConfig({ ...config, ...data });
    }
    if (type === 'updateGPTModel') {
      // 借机更新下模型和头像的映射关系
      data.forEach((item: ChatModelConfig) => {
        ModelAvatarMap.set(item.label, item.avatar);
      });
      setModelList([...data]);
    }
  });

  useEffect(() => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-get-model',
      },
    });
  }, []);

  const ModelMemuItems = useMemo<MenuProps['items']>(() => {
    return modelList
      .filter((item) => {
        if (requireModelFeatures && requireModelFeatures.length) {
          return requireModelFeatures.every((feature) => item.features?.includes(feature));
        }
        return true;
      })
      .map((item) => {
        const ICONS =
          item.features
            ?.map((feature) => {
              const idKey = `${item.label}_${feature}`;
              if (feature === 'vision') {
                return (
                  <Tooltip
                    key={idKey}
                    title="支持图文多模态"
                    getTooltipContainer={getPopupContainer(idKey)}
                    placement="topRight"
                  >
                    <FileImageOutlined id={idKey} className="chatconfig_menu_content_header_icons--vision" />
                  </Tooltip>
                );
              }
              if (feature === 'function_call') {
                return (
                  <Tooltip
                    key={idKey}
                    title="支持函数调用（逻辑规划）"
                    getTooltipContainer={getPopupContainer(idKey)}
                    placement="topRight"
                  >
                    <ApiOutlined id={idKey} className="chatconfig_menu_content_header_icons--function" />
                  </Tooltip>
                );
              }
              if (feature === 'recommend') {
                return (
                  <Tooltip
                    key={idKey}
                    title="JoyCoder推荐使用"
                    getTooltipContainer={getPopupContainer(idKey)}
                    placement="topRight"
                  >
                    <LikeOutlined id={idKey} className="chatconfig_menu_content_header_icons--recommend" />
                  </Tooltip>
                );
              }
              return null;
            })
            .filter(Boolean) || [];
        if (item.maxTotalTokens > 0) {
          ICONS.push(
            <Tooltip
              key={`${item.label}_maxToken`}
              title={`最大支持上下文长度${(item.maxTotalTokens / 1000).toFixed(0)}K`}
              getTooltipContainer={getPopupContainer(`${item.label}_maxToken`)}
              placement="topRight"
            >
              <div
                id={`${item.label}_maxToken`}
                className="chatconfig_menu_content_header_icons--tokens"
                style={{ display: pluginVersion == 'business' ? 'none' : '' }}
              >
                {(item.maxTotalTokens / 1000).toFixed(0)}K
              </div>
            </Tooltip>
          );
        }
        return {
          key: item.label,
          label: (
            <div className="chatconfig_menu" onClick={() => setChatModel(item.label)}>
              <a className="chatconfig_menu_action" target="_blank" title={item.description}>
                <img style={{ width: 14, height: 14 }} src={item.avatar || DEFAULT_ASSISTANT_AVATAR} alt={item.label} />
                <div className={`chatconfig_menu_content ${config.model === item.label ? 'active' : ''}`}>
                  <div className="chatconfig_menu_content_header">
                    {item.label}
                    <div className="chatconfig_menu_content_header_icons">{ICONS}</div>
                  </div>
                  <div className="chatconfig_menu_content_tip">{item.description}</div>
                </div>
              </a>
            </div>
          ),
        };
      });
  }, [modelList, requireModelFeatures, config.model]);

  return (
    <div className="chatconfig">
      {/* 设置菜单，区分商业化版本及ide版本 */}
      {pluginVersion !== 'business' && pluginVersion !== 'ide' && (
        <Dropdown
          menu={{ items: ConfigMemuItems }}
          placement="topLeft"
          trigger={['click']}
          getPopupContainer={() => {
            return document.querySelector('#dropdown-setting') || document.createElement('div');
          }}
        >
          <Button
            className="chatconfig_btn"
            icon={<SettingOutlined />}
            size="small"
            disabled={disabled}
            id="dropdown-setting"
          ></Button>
        </Dropdown>
      )}

      {/* 主题设置 */}
      <Popconfirm
        placement="topLeft"
        showCancel={false}
        icon={null}
        getPopupContainer={getPopupContainer('popconfirm-theme')}
        title={ThemePanel}
      >
        <Button className="chatconfig_btn" icon={<ThemeIcon />} size="small" id="popconfirm-theme" />
      </Popconfirm>
      {/* 清除会话 */}
      <Tooltip placement="top" title={'清除会话'} getPopupContainer={getPopupContainer('tip-clear')}>
        <Button
          disabled={disabled}
          className="chatconfig_btn"
          icon={<DeleteOutlined />}
          size="small"
          id="tip-clear"
          onClick={() => {
            vscode.postMessage<CommonMessage>({
              type: 'COMMON',
              payload: {
                type: 'clear-current-chatgpt',
              },
            });
          }}
        />
      </Tooltip>
      {/* 多轮会话开关 */}
      <Tooltip
        placement="top"
        title={`多轮对话模式已${config['conversation' as keyof Config]}`}
        getPopupContainer={getPopupContainer('tip-conversation')}
      >
        <Button
          disabled={disabled || scene == AgentSceneType.MultiAgent || scene == AgentSceneType.D2C}
          className={`chatconfig_btn ${config.conversation === '开启' ? 'active' : ''}`}
          icon={config.conversation === '开启' ? <MessageTwoTone twoToneColor="#3cad6e" /> : <MessageOutlined />}
          size="small"
          id="tip-conversation"
          onClick={() => setChatUseConversation(config.conversation)}
        ></Button>
      </Tooltip>
      {/* 模型切换 */}
      <Dropdown
        menu={{ items: ModelMemuItems }}
        placement="top"
        trigger={['click']}
        disabled={disabled}
        getPopupContainer={getPopupContainer('tip-model')}
      >
        <Button
          disabled={disabled}
          className="chatconfig_btn"
          icon={<img className="model-icon" style={{ width: 12, height: 12 }} src={modelAvatar} />}
          size="small"
          id="tip-model"
        >
          {config.model}
        </Button>
      </Dropdown>
      <Tooltip placement="top" title={'导出当前会话'} getPopupContainer={getPopupContainer('tip-export')}>
        <Button
          disabled={disabled}
          className="chatconfig_btn"
          icon={<DownloadOutlined />}
          size="small"
          id="tip-export"
          onClick={() => downLoadChat(qaList)}
        ></Button>
      </Tooltip>
    </div>
  );
}

/** 下载当前会话到本地，格式为markdown文件 */
export const downLoadChat = (qaList: QA[]) => {
  if (!qaList || !qaList.length) {
    message.warning('暂无可以导出的会话~');
    return;
  }
  try {
    let markdownContent = '';
    // 遍历对象数组，将每个对象的markdown内容添加到markdownContent中
    qaList.forEach((obj) => {
      markdownContent += '# ' + obj.name + '\n\n';
      markdownContent += obj.markdown + '\n\n';
    });
    // 创建Blob对象，将markdown内容转换为Blob
    const blob = new Blob([markdownContent], { type: 'text/markdown' });
    // 创建下载链接
    const downloadLink = document.createElement('a');
    downloadLink.href = URL.createObjectURL(blob);
    downloadLink.download = `JoyCoder-${getCurrentTabName()}-${new Date().toLocaleDateString()}.md`;
    // 模拟点击下载链接
    downloadLink.click();
  } catch (e) {
    console.error('downloadChat', e);
  }
};

/**
 * 设置模型 或 呼起设置模型下拉列表
 * @param model
 */
export function setChatModel(model?: string) {
  vscode.postMessage<CommonMessage>({
    type: 'COMMON',
    payload: {
      type: 'chatgpt-set-model',
      data: { model: model || '' },
    },
  });
}

/**
 * 设置模型 或 呼起设置模型下拉列表
 * @param model
 */
export function setChatModelIfNeed(model: string, features: string[] = []) {
  vscode.postMessage<CommonMessage>({
    type: 'COMMON',
    payload: {
      type: 'chatgpt-set-model-if-need',
      data: { model: model || '', features },
    },
  });
}

/**
 * 设置是否开启上下文会话
 * @param model
 */
export function setChatUseConversation(conversation: '开启' | '关闭' | '') {
  vscode.postMessage<CommonMessage>({
    type: 'COMMON',
    payload: {
      type: 'chatgpt-set-conversation',
      data: conversation === '开启' ? '关闭' : '开启',
    },
  });
}

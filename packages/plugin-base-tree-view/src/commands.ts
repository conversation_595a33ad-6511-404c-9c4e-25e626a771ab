import * as vscode from 'vscode';
import {
  setVscodeConfig,
  getVscodeConfig,
  getRemoteConfigSync,
  PLUGIN_ID,
  toggleViewShow,
  ViewSwitchKey,
  openInBrowser,
  isBusiness,
  JOYCODE_BRAND_NAME,
} from '@joycoder/shared';
import { treeViewWebview } from './viewProvider';

export const changeWorkSpace = async () => {
  const current = getVscodeConfig('JoyCoder.config.workSpaceId');
  let workSpaceList = getRemoteConfigSync().workSpaceConfig;

  // 将官方默认工作区移动到最前面
  const defaultWorkSpaceItem = workSpaceList.find((item) => item.teamID === 'joycoderfe');
  if (defaultWorkSpaceItem) {
    workSpaceList = workSpaceList.filter((item) => item.teamID !== 'joycoderfe');
    workSpaceList.unshift(defaultWorkSpaceItem);
  }

  const quickPick = vscode.window.createQuickPick();
  const items = workSpaceList.map((item) => {
    return {
      label: item.teamName,
      description: item.teamDesp,
      picked: current == item.teamID,
      workSpaceId: item.teamID,
    };
  });
  quickPick.items = items;
  quickPick.activeItems = quickPick.items.filter((item: any) => item.workSpaceId == current);
  quickPick.placeholder = `'根据开发场景选择工作区，以便${JOYCODE_BRAND_NAME}为您提供更加个性化的功能'`;
  quickPick.show();

  const target: any = await new Promise((resolve) => {
    quickPick.onDidAccept(() => resolve(quickPick.selectedItems[0]));
    quickPick.onDidHide(() => resolve(undefined));
  });

  quickPick.dispose();

  if (!target) return;

  await setVscodeConfig('JoyCoder.config.workSpaceId', target.workSpaceId);
  vscode.commands.executeCommand('workbench.action.reloadWindow');
};

// 切换AI助手
export function showAIChatView() {
  toggleViewShow(ViewSwitchKey.AIChatView, true);
  toggleViewShow(ViewSwitchKey.NavView, false);
  treeViewWebview?.postMessage({
    type: 'COMMON',
    payload: {
      type: 'switch-chat-view',
      data: { type: 'AIChat' },
    },
  });
}

export const commands = [
  // VSCode设置
  vscode.commands.registerCommand('JoyCoder.openConfigPage', () => {
    vscode.commands.executeCommand('workbench.action.openSettings', `@ext:${PLUGIN_ID}`);
  }),
  // VSCode快捷键设置
  vscode.commands.registerCommand('JoyCoder.openGlobalKeybindings', () => {
    vscode.commands.executeCommand('workbench.action.openGlobalKeybindings', `@ext:${PLUGIN_ID}`);
  }),
  // 导出历史会话
  vscode.commands.registerCommand('JoyCoder.downloadHistory', () => {
    treeViewWebview?.postMessage({
      type: 'COMMON',
      payload: {
        type: 'downloadChatHistory',
        data: { type: 'downloadHistory' },
      },
    });
  }),

  // // 切换为Chat
  // vscode.commands.registerCommand('joycoder.switch.chat', async () => {
  //   try {
  //     await vscode.workspace.getConfiguration().update('JoyCoder.switch.AgentView', false, false);
  //   } catch (e) {
  //     setVscodeConfig('JoyCoder.switch.AgentView', false);
  //   }
  //   vscode.commands.executeCommand('JoyCoder-left-view.focus');
  // }),
  // // 切换为Coder
  // vscode.commands.registerCommand('joycoder.switch.Coder', async () => {
  //   try {
  //     await vscode.workspace.getConfiguration().update('JoyCoder.switch.AgentView', true, false);
  //   } catch (e) {
  //     setVscodeConfig('JoyCoder.switch.AgentView', true);
  //   }
  //   vscode.commands.executeCommand('joycoder.joycoder.SidebarProvider.focus');
  // }),
];

if (isBusiness()) {
  commands.push(
    // 官方文档
    vscode.commands.registerCommand('JoyCoder.openHomePage', () => {
      openInBrowser('https://docs.jdcloud.com/cn/joycoder/product-overview');
    })
  );
} else {
  commands.push(
    // 切换工作区
    vscode.commands.registerCommand('JoyCoder.changeWorkSpace', () => changeWorkSpace()),
    // 切换AI助手
    vscode.commands.registerCommand('JoyCoder.view.aichat', () => showAIChatView()),
    // 切换工具箱
    vscode.commands.registerCommand('JoyCoder.view.nav', () => {
      toggleViewShow(ViewSwitchKey.NavView, true);
      toggleViewShow(ViewSwitchKey.AIChatView, false);
      treeViewWebview?.postMessage({
        type: 'COMMON',
        payload: {
          type: 'switch-chat-view',
          data: { type: 'ToolBox' },
        },
      });
    }),
    // 官方文档
    vscode.commands.registerCommand('JoyCoder.openHomePage', () => {
      openInBrowser('http://joycoder.jd.com/');
    }),
    // 工作区配置
    vscode.commands.registerCommand('JoyCoder.settingWorkSpace', () => {
      openInBrowser('https://dripworks-17tni50x-pro.local-pf.jd.com/#/');
    }),
    // 问题反馈
    vscode.commands.registerCommand('JoyCoder.bugReport', () => {
      openInBrowser('https://taishan.jd.com/xg/?nodeId=21912');
    }),
    // 常见问题
    vscode.commands.registerCommand('JoyCoder.openFAQ', () => {
      openInBrowser('http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/vscode/faq');
    }),
    // 咚咚群
    vscode.commands.registerCommand('JoyCoder.openME', () => {
      openInBrowser('http://joycoder.jd.com?timeline=1');
    })
  );
}

import * as vscode from 'vscode';
import { InlineCompletionItem, InlineCompletionList } from 'vscode';
import {
  startReportAction,
  getVscodeConfig,
  ActionType,
  getRemoteConfigSync,
  getMarkdownCodeBlockContent,
  GlobalState,
  Logger,
  isBusiness,
} from '@joycoder/shared';
import getDocumentLanguage from './utils/getDocumentLanguage';
import { getCodeCompletions, getPromptStr, checkCodeCommentAndEnter } from './utils/getCodeCompletions';
import { updateStatusBarItem } from './utils/updateStatusBarItem';
import { GetCodeCompletions } from '@joycoder/plugin-base-ai/src/proxy/codeCompletionsAsk';
import { isEqualCommentFlag, modelLabels } from './utils/constant';
import Debouncer from './utils/debouncer';
import { parsesWithoutMissLine, isSupportedLanguageId, parsesHtmlOrCSS } from './utils/elidableText';
import { getSwitchLanguageId } from './utils/codeLensProcider';
import {
  clearDecoration,
  currentEditorContentMatchesPopupItem,
  getContextInfo,
  getCurrentLineText,
  getNextContext,
  getNextLineData,
  getPrevContext,
  isAtTheMiddleOfLine,
  middleOfLineWontComplete,
  processInsertText,
  removeTrailingCharsByReplacement,
} from './utils/completionUntil';
import { getInnerModelNameToBusiness, setCompletionAccept } from './utils/util';
import AdoptResultCache from './stats/adoptResultCache';
import { findMissingBracket, needCheckBracketPair, simplifyStringBracket } from './utils/brackets';
import { getEditorTabSize } from './utils/shared';

// const prompts: string[] = [];
const maxLines = getVscodeConfig('JoyCoder.config.codeCompletionsMaxLines') || 1;
let onDidChangeTextDisposable: vscode.Disposable = new vscode.Disposable(() => {});
const debounceObj = new Debouncer();
let onDidChangeTextEditorSelectionDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let onDidChangeActiveTextEditorDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let activeTerminalChangeDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let decorationFn: any = null;

type CompletionAccept = {
  requestTotal: number; // 累计请求
  acceptTotal: number; // 累计采纳
  abortTotal: number; // 累计放弃
  writeLength: number; //手动编写代码
  aiLength: number; //ai编写代码
  requestList: number[]; // 最近10次采纳情况1采纳、0未采纳
};
//采纳率统计
const completionAccept: CompletionAccept = {
  requestTotal: 0, //累计请求
  acceptTotal: 0, //累计采纳
  abortTotal: 0, //未采纳
  writeLength: 0, //手动编写代码
  aiLength: 0, //ai编写代码
  requestList: [
    //最近10次采纳情况1采纳、0未采纳
  ],
};

/**
 * 提供代码补全功能。
 * @param g_isLoading - 是否正在加载。
 * @param myStatusBarItem - 状态栏项。
 * @param reGetCompletions - 是否重新获取补全项。
 * @param vsContext - VS Code扩展上下文。
 * @returns 代码补全结果。
 */
export default function codeCompletionProvider(
  g_isLoading: boolean,
  myStatusBarItem: vscode.StatusBarItem,
  reGetCompletions: boolean,
  // originalColor: string | vscode.ThemeColor | undefined,
  vsContext: vscode.ExtensionContext
) {
  let codeCompletionRes: any = { items: [] };
  try {
    codeCompletionRes = codeCompletionProviderContext(g_isLoading, myStatusBarItem, reGetCompletions, vsContext);
  } catch (error) {
    Logger.warn(error);
    return { items: [] };
  }
  //重置
  // timeDiff = 0;
  return codeCompletionRes;
}

/**
 * 提供代码补全功能的上下文。
 *
 * @param g_isLoading - 是否正在加载。
 * @param myStatusBarItem - 状态栏项目。
 * @param reGetCompletions - 是否重新获取补全。
 * @param vsContext - VS Code 扩展上下文。
 * @returns 代码补全提供者。
 */
export function codeCompletionProviderContext(
  g_isLoading: boolean,
  myStatusBarItem: vscode.StatusBarItem,
  _reGetCompletions: boolean,
  // originalColor: string | vscode.ThemeColor | undefined,
  vsContext: vscode.ExtensionContext
) {
  const provider: vscode.InlineCompletionItemProvider = {
    provideInlineCompletionItems: async (
      document,
      position,
      context: vscode.InlineCompletionContext,
      token: vscode.CancellationToken
    ): Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList | null | undefined> => {
      const timestart = new Date().getTime();

      //需要活动的文件
      const activeTextEditor = vscode.window.activeTextEditor;
      if (!activeTextEditor) {
        // vscode.window.showInformationMessage('请先打开一个文件');
        return;
      }
      //  手动触发
      const lastManualCompletionTimestamp = GlobalState.get('JoyCoder.lastManualCompletionTimestamp');
      //判断手动触发补全
      const isManualCompletion = Boolean(
        lastManualCompletionTimestamp && lastManualCompletionTimestamp > Date.now() - 600
      );
      GlobalState.update('JoyCoder.lastManualCompletionTimestamp', null);
      const rejectInlineCompletionNumber: any = await vsContext.globalState.get(
        'JoyCoder.rejectInlineCompletion.number'
      );
      // 默认连续200次不采纳就不再执行补全
      const completionsRejectTimes = getVscodeConfig('JoyCoder.config.completionsRejectTimes');
      if (Number(rejectInlineCompletionNumber) > Number(completionsRejectTimes) && !isManualCompletion) {
        await vsContext.globalState.update('enableCompletionCommand', false);
        updateStatusBarItem(myStatusBarItem, g_isLoading, false, '代码补全');
        return;
      }
      // 开启预测补全-命令模式
      const enableCompletionCommand: undefined | InlineCompletionList | InlineCompletionItem[] =
        await vsContext.globalState.get('enableCompletionCommand');
      //开启配置，同时触发快捷键
      if (!enableCompletionCommand && !isManualCompletion) {
        return;
      }
      //输入文字处理
      const cursorPosition = activeTextEditor.selection.active;
      let thisLine = getCurrentLineText(activeTextEditor, document) || '';
      //文件路径
      const filePath = activeTextEditor?.document?.uri?.fsPath || '';
      const projectName = vscode.workspace?.name || '';

      // 获取当前光标上文1行字符
      const prevRowCursor = getPrevContext(activeTextEditor, document, 1);
      const commentStatus = isEqualCommentFlag(prevRowCursor?.trim());
      // 获取当前光标上下文6行字符
      const prevCursor = getPrevContext(activeTextEditor, document, 6);
      const nextCursor = getNextContext(activeTextEditor, document, 6);
      //减少无用调用，解决光标之后有除括号空格之外内容，仍然补充造成的调用浪费
      const selectionNextChar: vscode.Selection = new vscode.Selection(
        cursorPosition.line,
        cursorPosition.character,
        cursorPosition.line,
        cursorPosition.character + 1
      );
      const nextChar = document.getText(selectionNextChar);
      let textBeforeCursor = prevCursor; // document.getText(selection);
      if (nextCursor) {
        textBeforeCursor = thisLine;
      }

      const checkStr = ']})\n\'"';
      const languageId = getSwitchLanguageId(document);
      try {
        const isSupportedLanguage = isSupportedLanguageId(languageId);
        const fns = isSupportedLanguage ? await parsesWithoutMissLine(languageId, document.getText()) : [];
        const notFnEnd = fns.find((item) => {
          if (cursorPosition.line >= item.endPosition.row + 1) {
            return item;
          }
        });
        const backList = getRemoteConfigSync().codeCompletionsBackListConfigs || [];
        const isHtmlOrCSS = await parsesHtmlOrCSS(prevRowCursor, backList);
        const fileType = getDocumentLanguage(activeTextEditor);
        // 由于模型对CSS和HTML推测不准确 所以这里需要判断HTML CSS 和 VUE文件
        const htmlORcss = backList.length > 0 && isHtmlOrCSS && backList.includes(fileType);
        const isCheckStr = textBeforeCursor !== '' && checkStr.includes(textBeforeCursor);
        const isCheckPrevRowCursor = checkStr.includes(prevRowCursor.trim());
        if (!commentStatus && (htmlORcss || isCheckStr || (!notFnEnd && isCheckPrevRowCursor))) {
          return;
        }
        // 判断光标后的下个字符是一个字母、数字或下划线
        if (
          context.triggerKind === vscode.InlineCompletionTriggerKind.Automatic &&
          /\w/.test(nextChar) &&
          !isManualCompletion
        ) {
          return;
        }
        // 当光标位于文件结尾行的开头且上一行为空时不触发
        if (
          context.triggerKind === vscode.InlineCompletionTriggerKind.Automatic &&
          position.line !== 0 &&
          position.line === document.lineCount - 1
        ) {
          const lineAbove = Math.max(position.line - 1, 0);
          if (document.lineAt(lineAbove).isEmptyOrWhitespace && !position.character) {
            return;
          }
        }
      } catch (error) {
        Logger.warn('%c [ error ]-197', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      }

      // 判断光标位置是否在行首且前一个字符不是换行符，如果是则在光标前添加一个换行符
      if (cursorPosition.character === 0 && textBeforeCursor[textBeforeCursor.length - 1] !== '\n') {
        textBeforeCursor += '\n';
      }
      // 获取当前激活的编辑器，并获取当前选中列的索引。
      if (vscode.window.activeNotebookEditor) {
        const cells = vscode.window.activeNotebookEditor.notebook.getCells();
        const currentCell = vscode.window.activeNotebookEditor.selection.start;
        let str = '';
        for (let i = 0; i < currentCell; i++) {
          str += cells[i].document.getText().trimEnd() + '\n';
        }
        textBeforeCursor = str + textBeforeCursor;
      }

      if (middleOfLineWontComplete(activeTextEditor, document) && !isManualCompletion) {
        console.log('不进行补充');
        updateStatusBarItem(myStatusBarItem, g_isLoading, false, '');
        return;
      }
      const checkString = ']}) \n\t\'"';
      //获取模型配置
      const gptCompletionModel: string = getInnerModelNameToBusiness(
        (await vsContext.globalState.get('JoyCoder.gptCompletionModel')) || 'JoyCoder-Base-Lite'
      );
      const gptCompletionOption = (await vsContext.globalState.get('JoyCoder.gptCompletionOption')) || null;
      if (!checkString.includes(nextChar) && !isManualCompletion) {
        return;
      }
      //  根据模型判断执行
      const modelName = getInnerModelNameToBusiness('JoyCoder-Base');
      if (modelLabels.includes(gptCompletionModel) || gptCompletionModel?.startsWith(modelName)) {
        //防抖
        try {
          const completionDelay = GlobalState.get('JoyCoder.completion.delay') || 1.2;
          const delay: number = completionDelay * 1000;
          await debounceObj.debounce(delay);
        } catch {
          Logger.log('思考中..');
          return;
        }

        // console.log('预测补全');
        let rs: GetCodeCompletions | null = null;
        let lang = '';
        let prevCode = ''; //前1000行
        let nextCode = ''; //后400行
        let isCommentEnd = false;
        try {
          if (activeTextEditor) {
            lang = getDocumentLanguage(activeTextEditor);
            if (!lang) {
              // 如果未识别出来语言 直接返回
              updateStatusBarItem(myStatusBarItem, g_isLoading, false, ' 无建议');
              return;
            }
          }
          // if (isEqualCommentFlag(prevRowCursor) && !checkCodeCommentAndEnter(prevRowCursor)) {
          //   return;
          // }
          // 当用户打开自动完成弹出窗口，并且选中的项目与编辑器中已有的文本不匹配时，
          // VS Code 将永远不会渲染（显示）该自动完成项。
          if (!currentEditorContentMatchesPopupItem(document, context)) {
            return;
          }

          updateStatusBarItem(myStatusBarItem, g_isLoading, true, ' JoyCoder思考中✨');

          // const modelName = getInnerModelNameToBusiness('JoyCoder-Base');
          const related_files = await getContextInfo({
            document,
            prevCursor,
            nextCursor,
          }); //  sameFolderFilesText + importContextText + snippetsText + currentFileName +
          const commentEnd = checkCodeCommentAndEnter(prevRowCursor.replace(/ /g, ''), lang);
          const prevContext = getPrevContext(activeTextEditor, document, 800);
          const prevContextLines = prevContext.split('\n').length;
          isCommentEnd = commentEnd && prevContextLines < 6;
          prevCode = commentEnd && prevContextLines < 6 ? prevRowCursor : prevContext;
          thisLine = commentEnd ? prevRowCursor : thisLine;
          nextCode = getNextContext(activeTextEditor, document, 300);

          //请求调用
          rs = await getCodeCompletions(
            thisLine,
            lang,
            {
              nextCursor,
              prevCursor,
              prevCode,
              nextCode,
              filePath,
              related_files,
              projectName,
              maxLines,
              isPrevRowComment: commentStatus,
            },
            gptCompletionModel,
            gptCompletionOption
          );
          const timeend = new Date().getTime();
          Logger.log('代码预测补全-执行时间', timeend - timestart);
        } catch (err) {
          if (err) {
            Logger.warn('代码预测补全请求异常');
          }
          updateStatusBarItem(myStatusBarItem, g_isLoading, false, ' 无建议');
          return { items: [] };
        }
        // 添加生成代码
        const items: InlineCompletionItem[] | InlineCompletionList | null | undefined = [];
        const tabSize = getEditorTabSize(document.uri, vscode.workspace, vscode.window);
        const cursorPosition = activeTextEditor.selection.active;
        if (rs === null || !rs.completions || rs.completions.length === 0) {
          const res = findMissingBracket(textBeforeCursor, nextCode, document.languageId);
          const isNeedCheckBracketPair = needCheckBracketPair(document);
          const numberIndent = simplifyStringBracket(textBeforeCursor, document.languageId).length;
          const bracketIndent = (numberIndent - 1 > 0 ? numberIndent - 1 : 0) * tabSize;
          if (res && res.length > 0 && isNeedCheckBracketPair) {
            if (thisLine.trim() !== '') {
              const isStart = (textBeforeCursor + nextCode).startsWith(
                textBeforeCursor + '\n' + ' '.repeat(bracketIndent) + res[0]
              );
              if (isStart) {
                return;
              }
              const item = {
                insertText: '\n' + ' '.repeat(bracketIndent) + res[0],
                range: new vscode.Range(cursorPosition, cursorPosition),
                command: {
                  command: 'JoyCoder.completion.get-new-completion',
                  title: '自动触发补全',
                },
              };
              items.push(item);
              updateStatusBarItem(myStatusBarItem, g_isLoading, false, ' JoyCoder完成');
              return items;
            }
            if ((textBeforeCursor + nextCode).startsWith(textBeforeCursor + res[0])) {
              return;
            }
            const item = {
              insertText: res[0],
              range: new vscode.Range(
                cursorPosition.translate(0, bracketIndent - cursorPosition.character),
                cursorPosition
              ),
              command: {
                command: 'JoyCoder.completion.get-new-completion',
                title: '自动触发补全',
              },
            };
            items.push(item);
            updateStatusBarItem(myStatusBarItem, g_isLoading, false, ' JoyCoder完成');
            return items;
          } else {
            updateStatusBarItem(myStatusBarItem, g_isLoading, false, ' 无建议');
            return { items: [] };
          }
        }
        // prompts.push(textBeforeCursor);
        for (let i = 0; i < rs.completions.length; i++) {
          let insertText = `${rs.completions[i]}`;
          if (isAtTheMiddleOfLine(activeTextEditor, document)) {
            const cursorPosition = activeTextEditor.selection.active;
            const currentLine = document?.lineAt(cursorPosition.line);
            const lineEndPosition = currentLine?.range.end;

            const selectionTrailingString: vscode.Selection = new vscode.Selection(
              cursorPosition.line,
              cursorPosition.character,
              cursorPosition.line,
              lineEndPosition.character + 1
            );
            const trailingString = document.getText(selectionTrailingString);
            insertText = removeTrailingCharsByReplacement(insertText, trailingString);
            if (
              insertText.trimEnd().slice(-1) === '{' ||
              insertText.trimEnd().slice(-1) === ';' ||
              insertText.trimEnd().slice(-1) === ':'
            ) {
              insertText = insertText.trimEnd().substring(0, insertText.length - 1);
            }
          }
          //获取markdown内部代码
          if (insertText && insertText.includes('```')) {
            insertText = getMarkdownCodeBlockContent(insertText);
          }
          try {
            insertText = await processInsertText({
              insertText,
              textBeforeCursor,
              thisLine,
              nextCursor,
              document,
              cursorPosition,
            });
            const prevRow = getPrevContext(vscode.window.activeTextEditor, document, 1);
            const nextRow = getNextLineData();
            if (!insertText || nextRow.trim() === insertText || prevRow.trim() === insertText) {
              updateStatusBarItem(myStatusBarItem, g_isLoading, false, '无建议');
              return { items: [] };
            }
            // insertText = insertMissingBrackets(insertText);
          } catch (error) {
            return { items: [] };
          }
          items.push({
            insertText,
            range: !!nextChar
              ? new vscode.Range(cursorPosition, cursorPosition)
              : new vscode.Range(cursorPosition.translate(0, rs?.completions?.length), cursorPosition),
            command: {
              command: 'JoyCoder.completion.formatAccept',
              title: '格式化采纳',
            },
          });

          updateStatusBarItem(myStatusBarItem, g_isLoading, false, insertText ? 'JoyCoder完成' : '无建议');
          // isCancellationRequested代表用户有其它操作，可以取消后续所有操作了
          if (!insertText || token.isCancellationRequested) return { items: [] };
          setChangeTextDocument(
            textBeforeCursor,
            lang,
            nextCursor,
            prevCursor,
            insertText,
            cursorPosition,
            rs,
            vsContext,
            isCommentEnd
          );
          const hideTip = await vsContext.globalState.get('JoyCoder.hideTip');
          if (insertText.trim().length > 0 && !hideTip) {
            decorationFn = decoration(
              activeTextEditor,
              {
                range: new vscode.Range(cursorPosition.translate(0, rs?.completions?.length), cursorPosition),
              },
              vsContext
            );
          }
          // 记录返回次数 如果采纳次数归零
          vsContext.globalState.update(
            'JoyCoder.rejectInlineCompletion.number',
            Number(rejectInlineCompletionNumber) + 1
          );
        }
        //预测一次后关闭
        return items;
      }
      updateStatusBarItem(myStatusBarItem, g_isLoading, false, ' 无建议');
      return { items: [] };
    },
  };
  return provider;
}

/**
 * 异步函数，用于处理文本文档的更改。
 * @param textBeforeCursor 光标前的文本
 * @param lang 代码语言
 * @param nextCursor 光标后的文本
 * @param prevCursor 光标前的文本
 * @param insertText 插入的文本
 * @param cursorPosition 光标位置
 * @param rs 获取代码补全的结果
 * @param vsContext VSCode扩展上下文
 */
async function setChangeTextDocument(
  textBeforeCursor: string,
  lang: string,
  nextCursor: string,
  prevCursor: string,
  insertText: string,
  cursorPosition: vscode.Position,
  rs: GetCodeCompletions,
  vsContext: vscode.ExtensionContext,
  isChat?: boolean
) {
  try {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;
    // GPT补全上报
    const question = await getPromptStr(
      textBeforeCursor,
      lang,
      {
        nextCursor,
        prevCursor,
      },
      ''
    );
    const gptCompletionModel: string = vsContext.globalState.get('JoyCoder.gptCompletionModel') || 'llm';
    const gptCompletionChat: any = vsContext.globalState.get('JoyCoder.gptCompletionOption');
    const chatModelConfig = gptCompletionChat?.chat || {};
    const chatModel = chatModelConfig?.model || 'Chatrhino-81B';
    // 接口返回代码开始上报
    Logger.log('report--start', insertText);
    // 补全预测结果缓存，用于后续AI代码占比统计
    AdoptResultCache.setRemote(
      insertText,
      gptCompletionModel,
      AdoptResultCache.ADOPT_CODE_SOURCE.COMPLETION,
      rs.completionId || ''
    );
    // 跨文件感知开启与否
    const codeCompletionsMoreContext = getVscodeConfig('JoyCoder.config.codeCompletionsMoreContext');
    const reporter = startReportAction({
      actionCate: 'ai',
      actionType: ActionType.completion,
      question,
      model: isChat ? chatModel : gptCompletionModel,
      // @completion专用 代码语言
      codeLanguage: lang,
      // @completion专用 温度
      temperature: rs.temperature?.toString(),
      // @completion专用 光标位置
      offset: editor?.document.offsetAt(cursorPosition),
      // @completion专用 前缀代码
      prefixCode: prevCursor,
      // @completion专用 后缀代码
      sufixCode: nextCursor,
      // @completion专用 采纳选项数据，json串
      choices: rs.completions.map((codeStr, index) => ({ completionCode: codeStr, index })),
      // 将completionId当做conversationId
      conversationId: rs.completionId,
      extendMsg: {
        CROSS_FILES: codeCompletionsMoreContext ? '1' : '0',
      },
    });
    // let completionAcceptText = rs.completions.map((codeStr) => codeStr).join('') ?? '';
    // if (completionAcceptText && completionAcceptText.includes('```')) {
    //   completionAcceptText = getMarkdownCodeBlockContent(insertText);
    // }
    //  补全操作函数
    setCompletionAccept({ reporter, completionAccept, insertText, vsContext, textBeforeCursor, isChat, chatModel });
    // TODO: 后续补全的逻辑也改成注册一个onDidChangeTextDocument事件即可
    onDidChangeTextDisposable && onDidChangeTextDisposable.dispose();
    onDidChangeTextDisposable = vscode.workspace.onDidChangeTextDocument(async (event) => {
      const editor = vscode.window.activeTextEditor;
      if (event.contentChanges.length > 0) {
        // 文档内容变化了，可能是补全项被接受了
        // inlineCodeLensDisposable && inlineCodeLensDisposable.dispose();
        clearDecoration(decorationFn, editor);
      }
      if (editor && event.document === editor.document) {
        const changes = event.contentChanges;
        const lastChange = changes[changes.length - 1];
        const insertedText = lastChange?.text;
        if (insertText && insertedText && insertText.indexOf(insertedText) === -1) {
          // 文本变化的监听，可能会触发多次，但是reporter里有闭包isDone判断，不会重复上报
          reporter.fail({ result: insertText }, () => {
            completionAccept.abortTotal += 1;
            completionAccept.requestList.push(0);
            completionAccept.requestList = completionAccept.requestList.slice(-10);
            if (!isBusiness()) {
              Logger.info('completionAccept', JSON.stringify(completionAccept));
            }
          });
        }
        //一个字符，用户手动输入的
        if (insertedText !== insertText && `${insertedText}`.length === 1) {
          completionAccept.writeLength += 1;
        }
      }
    });

    vsContext.subscriptions.push(onDidChangeTextDisposable);
  } catch (error) {
    Logger.error('%c [ error ]-417', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}

// 添加装饰器
export function decoration(activeEditor, _decorationRange, context) {
  const decorationType = vscode.window.createTextEditorDecorationType({
    light: {
      // 这将应用于亮色主题
      after: {
        color: 'rgba(0, 0, 0, 0.4)',
        contentText: '👈JoyCoder自动补全，Tab或者Enter键采纳',
        margin: '0 0 0 20px', // 根据需要调整边距
      },
    },
    dark: {
      // 这将应用于暗色主题
      after: {
        contentText: '👈JoyCoder自动补全，Tab或者Enter键采纳',
        color: 'rgba(255, 255, 255, 0.4)',
        margin: '0 0 0 20px', // 根据需要调整边距
      },
    },
  });

  if (activeEditor) {
    const cursorPosition = activeEditor.selection.active;
    const currentLine = activeEditor.document?.lineAt(cursorPosition.line);
    const lineEndPosition = currentLine?.range.end;
    const decoration = {
      range: new vscode.Range(cursorPosition, lineEndPosition),
    };
    decorationFn && activeEditor.setDecorations(decorationFn, []);
    activeEditor.setDecorations(decorationType, [decoration]);
    setTimeout(() => {
      activeEditor.setDecorations(decorationType, []);
    }, 5000);
  }
  onDidChangeTextEditorSelectionDisposable && onDidChangeTextEditorSelectionDisposable.dispose();
  onDidChangeTextEditorSelectionDisposable = vscode.window.onDidChangeTextEditorSelection((event) => {
    if (event.kind === vscode.TextEditorSelectionChangeKind.Command) {
      // 光标位置因为命令（可能是补全命令）而变化了
      // inlineCodeLensDisposable && inlineCodeLensDisposable.dispose();
      clearDecoration(decorationFn);
    }
    if (event.selections[0]) {
      // inlineCodeLensDisposable && inlineCodeLensDisposable.dispose();
      clearDecoration(decorationFn);
    }
    if (!(event.textEditor === vscode.window.activeTextEditor)) {
      // 光标不在编辑区
      clearDecoration(decorationFn);
    }
  });
  onDidChangeActiveTextEditorDisposable && onDidChangeActiveTextEditorDisposable.dispose();
  onDidChangeActiveTextEditorDisposable = vscode.window.onDidChangeActiveTextEditor((editor) => {
    if (editor) {
      // 当活动编辑器改变时，这里的代码会被执行
      // inlineCodeLensDisposable && inlineCodeLensDisposable.dispose();
      clearDecoration(decorationFn);
    }
  });
  // 监听活动终端的改变
  activeTerminalChangeDisposable && activeTerminalChangeDisposable.dispose();
  activeTerminalChangeDisposable = vscode.window.onDidChangeWindowState((event) => {
    if (!event?.focused) {
      clearDecoration(decorationFn);
    }
  });
  context.subscriptions.push(onDidChangeTextEditorSelectionDisposable);
  context.subscriptions.push(onDidChangeActiveTextEditorDisposable);
  context.subscriptions.push(activeTerminalChangeDisposable);
  return decorationType;
}

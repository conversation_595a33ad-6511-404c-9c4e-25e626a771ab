import * as vscode from 'vscode';
import { v5 as uuidv5 } from 'uuid';
import { getJdhLoginInfo, hasJdhLoginCookie, pluginVersion } from '@joycoder/shared';
import getDocumentLanguage from '../utils/getDocumentLanguage';
import { codeStatService } from './codeStatService';
import AdoptResultCache from './adoptResultCache';
import fileChangeMonitor, { FileChangeInfo } from './fileChangeMonitor';

// 最近一次的文档快照
let originText = '';

export async function hookCodeStatistics(context: vscode.ExtensionContext) {
  // 注册事件监听，将文档变化数据采集上报到GO程序
  context.subscriptions.push(
    vscode.workspace.onDidChangeTextDocument((e) => registerCodeStatisticsListenForTextEditor(e))
  );
  // 启动服务GO服务
  codeStatService.checkAndStartService();
  // 注册立即上报的命令
  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCoder.ai.log.reportImmediate', () => {
      codeStatService.sendReportImmediate();
    })
  );
  // 维护文件第一次打开的快照：originText，用于convertTextDocumentChangeEvent中计算位置
  originText = vscode.window.activeTextEditor?.document.getText() || '';
  context.subscriptions.push(
    vscode.window.onDidChangeActiveTextEditor((editor: vscode.TextEditor) => {
      // 文件切换后的立即上报，由GO的二进制来切换
      // codeStatService.sendReportImmediate();
      if (editor) {
        originText = editor.document.getText();
      }
    })
  );

  //文件移动、复制粘贴监听
  try {
    fileChangeMonitor(context, (fileChangeInfo: FileChangeInfo) => {
      console.log('fileChangeMonitor', fileChangeInfo);
      codeStatService.sendCodeChangeToStat(fileChangeInfo);
    });
  } catch (error) {
    console.error('fileChangeMonitor error', error);
  }
}

export async function registerCodeStatisticsListenForTextEditor(e: vscode.TextDocumentChangeEvent) {
  const isLogin = !!hasJdhLoginCookie();
  const activeTextEditor = vscode.window.activeTextEditor;
  if (!isLogin || !activeTextEditor) {
    return;
  }

  // 针对特定类型的语言进行统计
  const lang = getDocumentLanguage(activeTextEditor);
  if (!lang) {
    return;
  }

  if (e.document != null && e.document.uri.scheme === 'file') {
    // 生成JSON文档
    if (e.contentChanges.length > 0) {
      const docChangeList: any[] = convertTextDocumentChangeEvent(e);
      // 对数据进行统计
      docChangeList.forEach((d) => {
        codeStatService.sendCodeChangeToStat(d);
      });
    }
  }
}
/**
 * 将事件中的参数信息转换为统计程序可以识别的格式
 *
 * @param e 文档变更事件
 * @param adoptType 补全类型
 * @returns
 */
function convertTextDocumentChangeEvent(e: vscode.TextDocumentChangeEvent) {
  const doc = e.document;
  const contentChange = e.contentChanges[e.contentChanges.length - 1];
  const loginInfo = getJdhLoginInfo();
  const docChangeList: any[] = [];
  // 文本信息
  const docChangeBasicInfo = {
    text: doc.getText(),
    filename: doc.uri.fsPath,
    uuid: uuidv5(doc.uri.fsPath, uuidv5.URL),
    // 文档语言
    lang: doc.languageId,
    // 当前的毫秒数
    timeMills: new Date().getTime(),
    // 变更的位置
    offset: contentChange.rangeOffset,
    userName: loginInfo?.erp || loginInfo?.userName,
    ideType: 'vscode',
    projectPath: getCurrentProjectPath(), //当前变动文档对应的项目全路径
    pluginVersion: pluginVersion,
    status: 1,
  };

  const {
    type: adoptType,
    text: adoptText,
    codeSource,
    model,
    conversationId,
  } = AdoptResultCache.getAndClearAdopt(contentChange.text);

  if (contentChange.text) {
    if (contentChange.rangeLength) {
      // 先发送删除操作
      const docDelObj = JSON.parse(JSON.stringify(docChangeBasicInfo));
      docDelObj['text'] =
        docChangeBasicInfo.text.substring(0, docChangeBasicInfo.offset) +
        docChangeBasicInfo.text.substring(docChangeBasicInfo.offset + contentChange.text.length);
      docDelObj['newStr'] = '';
      docDelObj['newLen'] = 0;
      docDelObj['oldStr'] = originText?.substring(
        contentChange.rangeOffset,
        contentChange.rangeOffset + contentChange.rangeLength
      );
      docDelObj['oldLen'] = contentChange.rangeLength;
      docChangeList.push(docDelObj);
    }
    // 再发送新增操作
    const docChangeObj = docChangeBasicInfo;
    docChangeObj['newStr'] = contentChange.text;
    docChangeObj['newLen'] = contentChange.text.length;
    docChangeObj['oldStr'] = '';
    docChangeObj['oldLen'] = 0;
    if (
      // adoptType判断是否有AI预测的代码。全等判断为了兼容某些极端异常的情况
      contentChange.text.replace(/\r\n/g, '\n') === adoptText ||
      adoptType === AdoptResultCache.TYPE.REMOTE_JOY_CODER
    ) {
      docChangeObj['adoptText'] = adoptText;
      docChangeObj['adoptType'] = adoptType;
      docChangeObj['codeSource'] = codeSource;
      docChangeObj['model'] = model;
      docChangeObj['itemId'] = conversationId;
    } else if (adoptType === AdoptResultCache.TYPE.LOCAL_SYSTEM) {
      // TODO: 目前实际没有上报本地系统预测的结果（exp: VSCode中通过ts类型推断的联想），统计侧反馈不care
      docChangeObj['adoptText'] = adoptText;
      docChangeObj['adoptType'] = adoptType;
      docChangeObj['codeSource'] = codeSource;
    }
    docChangeList.push(docChangeObj);
  } else {
    const docChangeObj = docChangeBasicInfo;
    docChangeObj['newStr'] = '';
    docChangeObj['newLen'] = 0;
    docChangeObj['oldStr'] = originText?.substring(
      contentChange.rangeOffset,
      contentChange.rangeOffset + contentChange.rangeLength
    );
    docChangeObj['oldLen'] = contentChange.rangeLength;
    docChangeList.push(docChangeObj);
  }
  originText = doc.getText();

  return docChangeList;
}

/**
 * 获取当前文件所属项目的根目录路径
 * @returns {string} 当前文件所属项目的根目录路径或提示信息
 */
export function getCurrentProjectPath() {
  // 获取当前活动的文本编辑器
  try {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      // 获取当前文档的 URI
      const documentUri = editor.document.uri;
      // 获取当前文档所属的工作区文件夹
      const workspaceFolder = vscode.workspace.getWorkspaceFolder(documentUri);
      if (workspaceFolder) {
        // 获取工作区文件夹的路径
        return workspaceFolder.uri.fsPath;
      } else {
        return '';
      }
    } else {
      return '';
    }
  } catch (error) {
    console.log('🚀 ~ getCurrentProjectPath ~ error:', error);
    return '';
  }
}

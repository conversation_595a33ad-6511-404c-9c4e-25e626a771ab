import * as vscode from 'vscode';
import to from 'await-to-js';
import { serializeError } from 'serialize-error';
import {
  Logger,
  reportRd,
  initReportRdCommand,
  getRemoteConfigAsync,
  onChangeActiveTextEditor,
  initContextVariable,
  reportFirstInstallTimes,
  registerRouter,
  GlobalState,
  // globalLogin,
  initLogOutCommand,
  GitStateManager,
  updateJdhLoginInfo,
  getJdhLoginInfo,
  setGlobalConfig,
} from '@joycoder/shared';
import showChangeLog, { initCheckUpdateCommand, showUpgradeGuide } from '@joycoder/version';
import syncFeConfigToJoyCoder from '@joycoder/version/src/syncFeConfigToJoyCoder';
import initBaseAI from '@joycoder/plugin-base-ai';
import initBaseAID2C from '@joycoder/plugin-base-ai-d2c';
import initBaseUpLoadImage from '@joycoder/plugin-base-upload-image';
import initBaseMaterialImport from '@joycoder/plugin-base-material-import';
import initBaseHoverProvider from '@joycoder/plugin-base-hover-provider';
import initCustomHoverProvider from '@joycoder/plugin-custom-hover-provider';
import initBaseImportAutoComplete from '@joycoder/plugin-base-import-auto-complete';
import initBaseStyleHelper from '@joycoder/plugin-base-style-helper';
import initBaseDynamicSnippets from '@joycoder/plugin-base-dynamic-snippets';
import initBaseTreeView from '@joycoder/plugin-base-tree-view';
import initBaseQuickJump from '@joycoder/plugin-base-quick-jump';
import initCustomQuickJump from '@joycoder/plugin-custom-quick-jump';
import initCustomActive from '@joycoder/plugin-custom-active';
import initBaseAppManager from '@joycoder/plugin-base-app-manager';
import initBaseTaroHelper from '@joycoder/plugin-base-taro';
import initBaseCodeDetection from '@joycoder/plugin-base-code-detection';
import initBaseCliFactory from '@joycoder/plugin-base-cli-factory';
import initBaseQuickConsole from '@joycoder/plugin-base-quick-console';
import initBaseCodeCompletion, { stopCodeStatService } from '@joycoder/plugin-base-code-completion';
import initBaseCodeReview from '@joycoder/plugin-base-code-review';
import initBaseBrowser from '@joycoder/plugin-base-browser';
import initBaseSec from '@joycoder/plugin-base-sec';
import initAgentDriven from '@joycoder/agent-driven/initAgentDriven';

export async function activate(context: vscode.ExtensionContext) {
  // 注册监听路由事件
  registerRouter();

  // 初始化使用globalState需要的context
  GlobalState.init(context);

  // 全局登录
  // globalLogin();
  initLogOutCommand(context);

  // 定义输出渠道，方便在vscode-输出中查看日志
  Logger.channel = vscode.window.createOutputChannel('JoyCoder', { log: true });
  context.subscriptions.push(Logger.channel);
  Logger.log('JoyCoder插件激活成功啦~');

  await syncFeConfigToJoyCoder();
  setGlobalConfig({
    joyCoderBaseUrl: (vscode.env as any).joyCoderBaseUrl,
    joyCoderVersion: (vscode.env as any).joyCoderVersion,
  });

  // 请求工作区远程配置
  const [err] = await to(getRemoteConfigAsync());
  if (err) {
    Logger.showErrorMessage(
      `远程配置拉取失败，部分功能不可用，请稍后重新加载窗口~${JSON.stringify(serializeError(err))}`
    );
  }
  // 自动化编程
  initAgentDriven(context);

  initBaseCodeCompletion(context); //代码预测
  initBaseCodeReview(context); //代码评审

  // 公共能力
  initBaseCliFactory(context);
  initBaseUpLoadImage(context);
  initBaseHoverProvider(context);
  initBaseStyleHelper(context);
  // initBaseMarketplace(context);
  initBaseMaterialImport(context);
  initBaseTreeView(context);
  initReportRdCommand(context);
  initBaseQuickJump(context);
  initBaseAppManager(context);
  initBaseCodeDetection(context);
  initBaseImportAutoComplete(context);
  initBaseQuickConsole(context);
  initBaseAI(context);
  initBaseAID2C(context);
  initBaseTaroHelper(context);
  initBaseBrowser(context);

  // 自定义能力
  initCustomQuickJump(context);
  initCustomHoverProvider(context);
  initCustomActive(context);

  // 其他
  GitStateManager.init();
  onChangeActiveTextEditor(context);
  initCheckUpdateCommand(context);
  initContextVariable();
  initBaseDynamicSnippets();
  reportRd(1);
  reportFirstInstallTimes();
  showChangeLog();
  showUpgradeGuide();
  initBaseSec(context);
  return {
    updateJdhLoginInfo,
    getJdhLoginInfo,
  };
}

// this method is called when your extension is deactivated
export function deactivate() {
  try {
    // 一次reload window便可使snippet生效
    initBaseDynamicSnippets();
    // 停止代码采集服务
    stopCodeStatService();
  } catch (error: any) {
    Logger.error(error);
  }
}

{"private": true, "name": "@joycoder/agent-runtime", "description": "<PERSON>, the code AI that writes code, fixes bugs, and answers questions using the code graph", "license": "", "homepage": "", "engines": {"node": ">=18", "pnpm": ">=8.6.7"}, "browser": {"fs": false}, "scripts": {"agent": "pnpm -C agent agent", "build": "tsc --build", "watch": "tsc --build --watch", "check": "pnpm run -s check:build && pnpm run -s biome && pnpm run -s check:css", "check:css": "stylelint --quiet --cache '**/*.css'", "check:build": "pnpm run -C vscode check:build", "biome": "biome check --apply --error-on-warnings .", "test": "vitest", "test:unit": "vitest run", "test:integration": "pnpm -C vscode test:integration", "test:e2e": "pnpm -C vscode test:e2e", "test:local-e2e": "RUN_LOCAL_E2E_TESTS=true pnpm test agent/src/local-e2e/template.test.ts", "generate-agent-kotlin-bindings": "./agent/scripts/generate-agent-kotlin-bindings.sh", "update-agent-recordings": "pnpm build && CODY_KEEP_UNUSED_RECORDINGS=false CODY_RECORD_IF_MISSING=true vitest agent/src", "update-agent-recordings-windows": "PowerShell -ExecutionPolicy Bypass -Command \"pnpm build; if ($?) { $Env:CODY_KEEP_UNUSED_RECORDINGS='false'; $Env:CODY_RECORD_IF_MISSING='true'; vitest agent/src }\"", "update-rewrite-recordings": "rm -rf recordings && CODY_RECORD_IF_MISSING=true vitest vscode/src/local-context/rewrite-keyword-query.test.ts", "openctx:link": "cd ../openctx && pnpm -C lib/client link --global && pnpm -C lib/schema link --global && pnpm -C lib/protocol link --global && pnpm -C client/vscode-lib link --global && cd ../cody && pnpm link --global @openctx/client && pnpm link --global @openctx/schema &&  pnpm link --global @openctx/protocol && cd vscode && pnpm link --global @openctx/vscode-lib", "openctx:unlink": "pnpm unlink --global @openctx/client && pnpm unlink --global @openctx/schema &&  pnpm unlink --global @openctx/protocol && cd vscode && pnpm unlink --global @openctx/vscode-lib", "vsce-version-bump": "pnpm -C vscode version-bump:minor"}, "devDependencies": {"@types/fast-json-stable-stringify": "^2.1.0", "@biomejs/biome": "1.7.2", "@sourcegraph/tsconfig": "^4.0.1", "@storybook/addon-essentials": "^8.0.5", "@storybook/react": "^8.0.5", "@storybook/react-vite": "^8.0.5", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.2", "@types/fs-extra": "^11.0.4", "@types/node": "20.12.7", "@types/react": "18.2.79", "@types/react-dom": "18.2.25", "@vitejs/plugin-react": "^4.2.1", "esbuild": "^0.18.20", "fs-extra": "^11.2.0", "happy-dom": "^14.3.10", "jsdom": "^22.1.0", "knip": "^5.27.4", "storybook": "^8.0.5", "stylelint": "^15.11.0", "stylelint-config-standard": "^34.0.0", "ts-node": "^10.9.2", "typescript": "^5.5.4", "vite": "^5.4.2", "vitest": "^2.0.5"}, "dependencies": {"@openctx/client": "^0.0.29", "@sourcegraph/telemetry": "^0.18.0", "observable-fns": "^0.6.1", "react": "18.2.0", "react-dom": "18.2.0", "vscode-jsonrpc": "^8.2.0", "web-tree-sitter": "^0.21.0", "win-ca": "^3.5.1"}, "packageManager": "pnpm@8.6.7", "resolutions": {"tslib": "2.1.0"}, "pnpm": {"overrides": {"@lexical/react": "https://storage.googleapis.com/sourcegraph-assets/npm/lexical-react-sourcegraph-fork-31065486.tgz"}, "packageExtensions": {"@vscode/webview-ui-toolkit": {"dependencies": {"@microsoft/fast-web-utilities": "^6.0.0", "tslib": "^2.1.0"}}}, "neverBuiltDependencies": ["deas<PERSON>", "playwright"]}, "version": "2.9.0"}
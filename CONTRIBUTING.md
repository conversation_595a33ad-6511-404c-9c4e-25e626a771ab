# 贡献指南

很高兴您有兴趣为本项目做贡献。在提交您的贡献之前，请确保花一点时间通读以下准则：

## 项目结构

📦JoyCoder-VSCode // 主插件 JoyCoder 项目目录  
　 ┣ 📂assets // 图片等资源  
　 ┣ 📂dist // webpack 打包出口  
　 ┣ 📂scripts // 项目脚本  
　 ┣ 📂snippets // 代码片段(含片段源文件和动态生成的片段文件)  
　 ┣ 📂packages // 公共包和插件  
　　 ┣ 📂agent-common // 多 agent 能力
　　 ┣ 📂plugin-base-ai // plugin-base 表示通用的基础能力  
　　 ┣ 📂plugin-base-ai-d2c  
　　 ┣ 📂plugin-base-app-manager  
　　 ┣ 📂plugin-base-cli-factory  
　　 ┣ 📂plugin-base-code-completion  
　　 ┣ 📂plugin-base-code-detection  
　　 ┣ 📂plugin-base-code-review  
　　 ┣ 📂plugin-base-sec
　　 ┣ 📂plugin-base-dynamic-snippets  
　　 ┣ 📂plugin-base-hover-provider  
　　 ┣ 📂plugin-base-import-auto-complete  
　　 ┣ 📂plugin-base-material-import  
　　 ┣ 📂plugin-base-quick-console  
　　 ┣ 📂plugin-base-quick-jump  
　　 ┣ 📂plugin-base-style-helper  
　　 ┣ 📂plugin-base-taro  
　　 ┣ 📂plugin-base-tree-view  
　　 ┣ 📂plugin-base-upload-image  
　　 ┣ 📂plugin-custom-condition-compile // plugin-custom 表示定制的插件功　　能
　　 ┣ 📂plugin-custom-hover-provider  
　　 ┣ 📂plugin-custom-quick-jump  
　　 ┣ 📂shared // 插件共享的基础工具函数
　　 ┣ 📂version // 插件版本相关，如更新提示
　　 ┣ 📂vscode-business // 商业化版本插件  
 ┣ 📂vscode-IDE // IDE 版本插件  
　　 ┣ 📂vscode-plugin-demo // 插件包示例  
　　 ┣ 📂web // 插件共用的 H5 容器及页面
　　 ┣ 📂src  
　　　 ┃ 📜extension.ts // 插件入口文件  
　　 ┃ 📜package.json // 项目信息及主插件信息

## 安装环境

您需要 Node.js 版本 v16.14+PNPM 版本 v8，建议使用 n 管理 Node 版本。注意[node 和 pnpm 的版本对应关系](https://pnpm.io/zh/installation#%E5%85%BC%E5%AE%B9%E6%80%A7)

- Node: v16.14.2
- pnpm: v8.12.1

```bash
# 全局安装pnpm@7
$ npm i -g pnpm@8
```

克隆仓库并安装依赖，项目基于 pnpm workspace：

```bash
$ <NAME_EMAIL>:JoyCoder/JoyCoder-VSCode.git
$ cd JoyCoder-VSCode && pnpm i
```

### 新增/删除包

1. 从 packages/\* 中选择拷贝合适的 package 作为新包的模版复制到 packages 目录下。
2. 在项目中安装新增的包

```bash
$ pnpm i
```

_包文件夹命名规范_：  
`plugin-base-`: 基础通用能力  
`plugin-custom-`: 定制能力，比如只适用于某些部门的非通用能力  
`vscode-`: VSCode 插件目录包  
其他: 一些公共代码目录，如 shared、web

_包名命名规范_：（package.json.name）  
VSCode 插件目录的包：自行决定，影响插件 ID，VSCode 的插件 ID 为：{publisher}.{name}  
其他：@joycoder/\*

删除包同理，删除包文件夹后执行`pnpm i`即可。

> packages 下的包并不会发布至 NPM，只作为本地代码模块化手段。

### 依赖管理

可以分为三种情况：

> 应该尽量把子包的 devDependencies 作为根目录的 devDependencies，从而安装在根目录。
> 如果版本遇到冲突，可以安装在子包内。

#### 1. 根目录

```bash
# 新增、删除dependencies
$ pnpm add -w <dependency>
$ pnpm remove -w <dependency>

# 新增的dependencies为workspace中的package时
$ pnpm add -w @joycoder/shared@"*"
# 结果： "@joycoder/shared": "workspace:*"

# 新增、删除devDependencies
$ pnpm add -wD <dependency>
$ pnpm remove -wD <dependency>
```

#### 2. 操作某个子包

```bash
# 为某个子包（如 @joycoder/shared）新增一个依赖
$ pnpm --filter @joycoder/shared add <dependency>

# 为某个子包（如 @joycoder/shared）删除一个依赖
$ pnpm --filter @joycoder/shared remove <dependency>
```

#### 3. 操作所有子包

```bash
# 新增
$ pnpm -r --filter=./packages/* add <dependency>

# 删除
$ pnpm -r --filter=./packages/* remove <dependency>
```

### 清理所有依赖

```bash
# 包括删除根目录的 node_modules 和所有 workspace 里的 node_modules
$ npm run clear-all
```

### 运行子包的 `npm script`

```bash
$ pnpm --filter <workspace> run <script-name>
```

## 开发与调试

使用 VSCode 打开 JoyCoder-VSCode 项目，然后通过 `F5` 呼起调试窗口即可，变更代码后在调试窗口按下 `CMD + R` 触发重载窗口以应用最新代码。（或使用 cmd+shift+p 唤起命令行面板，并执行其中的 reload window 命令）

更多内容见：  
[vscode 插件开发文档（中文）](https://liiked.github.io/VS-Code-Extension-Doc-ZH/#/)  
[vscode 插件开发文档（英文）](https://code.visualstudio.com/api)  
[vscode 官方 demo](https://github.com/microsoft/vscode-extension-samples)  
[snippet 生成工具](https://snippet-generator.app/)  
[vscode 仓库地址](https://github.com/microsoft/vscode)  
[stackoverflow](https://stackoverflow.com/questions/tagged/visual-studio-code)

## 文档管理

文档地址：http://joycoder.jd.com  
文档仓库地址：http://xingyun.jd.com/codingRoot/auto_book_projects_release/jdhopenplatform-JoyCoder/  
发布管理：http://abook.jd.com/book/myBook

将更改推送至远程后文档会自动发布，也可使用 coding 的在线编辑功能在线编辑。

## 版本管理

> 版本管理由俞世杰、吴茜、陈隆德三人轮流负责跟进，每人连续跟进 5 个版本后交接至下一人。版本发布暂无固定周期，根据特性和 bugfix 情况确定即可。

发版具体流程如下：

一、收集发版特性、跟进代码评审，确保代码健壮性和可维护性，确保各 PR 的质量和体验，如需转测可联系高莹莹；

二、所有 PR 合并至 master 后，在本地打包验证插件，安装并走查打包后的插件，确保各核心功能无误；

ps: 合并 PR 时须使用 squash、rebase 等方式约束 commit 数量以及质量，因为版本号以及 changelog 是通过 commit 信息自动生成的。

```bash
$ pnpm prepare-release-test
```

执行以上命令后在根目录生成的`joycoder-fe-*.*.*.vsix`文件即为核心插件安装包。其余插件在各自根目录下，如：packages/vscode-plugin-demo/plugin-demo-_._.\*.vsix

三、发布插件前需确认已登录并授权了 vsce，相关命令如下：

VSCE 登录前需先注册 Azure 开发者账号，查询[UserId](https://marketplace.visualstudio.com/)，找插件管理员[配置权限](https://marketplace.visualstudio.com/manage/publishers/joycoder)。

```bash
# 安装vsce
$ npm i -g @vscode/vsce
# 登录vsce，需输入管理员提供的publisher personal access token
$ vsce login JoyCoder
```

四、正式发布

```bash
# 此命令会执行打包发布操作，并通过 standard-version 来管理版本号、changelog 及 tag。
$ pnpm release
```

此命令主要会执行以下操作：

1. 校验当前分支是否为 master,若不是则报错；
2. `update-vsix-version`:根据 commit 信息更新 package.json 中版本号。（这一步是为了保证插件打包时版本号是最新的）；
3. `update-packages-version`:更新子包版本；
4. 编译打包，发布插件；
5. `update-info`:更新 CHANGELOG 和 tag 并提交；
6. 推送远程，发起 PR。

五、版本更新周知：

1. 根据 commit 信息整理出升级公告内容，并在 https://dripworks-17tni50x-pro.local-pf.jd.com/#/2700 中配置，不配置则表示静默更新，无更新通知。
2. 在插件交流群周知

其他发布相关文档：

- https://marketplace.visualstudio.com/manage/publishers/joycoder
- https://liiked.github.io/VS-Code-Extension-Doc-ZH/#/working-with-extensions/publish-extension
- https://github.com/microsoft/vscode-vsce

## 代码风格

- 请务必遵循本项目 ESlint 及 Prettier 配置的代码风格
- 异步编程请使用 async/await，推荐使用 `await-to-js`

## Pull Request 指南

1. commit 信息请遵循 [commit 规范](https://github.com/conventional-changelog/commitlint)；
2. 务必保证 pnpm prepare-release 能够编译成功；
3. 务必保证提交的代码遵循相关包中的 .eslintrc, .prettierrc, .stylelintrc 所规定的规范；

## Issue 报告

[issue](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/)

## 其他信息

插件名：JoyCoder
插件简称：JoyCoder
插件在应用市场 ID：JoyCoder.joycoder-fe
命令前缀：JoyCoder
插件配置项前缀：JoyCoder.config
自定义配置文件名称：joycoder.config.json
工作区默认 ID：joycoderfe

## 注意事项

- web 端注意不要引入 node 端的库，node 端常常会依赖 vscode api，容易导致构建失败。
